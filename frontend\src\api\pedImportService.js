// frontend/src/api/pedImportService.js

import axios from 'axios';

const API = axios.create({ 
  baseURL: API_BASE_URL,
  timeout: 30000, // Timeout maior para uploads
});

// Interceptor de erros para logging
API.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('❌ PED Import API Error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      responseData: error.response?.data
    });
    return Promise.reject(error.response?.data || error);
  }
);

/**
 * Faz o upload do arquivo Excel de dados PED.
 * @param {object} data - Contém { projetoId, dadosFile }.
 * @returns {Promise<object>} - A resposta do servidor.
 */
export async function uploadPedData({ projetoId, dadosFile }) {
    const formData = new FormData();
    formData.append('projeto_id', projetoId);
    formData.append('dados_file', dadosFile);

    try {
        const response = await API.post('/api/ped/import-dados', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error("Erro no upload do Excel PED:", error);
        throw error;
    }
}

/**
 * Busca dados de PED para validação antes da importação.
 * @param {number} projetoId - O ID do projeto.
 * @returns {Promise<object>} - Dados de validação.
 */
export async function validatePedProject(projetoId) {
    try {
        const response = await API.get(`/api/ped/validate/${projetoId}`);
        return response.data;
    } catch (error) {
        console.error("Erro ao validar projeto PED:", error);
        throw error;
    }
}

/**
 * Busca estatísticas de dados PED de um projeto.
 * @param {number} projetoId - O ID do projeto.
 * @returns {Promise<object>} - Estatísticas do projeto.
 */
export async function fetchPedStats(projetoId) {
    try {
        const response = await API.get(`/api/ped/stats/${projetoId}`);
        return response.data;
    } catch (error) {
        console.error("Erro ao buscar estatísticas PED:", error);
        throw error;
    }
}