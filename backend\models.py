# backend/models_completo.py - ESTRUTURA COMPLETA MULTI-PRODUTO

from app import db
from datetime import datetime
from sqlalchemy.dialects.postgresql import JSONB

# =====================================================
# 1. ESTRUTURA ORGANIZACIONAL
# =====================================================

class Empresa(db.Model):
    __tablename__ = 'empresa'
    
    id = db.Column(db.Integer, primary_key=True)
    nome_empresa = db.Column(db.String(200), nullable=False)
    codigo_empresa = db.Column(db.String(50), unique=True)
    gerencia = db.Column(db.String(200))
    endereco = db.Column(db.Text)
    telefone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    logo_url = db.Column(db.String(500))
    ativo = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    projetos = db.relationship('Projeto', backref='empresa', cascade='all, delete-orphan')
    usuarios = db.relationship('UsuarioEmpresa', backref='empresa', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Empresa {self.nome_empresa}>'

class Projeto(db.Model):
    __tablename__ = 'projeto'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    codigo_projeto = db.Column(db.String(50), nullable=False)
    nome_projeto = db.Column(db.String(200), nullable=False)
    descricao = db.Column(db.Text)
    cliente = db.Column(db.String(200))
    coordenador = db.Column(db.String(100))
    data_inicio = db.Column(db.Date)
    data_fim = db.Column(db.Date)
    status = db.Column(db.String(50), default='ativo')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos com todos os produtos
    peds = db.relationship('Ped', backref='projeto', cascade='all, delete-orphan')
    pontos_ccv = db.relationship('CCVPonto', backref='projeto', cascade='all, delete-orphan')
    pontos_ftp = db.relationship('FTPPonto', backref='projeto', cascade='all, delete-orphan')
    segmentos_tlp = db.relationship('TLPSegmento', backref='projeto', cascade='all, delete-orphan')
    zonas_pod = db.relationship('PODZona', backref='projeto', cascade='all, delete-orphan')
    grupos_veiculo = db.relationship('CCVGrupoVeiculo', backref='projeto', cascade='all, delete-orphan') #


    # CORREÇÃO: O relacionamento VIA deve ser através de VIAProjeto, não VIACenario
    projetos_via = db.relationship('VIAProjeto', backref='projeto', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('empresa_id', 'codigo_projeto'),)
    
    def __repr__(self):
        return f'<Projeto {self.codigo_projeto}>'

# =====================================================
# 2. PRODUTO PED - PONTOS DE EMBARQUE E DESEMBARQUE
# =====================================================

class Ped(db.Model):
    __tablename__ = 'ped'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo_ponto = db.Column(db.String(50))
    nome_ped = db.Column(db.String(255), nullable=False)
    
    # Localização
    ponto = db.Column(db.String(50))
    sh = db.Column(db.String(50))
    km = db.Column(db.String(100))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    sentido = db.Column(db.String(50))
    
    # Infraestrutura
    placa = db.Column(db.String(10))
    abrigo = db.Column(db.String(10))
    baia = db.Column(db.String(10))
    mancha_urbana = db.Column(db.String(10))
    
    # Relacionamentos
    dados_horarios = db.relationship('PedDadosHorarios', backref='ped', cascade='all, delete-orphan')
    dias = db.relationship('PedDia', backref='ped', cascade='all, delete-orphan')
    pontos_ftp_associados = db.relationship('FTPPonto', backref='ped_associado')
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo_ponto'),)
    
    def __repr__(self):
        return f'<PED {self.nome_ped}>'

class PedDia(db.Model):
    __tablename__ = 'ped_dia'
    
    id = db.Column(db.Integer, primary_key=True)
    ped_id = db.Column(db.Integer, db.ForeignKey('ped.id'), nullable=False)
    data_coleta = db.Column(db.Date, nullable=False)
    responsavel = db.Column(db.String(100))
    observacoes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    dados_horarios = db.relationship('PedDadosHorarios', backref='dia', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('ped_id', 'data_coleta'),)

class PedDadosHorarios(db.Model):
    __tablename__ = 'ped_dados_horarios'
    
    id = db.Column(db.Integer, primary_key=True)
    ped_id = db.Column(db.Integer, db.ForeignKey('ped.id'), nullable=False)
    dia_id = db.Column(db.Integer, db.ForeignKey('ped_dia.id'))
    
    # Dados de tempo
    hora = db.Column(db.String(8))
    intervalo = db.Column(db.Integer)
    
    # Dados operacionais
    onibus = db.Column(db.Integer)
    embarque = db.Column(db.Integer)
    desembarque = db.Column(db.Integer)
    total = db.Column(db.Integer)
    travessia_1_2 = db.Column(db.Integer)
    travessia_2_1 = db.Column(db.Integer)
    
    # Dados calculados
    qtemed = db.Column(db.Float)
    tempo_embdes = db.Column(db.Float)
    tempo_manobras = db.Column(db.Float)
    tempo_total = db.Column(db.Float)
    taxa_chegada = db.Column(db.Float)
    taxa_atendimento = db.Column(db.Float)
    r = db.Column(db.Float)
    
    # Indicadores para cenários c1-c5
    gs_c1 = db.Column(db.Float)
    gs_c2 = db.Column(db.Float)
    gs_c3 = db.Column(db.Float)
    gs_c4 = db.Column(db.Float)
    gs_c5 = db.Column(db.Float)
    
    p0_c1 = db.Column(db.Float)
    p0_c2 = db.Column(db.Float)
    p0_c3 = db.Column(db.Float)
    p0_c4 = db.Column(db.Float)
    p0_c5 = db.Column(db.Float)
    
    pf_c1 = db.Column(db.Float)
    pf_c2 = db.Column(db.Float)
    pf_c3 = db.Column(db.Float)
    pf_c4 = db.Column(db.Float)
    pf_c5 = db.Column(db.Float)

# =====================================================
# 3. PRODUTO CCV - CONTAGEM CLASSIFICADA DE VEÍCULOS
# =====================================================

class CCVCategoriaVeiculo(db.Model):
    __tablename__ = 'ccv_categoria_veiculo'
    
    id = db.Column(db.Integer, primary_key=True)
    codigo = db.Column(db.String(20), nullable=False, unique=True) # LEVE, PESADO, ESPECIAL
    nome = db.Column(db.String(100), nullable=False)
    
    # Relacionamento reverso
    tipos_veiculo = db.relationship('CCVTipoVeiculo', backref='categoria')
    def to_dict(self):
        return {'id': self.id, 'codigo': self.codigo, 'nome': self.nome}
# --- NOVO MODELO PARA GRUPOS DE PROJETO ---
class CCVGrupoVeiculo(db.Model):
    __tablename__ = 'ccv_grupo_veiculo'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo = db.Column(db.String(20), nullable=False)
    nome = db.Column(db.String(100), nullable=False)
    cor_grupo = db.Column(db.String(7))
    ativo = db.Column(db.Boolean, default=True)

    # Relacionamentos
    tipos_associados = db.relationship('CCVTipoGrupo', backref='grupo', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo'),)

    def to_dict(self, include_tipos=False):
        data = {
            'id': self.id, 'projeto_id': self.projeto_id,
            'codigo': self.codigo, 'nome': self.nome,
            'cor_grupo': self.cor_grupo, 'ativo': self.ativo
        }
        if include_tipos:
            data['tipos_associados'] = [assoc.to_dict() for assoc in self.tipos_associados]
        return data

# --- NOVO MODELO PARA RELACIONAMENTO N:N TIPO-GRUPO ---
class CCVTipoGrupo(db.Model):
    __tablename__ = 'ccv_tipo_grupo'
    
    id = db.Column(db.Integer, primary_key=True)
    tipo_veiculo_id = db.Column(db.Integer, db.ForeignKey('ccv_tipo_veiculo.id'), nullable=False)
    grupo_id = db.Column(db.Integer, db.ForeignKey('ccv_grupo_veiculo.id'), nullable=False)
    
    __table_args__ = (db.UniqueConstraint('tipo_veiculo_id', 'grupo_id'),)
    
    def to_dict(self):
        return {
            'tipo_veiculo_id': self.tipo_veiculo_id,
            'grupo_id': self.grupo_id,
            'tipo_veiculo': self.tipo_veiculo.to_dict() if self.tipo_veiculo else None
        }

# --- MODELO CCVTipoVeiculo ATUALIZADO ---
class CCVTipoVeiculo(db.Model):
    __tablename__ = 'ccv_tipo_veiculo'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # COLUNAS ADICIONADAS
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=True)  # Null = Global
    categoria_id = db.Column(db.Integer, db.ForeignKey('ccv_categoria_veiculo.id'), nullable=False)
    codigo_original = db.Column(db.String(50)) # Código como veio da planilha

    # Colunas existentes
    codigo = db.Column(db.String(20), nullable=False)
    nome = db.Column(db.String(100), nullable=False)
    descricao = db.Column(db.Text) 
    equivalente_auto = db.Column(db.Numeric(3, 2), default=1.0)
    cor_padrao = db.Column(db.String(7))
    ativo = db.Column(db.Boolean, default=True)
    ordem_exibicao = db.Column(db.Integer, default=0)
    
    # Relacionamentos
    contagens = db.relationship('CCVContagem', backref='tipo_veiculo', cascade='all, delete-orphan')
    grupos_associados = db.relationship('CCVTipoGrupo', backref='tipo_veiculo', cascade='all, delete-orphan')

    # Garante que o código seja único por projeto (tipos globais não tem projeto_id)
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo'),)

    def to_dict(self):
        return {
            'id': self.id, 'projeto_id': self.projeto_id,
            'categoria_id': self.categoria_id, 'codigo_original': self.codigo_original,
            'codigo': self.codigo, 'nome': self.nome,
            'is_global': self.projeto_id is None,
            'categoria_nome': self.categoria.nome if self.categoria else 'N/A'
        }

class CCVPonto(db.Model):
    __tablename__ = 'ccv_ponto'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo_ponto = db.Column(db.String(50), nullable=False)
    nome_ponto = db.Column(db.String(200), nullable=False)
    
    # Localização
    latitude = db.Column(db.Numeric(10, 8))
    longitude = db.Column(db.Numeric(11, 8))
    endereco = db.Column(db.Text)
    
    # Arquivos
    imagem_visada_url = db.Column(db.String(500))
    
    ativo = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    dias = db.relationship('CCVDia', backref='ponto', cascade='all, delete-orphan')
    movimentos = db.relationship('CCVMovimento', backref='ponto', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo_ponto'),)

class CCVDia(db.Model):
    __tablename__ = 'ccv_dia'
    
    id = db.Column(db.Integer, primary_key=True)
    ponto_id = db.Column(db.Integer, db.ForeignKey('ccv_ponto.id'), nullable=False)
    data_coleta = db.Column(db.Date, nullable=False)
    dia_semana = db.Column(db.Integer)
    responsavel = db.Column(db.String(100))
    observacoes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    contagens = db.relationship('CCVContagem', backref='dia', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('ponto_id', 'data_coleta'),)

class CCVMovimento(db.Model):
    __tablename__ = 'ccv_movimento'
    
    id = db.Column(db.Integer, primary_key=True)
    ponto_id = db.Column(db.Integer, db.ForeignKey('ccv_ponto.id'), nullable=False)
    codigo_movimento = db.Column(db.String(50), nullable=False)
    nome_movimento = db.Column(db.String(200))
    origem = db.Column(db.String(100))
    destino = db.Column(db.String(100))
    tipo_movimento = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    latitude = db.Column(db.Numeric(10, 8), nullable=True)
    longitude = db.Column(db.Numeric(11, 8), nullable=True)
    # Relacionamentos
    contagens = db.relationship('CCVContagem', backref='movimento', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('ponto_id', 'codigo_movimento'),)

class CCVContagem(db.Model):
    __tablename__ = 'ccv_contagem'
    
    id = db.Column(db.Integer, primary_key=True)
    dia_id = db.Column(db.Integer, db.ForeignKey('ccv_dia.id'), nullable=False)
    movimento_id = db.Column(db.Integer, db.ForeignKey('ccv_movimento.id'), nullable=False)
    tipo_veiculo_id = db.Column(db.Integer, db.ForeignKey('ccv_tipo_veiculo.id'), nullable=False)
    
    # Período
    hora = db.Column(db.Time, nullable=False)
    periodo_inicio = db.Column(db.Time, nullable=False)
    periodo_fim = db.Column(db.Time, nullable=False)
    
    # Dados
    quantidade = db.Column(db.Integer, nullable=False, default=0)
    observacoes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('dia_id', 'movimento_id', 'tipo_veiculo_id', 'hora'),)

# =====================================================
# 4. PRODUTO FTP - FLUXO DE TRAVESSIAS DE PEDESTRES
# =====================================================

class FTPPonto(db.Model):
    __tablename__ = 'ftp_ponto'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo_ponto = db.Column(db.String(50), nullable=False)
    nome_ponto = db.Column(db.String(200), nullable=False)
    
    # Localização
    latitude = db.Column(db.Numeric(10, 8))
    longitude = db.Column(db.Numeric(11, 8))
    endereco = db.Column(db.Text)
    
    # Características
    tipo_travessia = db.Column(db.String(100))
    ped_associado_id = db.Column(db.Integer, db.ForeignKey('ped.id'))
    
    # Arquivos
    imagem_visada_url = db.Column(db.String(500))
    
    ativo = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    dias = db.relationship('FTPDia', backref='ponto', cascade='all, delete-orphan')
    movimentos = db.relationship('FTPMovimento', backref='ponto', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo_ponto'),)

class FTPDia(db.Model):
    __tablename__ = 'ftp_dia'
    
    id = db.Column(db.Integer, primary_key=True)
    ponto_id = db.Column(db.Integer, db.ForeignKey('ftp_ponto.id'), nullable=False)
    data_coleta = db.Column(db.Date, nullable=False)
    responsavel = db.Column(db.String(100))
    observacoes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    contagens = db.relationship('FTPContagem', backref='dia', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('ponto_id', 'data_coleta'),)

class FTPMovimento(db.Model):
    __tablename__ = 'ftp_movimento'
    
    id = db.Column(db.Integer, primary_key=True)
    ponto_id = db.Column(db.Integer, db.ForeignKey('ftp_ponto.id'), nullable=False)
    codigo_movimento = db.Column(db.String(50), nullable=False)
    nome_movimento = db.Column(db.String(200), nullable=False)
    origem = db.Column(db.String(100))
    destino = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    contagens = db.relationship('FTPContagem', backref='movimento', cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('ponto_id', 'codigo_movimento'),)

class FTPTipoPedestre(db.Model):
    __tablename__ = 'ftp_tipo_pedestre'
    
    id = db.Column(db.Integer, primary_key=True)
    codigo = db.Column(db.String(20), nullable=False, unique=True)
    nome = db.Column(db.String(100), nullable=False)
    descricao = db.Column(db.Text)
    cor_padrao = db.Column(db.String(7))
    ativo = db.Column(db.Boolean, default=True)
    ordem_exibicao = db.Column(db.Integer, default=0)
    
    # Relacionamentos
    contagens = db.relationship('FTPContagem', backref='tipo_pedestre', cascade='all, delete-orphan')

class FTPContagem(db.Model):
    __tablename__ = 'ftp_contagem'
    
    id = db.Column(db.Integer, primary_key=True)
    dia_id = db.Column(db.Integer, db.ForeignKey('ftp_dia.id'), nullable=False)
    movimento_id = db.Column(db.Integer, db.ForeignKey('ftp_movimento.id'), nullable=False)
    tipo_pedestre_id = db.Column(db.Integer, db.ForeignKey('ftp_tipo_pedestre.id'), nullable=False)
    
    # Período
    hora = db.Column(db.Time, nullable=False)
    periodo_inicio = db.Column(db.Time, nullable=False)
    periodo_fim = db.Column(db.Time, nullable=False)
    
    # Dados
    quantidade = db.Column(db.Integer, nullable=False, default=0)
    observacoes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('dia_id', 'movimento_id', 'tipo_pedestre_id', 'hora'),)


# =====================================================
# 6. PRODUTO VIA - SISTEMA VIÁRIO (AIMSUN)
# =====================================================

class VIAProjeto(db.Model):
    __tablename__ = 'via_projeto'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    nome_rede = db.Column(db.String(200), nullable=False)
    descricao = db.Column(db.Text)
    fonte_dados = db.Column(db.String(100), default='Aimsun')
    versao_aimsun = db.Column(db.String(50))
    data_criacao = db.Column(db.DateTime, default=datetime.utcnow)
    data_atualizacao = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    ativo = db.Column(db.Boolean, default=True)
    
    # Configuração dos indicadores escolhidos pelo usuário
    indicadores_config = db.Column(JSONB)  # Estrutura: {atributo: {nome: str, ativo: bool, tipo: str}}
    
    # Relacionamentos
    nos = db.relationship('VIANo', backref='projeto_via', cascade='all, delete-orphan')
    trechos = db.relationship('VIATrecho', backref='projeto_via', cascade='all, delete-orphan')
    cenarios = db.relationship('VIACenario', backref='projeto_via', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<VIAProjeto {self.nome_rede}>'

class VIANo(db.Model):
    __tablename__ = 'via_no'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_via_id = db.Column(db.Integer, db.ForeignKey('via_projeto.id'), nullable=False)
    
    # Identificadores do Aimsun
    aimsun_id = db.Column(db.String(50))
    nome = db.Column(db.String(200))
    tipo = db.Column(db.String(50))  # intersection, entry, exit, etc.
    
    # Geometria
    latitude = db.Column(db.Numeric(10, 8))
    longitude = db.Column(db.Numeric(11, 8))
    altitude = db.Column(db.Numeric(8, 2))
    
    # Atributos do GeoJSON (flexível para diferentes versões do Aimsun)
    atributos_originais = db.Column(JSONB)
    
    # Configurações específicas
    controlado = db.Column(db.Boolean, default=False)  # semáforo, rotatória, etc.
    tipo_controle = db.Column(db.String(50))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<VIANo {self.aimsun_id}>'

class VIATrecho(db.Model):
    __tablename__ = 'via_trecho'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_via_id = db.Column(db.Integer, db.ForeignKey('via_projeto.id'), nullable=False)
    
    # Identificadores do Aimsun
    aimsun_id = db.Column(db.String(50))
    nome = db.Column(db.String(200))
    tipo = db.Column(db.String(50))  # section, connector, etc.
    
    # Conectividade
    no_origem_id = db.Column(db.Integer, db.ForeignKey('via_no.id'))
    no_destino_id = db.Column(db.Integer, db.ForeignKey('via_no.id'))
    
    # Geometria (LineString)
    geometria = db.Column(JSONB)  # Coordenadas da linha
    
    # Características físicas básicas
    comprimento = db.Column(db.Numeric(10, 2))  # metros
    largura = db.Column(db.Numeric(5, 2))  # metros
    numero_faixas = db.Column(db.Integer)
    velocidade_limite = db.Column(db.Numeric(5, 2))  # km/h
    capacidade = db.Column(db.Integer)  # veículos/hora
    
    # Atributos do GeoJSON (todos os atributos originais do Aimsun)
    atributos_originais = db.Column(JSONB)
    
    # Configurações
    sentido = db.Column(db.String(20))  # bidirecional, unidirecional
    tipo_via = db.Column(db.String(50))  # arterial, coletora, local, etc.
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    no_origem = db.relationship('VIANo', foreign_keys=[no_origem_id], backref='trechos_saida')
    no_destino = db.relationship('VIANo', foreign_keys=[no_destino_id], backref='trechos_entrada')
    dados_desempenho = db.relationship('VIADadosDesempenho', backref='trecho', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<VIATrecho {self.aimsun_id}>'

class VIACenario(db.Model):
    __tablename__ = 'via_cenario'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_via_id = db.Column(db.Integer, db.ForeignKey('via_projeto.id'), nullable=False)
    
    nome_cenario = db.Column(db.String(200), nullable=False)
    descricao = db.Column(db.Text)
    tipo_cenario = db.Column(db.String(50))  # atual, futuro, alternativo
    ano_referencia = db.Column(db.Integer)
    
    # Parâmetros de simulação
    tempo_simulacao = db.Column(db.Integer)  # minutos
    periodo_aquecimento = db.Column(db.Integer)  # minutos
    intervalo_dados = db.Column(db.Integer)  # segundos
    
    # Configurações da simulação
    config_simulacao = db.Column(JSONB)
    
    ativo = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    dados_desempenho = db.relationship('VIADadosDesempenho', backref='cenario', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<VIACenario {self.nome_cenario}>'

class VIADadosDesempenho(db.Model):
    __tablename__ = 'via_dados_desempenho'
    
    id = db.Column(db.Integer, primary_key=True)
    trecho_id = db.Column(db.Integer, db.ForeignKey('via_trecho.id'), nullable=False)
    cenario_id = db.Column(db.Integer, db.ForeignKey('via_cenario.id'), nullable=False)
    
    # Período dos dados
    data_coleta = db.Column(db.Date)
    hora_inicio = db.Column(db.Time)
    hora_fim = db.Column(db.Time)
    intervalo_minutos = db.Column(db.Integer, default=15)
    
    # Indicadores de desempenho (armazenados como JSON para flexibilidade)
    indicadores = db.Column(JSONB)  # Estrutura: {nome_indicador: valor}
    
    # Dados brutos opcionais
    dados_brutos = db.Column(JSONB)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        db.UniqueConstraint('trecho_id', 'cenario_id', 'data_coleta', 'hora_inicio'),
    )
    
    def __repr__(self):
        return f'<VIADadosDesempenho T{self.trecho_id} C{self.cenario_id}>'
    

# =====================================================
# 5. PRODUTOS FUTUROS - ESTRUTURAS BASE
# =====================================================

class TLPSegmento(db.Model):
    __tablename__ = 'tlp_segmento'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo_segmento = db.Column(db.String(50), nullable=False)
    nome_segmento = db.Column(db.String(200), nullable=False)
    
    # Localização (linha)
    inicio_latitude = db.Column(db.Numeric(10, 8))
    inicio_longitude = db.Column(db.Numeric(11, 8))
    fim_latitude = db.Column(db.Numeric(10, 8))
    fim_longitude = db.Column(db.Numeric(11, 8))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo_segmento'),)

class PODZona(db.Model):
    __tablename__ = 'pod_zona'
    
    id = db.Column(db.Integer, primary_key=True)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    codigo_zona = db.Column(db.String(50), nullable=False)
    nome_zona = db.Column(db.String(200), nullable=False)
    tipo_zona = db.Column(db.String(50))
    area_km2 = db.Column(db.Numeric(8, 2))
    populacao = db.Column(db.Integer)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('projeto_id', 'codigo_zona'),)


# =====================================================
# 6. SISTEMA DE USUÁRIOS E PERMISSÕES
# =====================================================

class Usuario(db.Model):
    __tablename__ = 'usuario'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(100), nullable=False, unique=True)
    senha_hash = db.Column(db.String(255), nullable=False)
    nome = db.Column(db.String(200), nullable=False)
    sobrenome = db.Column(db.String(200))
    telefone = db.Column(db.String(20))
    cargo = db.Column(db.String(100))
    
    # Configurações
    ativo = db.Column(db.Boolean, default=True)
    ultimo_login = db.Column(db.DateTime)
    timezone = db.Column(db.String(50), default='America/Sao_Paulo')
    idioma = db.Column(db.String(10), default='pt-BR')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    empresas = db.relationship('UsuarioEmpresa', backref='usuario', cascade='all, delete-orphan')
    projetos = db.relationship('UsuarioProjeto', backref='usuario', cascade='all, delete-orphan')
    atividades = db.relationship('AtividadeLog', backref='usuario')

class UsuarioEmpresa(db.Model):
    __tablename__ = 'usuario_empresa'
    
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    papel = db.Column(db.String(50), nullable=False)  # admin, gerente, analista, consultor, readonly
    ativo = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('usuario_id', 'empresa_id'),)

class UsuarioProjeto(db.Model):
    __tablename__ = 'usuario_projeto'
    
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'), nullable=False)
    permissao = db.Column(db.String(50), nullable=False)  # leitura, escrita, admin
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('usuario_id', 'projeto_id'),)

# =====================================================
# 7. AUDITORIA E LOGS
# =====================================================

class AtividadeLog(db.Model):
    __tablename__ = 'atividade_log'
    
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'))
    projeto_id = db.Column(db.Integer, db.ForeignKey('projeto.id'))
    
    acao = db.Column(db.String(100), nullable=False)  # criar, editar, deletar, exportar
    entidade = db.Column(db.String(100), nullable=False)  # ccv_ponto, ped, projeto
    entidade_id = db.Column(db.Integer)
    
    detalhes = db.Column(JSONB)  # dados específicos da ação
    ip_address = db.Column(db.String(45))  # IPv4 e IPv6
    user_agent = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# =====================================================
# 8. CONFIGURAÇÕES DO SISTEMA
# =====================================================

class Configuracao(db.Model):
    __tablename__ = 'configuracao'
    
    id = db.Column(db.Integer, primary_key=True)
    chave = db.Column(db.String(100), nullable=False, unique=True)
    valor = db.Column(db.Text)
    tipo = db.Column(db.String(50))  # string, number, boolean, json
    descricao = db.Column(db.Text)
    categoria = db.Column(db.String(100))
    editavel = db.Column(db.Boolean, default=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# =====================================================
# 9. VIEWS E CONSULTAS OTIMIZADAS (via SQLAlchemy)
# =====================================================

from sqlalchemy import func, case

class ProjetoResumo:
    """View virtual para resumo de projetos"""
    
    @staticmethod
    def get_resumo(empresa_id=None):
        query = db.session.query(
            Projeto.id,
            Projeto.codigo_projeto,
            Projeto.nome_projeto,
            Empresa.nome_empresa,
            Empresa.gerencia,
            Projeto.status,
            func.count(CCVPonto.id.distinct()).label('total_pontos_ccv'),
            func.count(Ped.id.distinct()).label('total_pontos_ped'),
            func.count(FTPPonto.id.distinct()).label('total_pontos_ftp'),
            Projeto.data_inicio,
            Projeto.data_fim
        ).join(Empresa)\
         .outerjoin(CCVPonto, (Projeto.id == CCVPonto.projeto_id) & (CCVPonto.ativo == True))\
         .outerjoin(Ped, Projeto.id == Ped.projeto_id)\
         .outerjoin(FTPPonto, (Projeto.id == FTPPonto.projeto_id) & (FTPPonto.ativo == True))
        
        if empresa_id:
            query = query.filter(Projeto.empresa_id == empresa_id)
            
        return query.group_by(
            Projeto.id, Projeto.codigo_projeto, Projeto.nome_projeto,
            Empresa.nome_empresa, Empresa.gerencia, Projeto.status,
            Projeto.data_inicio, Projeto.data_fim
        ).all()

class CCVResumoDiario:
    """View virtual para dados CCV agregados por dia"""
    
    @staticmethod
    def get_resumo_diario(projeto_id=None, data_inicio=None, data_fim=None):
        query = db.session.query(
            CCVPonto.id.label('ponto_id'),
            CCVPonto.codigo_ponto,
            CCVPonto.nome_ponto,
            CCVDia.data_coleta,
            CCVMovimento.nome_movimento,
            CCVTipoVeiculo.nome.label('tipo_veiculo'),
            func.sum(CCVContagem.quantidade).label('total_diario'),
            func.count(CCVContagem.id).label('periodos_coletados')
        ).join(CCVDia, CCVPonto.id == CCVDia.ponto_id)\
         .join(CCVMovimento, CCVPonto.id == CCVMovimento.ponto_id)\
         .join(CCVContagem, 
               (CCVDia.id == CCVContagem.dia_id) & 
               (CCVMovimento.id == CCVContagem.movimento_id))\
         .join(CCVTipoVeiculo, CCVContagem.tipo_veiculo_id == CCVTipoVeiculo.id)\
         .filter(CCVPonto.ativo == True, CCVTipoVeiculo.ativo == True)
        
        if projeto_id:
            query = query.filter(CCVPonto.projeto_id == projeto_id)
        if data_inicio:
            query = query.filter(CCVDia.data_coleta >= data_inicio)
        if data_fim:
            query = query.filter(CCVDia.data_coleta <= data_fim)
            
        return query.group_by(
            CCVPonto.id, CCVPonto.codigo_ponto, CCVPonto.nome_ponto,
            CCVDia.data_coleta, CCVMovimento.nome_movimento, CCVTipoVeiculo.nome
        ).all()

# =====================================================
# 10. MÉTODOS DE UTILIDADE E VALIDAÇÃO
# =====================================================

class ValidationMixin:
    """Mixin para validações comuns"""
    
    def validate_coordinates(self, latitude, longitude):
        """Valida coordenadas geográficas"""
        if latitude is not None and not (-90 <= latitude <= 90):
            raise ValueError("Latitude deve estar entre -90 e 90")
        if longitude is not None and not (-180 <= longitude <= 180):
            raise ValueError("Longitude deve estar entre -180 e 180")
    
    def validate_time_period(self, periodo_inicio, periodo_fim):
        """Valida período de tempo"""
        if periodo_inicio and periodo_fim and periodo_inicio >= periodo_fim:
            raise ValueError("Período de início deve ser anterior ao fim")

# Aplicar mixin às classes que precisam
CCVPonto.__bases__ = (db.Model, ValidationMixin)
FTPPonto.__bases__ = (db.Model, ValidationMixin)
Ped.__bases__ = (db.Model, ValidationMixin)

# =====================================================
# 11. ÍNDICES PARA PERFORMANCE (via SQLAlchemy)
# =====================================================

from sqlalchemy import Index

# Índices para CCV
Index('idx_ccv_ponto_projeto_ativo', CCVPonto.projeto_id, CCVPonto.ativo)
Index('idx_ccv_ponto_location', CCVPonto.latitude, CCVPonto.longitude)
Index('idx_ccv_dia_data', CCVDia.data_coleta)
Index('idx_ccv_contagem_hora', CCVContagem.hora)
Index('idx_ccv_contagem_lookup', CCVContagem.dia_id, CCVContagem.movimento_id, CCVContagem.tipo_veiculo_id)

# Índices para PED
Index('idx_ped_projeto_codigo', Ped.projeto_id, Ped.codigo_ponto)
Index('idx_ped_location', Ped.latitude, Ped.longitude)
Index('idx_ped_dados_horarios_ped_hora', PedDadosHorarios.ped_id, PedDadosHorarios.hora)

# Índices para FTP
Index('idx_ftp_ponto_projeto_ativo', FTPPonto.projeto_id, FTPPonto.ativo)
Index('idx_ftp_ped_associado', FTPPonto.ped_associado_id)

# Índices para Usuários e Logs
Index('idx_usuario_email_ativo', Usuario.email, Usuario.ativo)
Index('idx_usuario_empresa_ativo', UsuarioEmpresa.empresa_id, UsuarioEmpresa.ativo)
Index('idx_atividade_log_usuario_data', AtividadeLog.usuario_id, AtividadeLog.created_at)
Index('idx_atividade_log_entidade', AtividadeLog.entidade, AtividadeLog.entidade_id)

# =====================================================
# 12. TRIGGERS E FUNÇÕES ESPECIAIS
# =====================================================

@db.event.listens_for(Projeto, 'before_insert')
def gerar_codigo_projeto(mapper, connection, target):
    """Gera código de projeto automaticamente se não fornecido"""
    if not target.codigo_projeto:
        # Busca o próximo número sequencial
        last_project = db.session.query(Projeto)\
            .filter_by(empresa_id=target.empresa_id)\
            .order_by(Projeto.id.desc())\
            .first()
        
        if last_project and last_project.codigo_projeto:
            # Extrai número do último código (ex: FTR001 -> 1)
            try:
                last_num = int(last_project.codigo_projeto[-3:])
                next_num = last_num + 1
            except:
                next_num = 1
        else:
            next_num = 1
        
        # Gera novo código baseado na empresa
        empresa = db.session.get(Empresa, target.empresa_id)
        codigo_empresa = empresa.codigo_empresa if empresa else 'PROJ'
        target.codigo_projeto = f"{codigo_empresa}{next_num:03d}"

@db.event.listens_for(CCVPonto, 'before_insert')
@db.event.listens_for(FTPPonto, 'before_insert')
@db.event.listens_for(Ped, 'before_insert')
def validar_coordenadas(mapper, connection, target):
    """Valida coordenadas antes de inserir"""
    if hasattr(target, 'validate_coordinates'):
        target.validate_coordinates(target.latitude, target.longitude)

# =====================================================
# 13. MÉTODOS DE SERIALIZAÇÃO PARA API
# =====================================================

class SerializationMixin:
    """Mixin para serialização JSON"""
    
    def to_dict(self, include_relations=False):
        """Converte modelo para dicionário"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            # Conversão especial para tipos específicos
            if hasattr(value, 'isoformat'):  # datetime, date, time
                result[column.name] = value.isoformat()
            elif isinstance(value, decimal.Decimal):
                result[column.name] = float(value)
            else:
                result[column.name] = value
        
        # Incluir relacionamentos se solicitado
        if include_relations:
            for relationship in self.__mapper__.relationships:
                if hasattr(self, relationship.key):
                    related_obj = getattr(self, relationship.key)
                    if related_obj is not None:
                        if hasattr(related_obj, '__iter__') and not isinstance(related_obj, str):
                            # Lista de objetos
                            result[relationship.key] = [obj.to_dict() if hasattr(obj, 'to_dict') else str(obj) for obj in related_obj]
                        else:
                            # Objeto único
                            result[relationship.key] = related_obj.to_dict() if hasattr(related_obj, 'to_dict') else str(related_obj)
        
        return result

# Aplicar mixin de serialização a todos os modelos principais
for model in [Empresa, Projeto, Ped, CCVPonto, CCVDia, CCVMovimento, CCVContagem, 
              FTPPonto, FTPDia, FTPMovimento, FTPContagem, Usuario]:
    if SerializationMixin not in model.__bases__:
        model.__bases__ = model.__bases__ + (SerializationMixin,)