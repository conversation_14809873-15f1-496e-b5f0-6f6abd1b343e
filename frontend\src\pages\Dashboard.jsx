// frontend/src/pages/Dashboard.jsx - NAVEGAÇÃO COM ESTADO DO MAPA

import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { fetchAllPeds, testAPI } from "../api/pedService";
import Sidebar from "../components/Sidebar";
import Map from "../components/Map";
import Legend from "../components/Legend";

export default function Dashboard({ isSidebarVisible }) {
  // --- GERENCIAMENTO DE ESTADO CENTRALIZADO ---
  const [loading, setLoading] = useState(true);
  const [dados, setDados] = useState(null);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState(null);
  
  // Estados para todos os filtros da sidebar
  const [indicador, setIndicador] = useState("Passageiros");
  const [cenario, setCenario] = useState("1");
  const [modoIntervalo, setModoIntervalo] = useState("fixo");
  const [horarios, setHorarios] = useState([]);
  const [intervaloDeTempo, setIntervaloDeTempo] = useState([0]);
  const [tamanhoIcone, setTamanhoIcone] = useState(1.0);
  
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // --- FUNÇÃO PARA TENTAR CARREGAR DADOS ---
  const carregarDados = async () => {
    setLoading(true);
    setError(null);
    setDebugInfo(null);
    
    try {
      console.log('🔄 Iniciando carregamento de dados...');
      
      const data = await fetchAllPeds();
      
      console.log('📊 Dados recebidos no Dashboard:', {
        type: typeof data,
        keys: data ? Object.keys(data) : null,
        hasIndicadores: data && data.indicadores,
        hasPedsData: data && data.pedsData
      });
      
      // Processa os horários
      const horariosSet = new Set();
      let pedCount = 0;
      let horariosCount = 0;
      
      if (data.pedsData && typeof data.pedsData === 'object') {
        Object.values(data.pedsData).forEach(ped => {
          pedCount++;
          if (ped.dados_horarios && typeof ped.dados_horarios === 'object') {
            Object.keys(ped.dados_horarios).forEach(h => {
              horariosSet.add(h);
              horariosCount++;
            });
          }
        });
      }
      
      const horariosOrdenados = Array.from(horariosSet).sort();
      
      console.log('📈 Processamento concluído:', {
        pedsCount: pedCount,
        horariosUnicos: horariosOrdenados.length,
        totalHorarios: horariosCount,
        primeiroHorario: horariosOrdenados[0],
        ultimoHorario: horariosOrdenados[horariosOrdenados.length - 1]
      });
      
      // Define estados
      setDados(data);
      setHorarios(horariosOrdenados);
      setIntervaloDeTempo([0]);
      
      // Informações de debug para exibir ao usuário se necessário
      setDebugInfo({
        pedsCount: pedCount,
        horariosCount: horariosOrdenados.length,
        indicadoresCount: Object.keys(data.indicadores || {}).length
      });
      
      console.log('✅ Dashboard carregado com sucesso');
      
      // Verifica se há parâmetros de zoom na URL para debug
      const lat = searchParams.get('lat');
      const lng = searchParams.get('lng');
      const zoom = searchParams.get('zoom');
      
      if (lat && lng && zoom) {
        console.log(`🗺️ Parâmetros de mapa detectados na URL: lat=${lat}, lng=${lng}, zoom=${zoom}`);
      }
      
    } catch (err) {
      console.error('💥 Erro ao carregar dados no Dashboard:', err);
      
      let errorMessage = 'Erro desconhecido';
      let errorDetails = null;
      
      if (err.message) {
        errorMessage = err.message;
      }
      
      // Adiciona detalhes específicos baseado no tipo de erro
      if (err.message.includes('Network Error') || err.message.includes('timeout')) {
        errorMessage = 'Não foi possível conectar ao servidor backend. {`Verifique se o serviço está rodando em ${import.meta.env.VITE_API_URL || 'http://localhost:5000'}`}';
        errorDetails = 'Certifique-se de que o Docker está rodando e execute: docker-compose up';
      } else if (err.message.includes('404')) {
        errorMessage = 'Endpoint da API não encontrado. Verifique se a rota /api/dados está configurada corretamente.';
        errorDetails = 'O backend pode não estar implementando a rota correta';
      } else if (err.message.includes('500')) {
        errorMessage = 'Erro interno do servidor. Verifique os logs do backend.';
        errorDetails = 'Pode ser um problema com o banco de dados ou processamento dos dados';
      } else if (err.message.includes('indicadores') || err.message.includes('pedsData')) {
        errorMessage = 'A API retornou dados em formato incorreto.';
        errorDetails = err.message;
      }
      
      setError({ message: errorMessage, details: errorDetails });
    } finally {
      setLoading(false);
    }
  };

  // --- CARREGAMENTO INICIAL ---
  useEffect(() => {
    carregarDados();
  }, []);

  // --- EFEITO PARA AJUSTAR O ESTADO DO SLIDER AO MUDAR O MODO ---
  useEffect(() => {
    if (horarios.length === 0) return;
    
    if (modoIntervalo === 'fixo' && intervaloDeTempo.length > 1) {
      setIntervaloDeTempo([intervaloDeTempo[0]]);
    } else if (modoIntervalo === 'livre' && intervaloDeTempo.length === 1) {
      const endIndex = Math.min(intervaloDeTempo[0] + 3, horarios.length - 1);
      setIntervaloDeTempo([intervaloDeTempo[0], endIndex]);
    }
  }, [modoIntervalo, horarios.length]);

  // --- FUNÇÃO DE TESTE PARA DEBUG ---
  const testarAPI = async () => {
    try {
      console.log('🧪 Executando teste da API...');
      const result = await testAPI();
      console.log('🧪 Resultado do teste:', result);
      alert('✅ Teste da API bem-sucedido! Verifique o console para detalhes.');
    } catch (err) {
      console.error('🧪 Teste falhou:', err);
      alert(`❌ Teste da API falhou: ${err.message}`);
    }
  };

  // --- FUNÇÃO PARA LIDAR COM CLIQUE NO MARCADOR ---
  const handleMarkerClick = (nomePed, mapState = null) => {
    console.log(`🖱️ Clique no marcador: ${nomePed}`);
    
    if (mapState) {
      // Constrói a URL com os parâmetros do mapa
      const params = new URLSearchParams(mapState);
      const detailUrl = `/ped/${encodeURIComponent(nomePed)}?${params.toString()}`;
      
      console.log(`🧭 Navegando para: ${detailUrl}`);
      navigate(detailUrl);
    } else {
      // Fallback sem parâmetros de mapa
      navigate(`/ped/${encodeURIComponent(nomePed)}`);
    }
  };

  // --- LÓGICA DE RENDERIZAÇÃO ---
  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Carregando dados...</h2>
          <p className="text-gray-600">Conectando com o backend e processando dados dos PEDs</p>
          <div className="mt-4 text-sm text-gray-500">
            <p>Verificando: {import.meta.env.VITE_API_URL || 'http://localhost:5000'}/api/dados</p>
          </div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg border border-red-200 max-w-2xl">
          <i className="fas fa-exclamation-triangle text-red-500 text-5xl mb-4"></i>
          <h2 className="text-2xl font-bold text-red-800 mb-4">Erro ao Carregar Dados</h2>
          <p className="text-red-600 mb-4 text-lg">{error.message}</p>
          
          {error.details && (
            <div className="bg-red-50 p-4 rounded-lg mb-4 text-left">
              <h3 className="font-semibold text-red-800 mb-2">Detalhes técnicos:</h3>
              <p className="text-red-700 text-sm font-mono">{error.details}</p>
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button 
              onClick={carregarDados}
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-semibold"
            >
              <i className="fas fa-redo mr-2"></i>
              Tentar Novamente
            </button>
            <button 
              onClick={testarAPI}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              <i className="fas fa-flask mr-2"></i>
              Testar API
            </button>
          </div>
          
          <div className="mt-6 text-sm text-gray-600">
            <details className="text-left">
              <summary className="cursor-pointer font-semibold hover:text-gray-800">
                Passos para solução de problemas
              </summary>
              <div className="mt-3 space-y-2 text-sm">
                <p><strong>1.</strong> Verifique se o Docker está rodando</p>
                <p><strong>2.</strong> Execute: <code className="bg-gray-100 px-2 py-1 rounded">docker-compose up</code></p>
                <p><strong>3.</strong> Acesse: <a href="{import.meta.env.VITE_API_URL || 'http://localhost:5000'}" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">{import.meta.env.VITE_API_URL || 'http://localhost:5000'}</a></p>
                <p><strong>4.</strong> Verifique os logs do container backend</p>
                <p><strong>5.</strong> Abra o console do navegador (F12) para mais detalhes</p>
              </div>
            </details>
          </div>
        </div>
      </div>
    );
  }
  
  if (!dados) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center p-8">
          <i className="fas fa-database text-gray-400 text-5xl mb-4"></i>
          <h2 className="text-xl font-semibold text-gray-600 mb-2">Nenhum dado disponível</h2>
          <button 
            onClick={carregarDados}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Recarregar
          </button>
        </div>
      </div>
    );
  }

  // --- DADOS CARREGADOS COM SUCESSO ---
  // Indicadores que não precisam de cenário
  const showCenario = indicador && !["Placa", "Abrigo", "Baia", "Mancha Urbana", "SH", "Passageiros", "Total Travessias", "Onibus"].includes(indicador);

  return (
    <> 
      {isSidebarVisible && (
        <Sidebar
          indicadores={dados.indicadores}
          indicadorSelecionado={indicador}
          setIndicadorSelecionado={setIndicador}
          cenario={cenario}
          setCenario={setCenario}
          mostrarCenario={showCenario}
          modoIntervalo={modoIntervalo}
          setModoIntervalo={setModoIntervalo}
          horarios={horarios}
          intervaloDeTempo={intervaloDeTempo}
          setIntervaloDeTempo={setIntervaloDeTempo}
          tamanhoIcone={tamanhoIcone}
          setTamanhoIcone={setTamanhoIcone}
          data={dados}
        />
      )}
      
      <main className="flex-1 relative">
        <Map
          data={dados}
          indicator={indicador}
          cenario={cenario}
          onMarkerClick={handleMarkerClick}
          horarios={horarios}
          intervaloDeTempo={intervaloDeTempo}
          modoIntervalo={modoIntervalo}
          tamanhoIcone={tamanhoIcone}
        />
        <Legend indicator={indicador} data={dados} />
      </main>
    </>
  );
}