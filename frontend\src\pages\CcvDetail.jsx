// frontend/src/pages/CcvDetail.jsx

import { useState, useEffect, useMemo } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { fetchCcvPointDetails, fetchGroupedCCVData } from '../api/ccvImportService';
import { Chart as ChartJS, BarElement, LineElement, CategoryScale, LinearScale, PointElement, Legend, Title, Tooltip, ArcElement } from 'chart.js';
import { Chart, Bar, Pie } from 'react-chartjs-2';
import { MapContainer, TileLayer, Marker, Popup, LayersControl } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Ícone para pontos principais (mesmo estilo do MapCCV) - SEM EMOJI
const createCCVIcon = (cor = '#2563eb', tamanho = 1, valor = null) => {
  const size = 30 * tamanho;
  const html = `<div style="width: ${size}px; height: ${size}px; background-color: ${cor}; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: ${Math.max(10, size * 0.3)}px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8); line-height: 1; font-family: Arial, sans-serif;">${valor !== null ? String(valor) : ''}</div>`;
  return L.divIcon({ 
    html, 
    className: 'ccv-marker-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
};

// Ícone para aproximações (mesmo estilo do MapCCV)
const createAproximacaoIcon = (codigo) => {
  const size = 22;
  const html = `<div style="width: ${size}px; height: ${size}px; background-color: #ff7800; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 5px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 11px; font-family: Arial, sans-serif;">${codigo}</div>`;
  return L.divIcon({ 
    html, 
    className: 'ccv-aproximacao-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
};

ChartJS.register(BarElement, LineElement, ArcElement, CategoryScale, LinearScale, PointElement, Legend, Title, Tooltip);

export default function CcvDetail() {
    const { pontoName } = useParams();
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const projetoId = searchParams.get('projeto_id');
    
    const [dados, setDados] = useState(null);
    const [aproximacoes, setAproximacoes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (pontoName && projetoId) {
            carregarDetalhes();
        }
    }, [pontoName, projetoId]);

    const carregarDetalhes = async () => {
        try {
            setLoading(true);
            // Buscar detalhes do ponto
            const data = await fetchCcvPointDetails(pontoName, projetoId);
            setDados(data);
            
            // Buscar dados agrupados para obter aproximações com coordenadas
            const dadosAgrupados = await fetchGroupedCCVData(projetoId);
            
            // Tentar várias formas de encontrar o ponto
            let pontoComAproximacoes = null;
            
            if (dadosAgrupados?.ccvData) {
                // 1. Buscar por nome exato
                pontoComAproximacoes = dadosAgrupados.ccvData[pontoName];
                
                // 2. Se não encontrou, buscar por código
                if (!pontoComAproximacoes) {
                    pontoComAproximacoes = Object.values(dadosAgrupados.ccvData).find(ponto => 
                        ponto.Codigo === pontoName || ponto['Nome do Ponto'] === pontoName
                    );
                }
            }
            
            if (pontoComAproximacoes?.aproximacoes) {
                setAproximacoes(pontoComAproximacoes.aproximacoes);
            } else {
                setAproximacoes([]);
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const analises = useMemo(() => {
        if (!dados?.dados_horarios || !dados?.tipos_veiculos) return null;

        const dadosHorarios = Object.values(dados.dados_horarios);
        
        const equivalentesAuto = {};
        const tipoInfo = {};
        dados.tipos_veiculos.forEach(tipo => {
            equivalentesAuto[tipo.codigo] = tipo.equivalente_auto || 1.0;
            tipoInfo[tipo.codigo] = { cor: tipo.cor, nome: tipo.nome, categoria: tipo.categoria };
        });

        const horariosUnicos = [...new Set(dadosHorarios.map(r => r.HORA))].sort();
        
        let volumeTotal = 0;
        const volumesPorTipo = {};
        const volumesPorMovimento = {};
        const uvpPorMovimento = {};
        const volumesPorTipoEIntervalo = {};

        dadosHorarios.forEach(registro => {
            const hora = registro.HORA;
            if (!volumesPorTipoEIntervalo[hora]) volumesPorTipoEIntervalo[hora] = {};

            Object.entries(registro).forEach(([chave, valor]) => {
                if (chave.includes('_') && !chave.startsWith('GRUPO_')) {
                    const [tipo, ...movimentoParts] = chave.split('_');
                    const movimento = movimentoParts.join('_');
                    const volume = Number(valor) || 0;
                    
                    volumeTotal += volume;
                    volumesPorTipo[tipo] = (volumesPorTipo[tipo] || 0) + volume;
                    volumesPorMovimento[movimento] = (volumesPorMovimento[movimento] || 0) + volume;
                    volumesPorTipoEIntervalo[hora][tipo] = (volumesPorTipoEIntervalo[hora][tipo] || 0) + volume;

                    const uvp = volume * (equivalentesAuto[tipo] || 1.0);
                    uvpPorMovimento[movimento] = (uvpPorMovimento[movimento] || 0) + uvp;
                }
            });
        });

        const volumesMoveisPorTipo = {};
        const uvpMovelPorHora = [];
        const intervalosCompletos = horariosUnicos.map(h => volumesPorTipoEIntervalo[h] || {});

        for (let i = 0; i < horariosUnicos.length; i++) {
            const window = intervalosCompletos.slice(Math.max(0, i - 3), i + 1);
            const volumesSomaMovel = {};
            let uvpSomaMovel = 0;
            window.forEach(intervalo => {
                Object.entries(intervalo).forEach(([tipo, volume]) => {
                    volumesSomaMovel[tipo] = (volumesSomaMovel[tipo] || 0) + volume;
                    uvpSomaMovel += volume * (equivalentesAuto[tipo] || 1.0);
                });
            });
            volumesMoveisPorTipo[horariosUnicos[i]] = volumesSomaMovel;
            uvpMovelPorHora.push(Math.round(uvpSomaMovel));
        }
        
        const movimentosUnicos = Object.keys(volumesPorMovimento).sort();
        const tiposUnicos = Object.keys(volumesPorTipo).sort();

        return {
            volumeTotal, volumesPorTipo, volumesPorMovimento,
            horariosUnicos, tipoInfo, volumesMoveisPorTipo,
            uvpMovelPorHora, movimentosUnicos, tiposUnicos,
            dadosHorarios, // Passando os dados brutos para a nova tabela
            uvpPorMovimento
        };
    }, [dados]);

    if (loading) return <div className="p-6">Carregando…</div>;
    if (error) return <div className="p-6 text-red-600">Erro: {error}</div>;
    if (!analises) return <div className="p-6">Sem dados disponíveis para este ponto.</div>;

    const totalTipos = Object.keys(analises.volumesPorTipo).length;
    const totalMovimentos = Object.keys(analises.volumesPorMovimento).length;

    return (
        <div className="min-h-screen bg-gray-100 font-sans">
            <header className="bg-[#111827] text-white h-16 flex items-center px-6 shadow-lg z-30 flex-shrink-0 justify-between sticky top-0">
                <div className="flex items-center">
                    <img src="/images/logo1.png" alt="Logótipo 1" className="h-9" onError={(e) => (e.currentTarget.style.display = 'none')} />
                </div>
                <h1 className="text-xl font-semibold text-white absolute left-1/2 -translate-x-1/2 whitespace-nowrap">
                    Análise de Contagem Classificada de Veículos
                </h1>
                <div className="flex items-center">
                    <img src="/images/logo2.svg" alt="Logótipo 2" className="h-10" onError={(e) => (e.currentTarget.style.display = 'none')} />
                </div>
            </header>

            <main className="w-full">
                <div className="container mx-auto p-6 space-y-8">
                    {/* Seções */}
                    <div className="flex justify-between items-center">
                        <h1 className="text-3xl font-bold text-gray-800">{pontoName}</h1>
                        <div className="flex items-center gap-4">
                            <button onClick={() => window.print()} className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"><i className="fas fa-file-pdf"></i>Imprimir Relatório</button>
                            <button onClick={() => navigate('/ccv')} className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"><i className="fas fa-arrow-left"></i>Voltar ao Dashboard</button>
                        </div>
                    </div>

                    <section>
                        <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl"><h2 className="text-xl font-bold">1. Informações Básicas</h2></div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 bg-white p-6 rounded-b-2xl shadow-md items-center">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 h-full">
                                <h3 className="text-lg font-bold text-gray-800 mb-3">Informações do Ponto</h3>
                                <div className="space-y-2 text-gray-700">
                                    <div><span className="font-semibold">Código:</span> {pontoName}</div>
                                    <div><span className="font-semibold">Localização:</span> {dados?.ponto?.endereco || 'N/A'}</div>
                                    <div><span className="font-semibold">Latitude:</span> {dados?.ponto?.coordenadas?.latitude || 'N/A'}</div>
                                    <div><span className="font-semibold">Longitude:</span> {dados?.ponto?.coordenadas?.longitude || 'N/A'}</div>
                                </div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 h-full">
                                <h3 className="text-lg font-bold text-gray-800 mb-3">Resumo da Coleta</h3>
                                <div className="space-y-2 text-gray-700">
                                    <div><span className="font-semibold">Total de Períodos:</span> {analises.horariosUnicos.length}</div>
                                    <div><span className="font-semibold">Tipos de Veículos:</span> {totalTipos}</div>
                                    <div><span className="font-semibold">Movimentos:</span> {totalMovimentos}</div>
                                </div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200"><CcvPointMapImage dados={dados} aproximacoes={aproximacoes} /></div>
                        </div>
                    </section>

                    <section>
                        <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl"><h2 className="text-xl font-bold">2. Análise de Dados de Contagem (Hora Móvel)</h2></div>
                        <div className="bg-white p-6 rounded-b-2xl shadow-md space-y-8">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200"><ChartVolumeMovelEmpilhado analises={analises} /></div>
                                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200"><ChartUVPMovel analises={analises} /></div>
                            </div>
                            <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200"><ChartMovimentos analises={analises} /></div>
                        </div>
                    </section>

                    <section>
                        <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl"><h2 className="text-xl font-bold">3. Composição Veicular (Volume Total)</h2></div>
                        <div className="bg-white p-6 rounded-b-2xl shadow-md">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
                                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 h-full"><ChartParticipacaoModal analises={analises} /></div>
                                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 h-full"><ChartComposicaoTipoVeiculo analises={analises} /></div>
                            </div>
                        </div>
                    </section>
                    
                    <section>
                        <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl"><h2 className="text-xl font-bold">4. Tabulação de Movimentos por Tipo de Veículo</h2></div>
                        <div className="bg-white p-6 rounded-b-2xl shadow-md">
                           <TabelaMovimentos analises={analises} pontoName={pontoName} />
                        </div>
                    </section>
                </div>
            </main>
        </div>
    );
}

// Subcomponentes
function StatCard({ icon, color, label, value }) {
    return (
        <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 flex items-center">
            <i className={`fa-solid ${icon} text-4xl ${color} mr-4`} />
            <div><p className="text-gray-500 text-sm">{label}</p><p className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</p></div>
        </div>
    );
}

function CcvPointMapImage({ dados, aproximacoes = [] }) {
    const pontoLat = dados?.ponto?.coordenadas?.latitude;
    const pontoLng = dados?.ponto?.coordenadas?.longitude;
    
    if (!pontoLat || !pontoLng) {
        return (
            <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                    <i className="fas fa-map-marker-alt text-3xl mb-2"></i>
                    <p>Coordenadas não disponíveis</p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full h-full rounded-lg overflow-hidden">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Localização e Aproximações</h3>
            <div style={{ height: '300px', width: '100%' }}>
                <MapContainer
                    center={[pontoLat, pontoLng]}
                    zoom={17}
                    style={{ height: '100%', width: '100%' }}
                    scrollWheelZoom={true}
                    zoomControl={false}
                >
                    {/* Controle de camadas */}
                    <LayersControl position="topright">
                        <LayersControl.BaseLayer checked name="Ruas">
                            <TileLayer
                                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                                attribution='&copy; OpenStreetMap'
                                maxZoom={20}
                            />
                        </LayersControl.BaseLayer>
                        <LayersControl.BaseLayer name="Satélite">
                            <TileLayer
                                url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                                attribution='&copy; Esri'
                                maxZoom={20}
                            />
                        </LayersControl.BaseLayer>
                    </LayersControl>
                    
                    {/* Controles de zoom customizados */}
                    <div className="leaflet-top leaflet-left" style={{ marginTop: '10px', marginLeft: '10px' }}>
                        <div className="leaflet-control-zoom leaflet-bar leaflet-control">
                            <a className="leaflet-control-zoom-in" href="#" title="Zoom in" role="button" aria-label="Zoom in">+</a>
                            <a className="leaflet-control-zoom-out" href="#" title="Zoom out" role="button" aria-label="Zoom out">−</a>
                        </div>
                    </div>
                    
                    {/* Marcador do ponto principal - SEM POPUP, mesmo estilo do MapCCV */}
                    <Marker 
                        position={[pontoLat, pontoLng]}
                        icon={createCCVIcon('#2563eb', 1.2, null)}
                    />

                    {/* Marcadores das aproximações reais - SEM POPUP, mesmo estilo do MapCCV */}
                    {aproximacoes.filter(aprox => aprox.latitude && aprox.longitude).map((aprox, idx) => (
                        <Marker
                            key={`aprox-${idx}`}
                            position={[aprox.latitude, aprox.longitude]}
                            icon={createAproximacaoIcon(aprox.codigo)}
                        />
                    ))}
                </MapContainer>
            </div>
            
            {/* Informações resumidas */}
            <div className="mt-3 text-xs text-gray-600 space-y-1">
                <div className="flex justify-between">
                    <span><strong>Ponto:</strong> {dados.ponto.codigo}</span>
                    <span><strong>Aproximações:</strong> {aproximacoes.length}</span>
                </div>
                {dados.ponto.endereco && (
                    <div><strong>Endereço:</strong> {dados.ponto.endereco}</div>
                )}
                <div className="flex justify-between">
                    <span><strong>Coordenadas:</strong> {pontoLat.toFixed(6)}, {pontoLng.toFixed(6)}</span>
                </div>
            </div>
        </div>
    );
}

function ChartVolumeMovelEmpilhado({ analises }) {
    const tiposDeVeiculo = Object.keys(analises.volumesPorTipo);
    const datasets = tiposDeVeiculo.map(tipo => ({
        label: tipo,
        data: analises.horariosUnicos.map(h => analises.volumesMoveisPorTipo[h]?.[tipo] || 0),
        backgroundColor: analises.tipoInfo[tipo]?.cor || '#cccccc',
    }));
    const data = { labels: analises.horariosUnicos, datasets };
    const options = { responsive: true, maintainAspectRatio: false, scales: { x: { stacked: true }, y: { stacked: true, beginAtZero: true, title: { display: true, text: 'Volume de Veículos (Hora Móvel)' } } }, plugins: { title: { display: true, text: 'Volume por Tipo (Hora Móvel)', font: { size: 16 } }, legend: { position: 'bottom' } } };
    return <div style={{ height: '300px' }}><Bar data={data} options={options} /></div>;
}

function ChartUVPMovel({ analises }) {
    const uvpPorHoraMovel = analises.uvpMovelPorHora;
    const findPeak = (startHour, endHour) => {
        let peakValue = -1, peakIndex = -1;
        analises.horariosUnicos.forEach((hora, index) => {
            const h = parseInt(hora.split(':')[0]);
            if (h >= startHour && h < endHour && uvpPorHoraMovel[index] > peakValue) {
                peakValue = uvpPorHoraMovel[index]; peakIndex = index;
            }
        });
        return { peakValue, peakIndex };
    };
    const morningPeak = findPeak(6, 12);
    const afternoonPeak = findPeak(12, 20);
    const peakData = analises.horariosUnicos.map((_, index) => (index === morningPeak.peakIndex || index === afternoonPeak.peakIndex) ? uvpPorHoraMovel[index] : null);
    const data = { labels: analises.horariosUnicos, datasets: [{ type: 'line', label: 'UVP (Hora Móvel)', data: uvpPorHoraMovel, borderColor: 'black', borderWidth: 2, fill: false, pointRadius: 1, tension: 0.4 }, { type: 'scatter', label: 'Picos', data: peakData, backgroundColor: 'grey', radius: 5 }] };
    const options = { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true, title: { display: true, text: 'Volume (UVP)' } } }, plugins: { title: { display: true, text: 'Unidade de Veículo Padrão (UVP) - Hora Móvel', font: { size: 16 } }, legend: { display: false } } };
    return <div style={{ height: '300px' }}><Chart type='bar' data={data} options={options} /></div>;
}

function ChartMovimentos({ analises }) {
    const movimentosOrdenados = Object.entries(analises.uvpPorMovimento) // **ALTERADO**
        .sort(([, a], [, b]) => b - a).slice(0, 10);
    
    const data = { 
        labels: movimentosOrdenados.map(([mov]) => mov), 
        datasets: [{ 
            label: 'UVP por Movimento', // **ALTERADO**
            data: movimentosOrdenados.map(([, uvp]) => uvp.toFixed(0)), // **ALTERADO**
            backgroundColor: 'rgba(75, 192, 192, 0.6)' 
        }] 
    };
    
    const options = { 
        responsive: true, 
        maintainAspectRatio: false, 
        indexAxis: 'y', 
        scales: { 
            x: { 
                beginAtZero: true, 
                title: { 
                    display: true, 
                    text: 'UVP Total' // **ALTERADO**
                } 
            } 
        }, 
        plugins: { 
            title: { 
                display: true, 
                text: 'Principais Movimentos (UVP Total)', // **ALTERADO**
                font: { size: 16 } 
            }, 
            legend: { display: false } 
        } 
    };
    
    return <div style={{ height: '400px' }}><Bar data={data} options={options} /></div>;
}

function ChartParticipacaoModal({ analises }) {
    // **ALTERADO: Define as categorias, separando Cam Leve e Cam Pesado**
    const volumesPorCategoria = { Carro: 0, Moto: 0, 'Cam Leve': 0, 'Cam Pesado': 0, Ônibus: 0, Outros: 0 };
    const categoriaMap = { 
        'Carro': 'Carro', 
        'Moto': 'Moto', 
        'Onibus': 'Ônibus', 
        'Cam Leve': 'Cam Leve', 
        'Cam Pesado': 'Cam Pesado' 
    };

    Object.entries(analises.volumesPorTipo).forEach(([tipoCodigo, volume]) => {
        const categoriaNome = analises.tipoInfo[tipoCodigo]?.categoria;
        const categoriaFinal = categoriaMap[categoriaNome] || 'Outros';
        volumesPorCategoria[categoriaFinal] += volume;
    });

    const data = {
        labels: Object.keys(volumesPorCategoria).map(k => `${k} (${((volumesPorCategoria[k] / analises.volumeTotal) * 100).toFixed(1)}%)`),
        datasets: [{ 
            data: Object.values(volumesPorCategoria), 
            // **ALTERADO: Novas cores para acomodar as categorias**
            backgroundColor: ['#007bff', '#dc3545', '#ffc107', '#fd7e14', '#6f42c1', '#6c757d'] 
        }]
    };
    const options = { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: 'Participação Modal por Categoria (Volume Total)', font: { size: 16 } }, legend: { position: 'right' } } };
    return <div style={{ height: '300px' }}><Pie data={data} options={options} /></div>;
}

function ChartComposicaoTipoVeiculo({ analises }) {
    const tiposOrdenados = Object.entries(analises.volumesPorTipo)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 8);

    // **NOVO: Calcular percentuais**
    const data = { 
        labels: tiposOrdenados.map(([tipo]) => tipo), 
        datasets: [{ 
            label: '% do Volume Total', 
            data: tiposOrdenados.map(([, vol]) => ((vol / analises.volumeTotal) * 100).toFixed(1)), // **ALTERADO**
            backgroundColor: 'rgba(153, 102, 255, 0.6)' 
        }] 
    };
    
    const options = { 
        responsive: true, 
        maintainAspectRatio: false, 
        scales: { 
            y: { 
                beginAtZero: true, 
                title: { 
                    display: true, 
                    text: 'Percentual (%)' // **ALTERADO**
                } 
            } 
        }, 
        plugins: { 
            title: { 
                display: true, 
                text: 'Composição Percentual por Tipo de Veículo', // **ALTERADO**
                font: { size: 16 } 
            }, 
            legend: { display: false },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return `${context.dataset.label}: ${context.parsed.y}%`;
                    }
                }
            }
        } 
    };
    
    return <div style={{ height: '300px' }}><Bar data={data} options={options} /></div>;
}

function TabelaMovimentos({ analises, pontoName }) {
    const { dadosHorarios, movimentosUnicos, tiposUnicos } = analises;

    const copyTableAsCSV = () => {
        const headers = ['Ponto', 'Data', 'Hora', ...movimentosUnicos.flatMap(mov => tiposUnicos.map(tipo => `Mov ${mov} (${tipo})`))];
        let csvContent = headers.join(',') + '\r\n';

        dadosHorarios.forEach(row => {
            const values = [
                pontoName,
                row.Data,
                row.HORA,
                ...movimentosUnicos.flatMap(mov => tiposUnicos.map(tipo => row[`${tipo}_${mov}`] || 0))
            ];
            csvContent += values.join(',') + '\r\n';
        });

        navigator.clipboard.writeText(csvContent).then(() => {
            const button = document.getElementById('copy-csv-mov-btn');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Copiado!';
            button.classList.add('bg-green-600', 'hover:bg-green-700');
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-600', 'hover:bg-green-700');
            }, 2000);
        }).catch(() => alert('Erro ao copiar dados.'));
    };

    return (
        <div>
            <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-gray-600">Tabela de volume de veículos por tipo, movimento e intervalo de 15 minutos.</p>
                <button id="copy-csv-mov-btn" onClick={copyTableAsCSV} className="bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2">
                    <i className="fas fa-copy"></i>Copiar CSV
                </button>
            </div>
            <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="overflow-x-auto" style={{ maxHeight: '600px' }}>
                    <table className="w-full text-sm text-left text-gray-500">
                        <thead className="text-xs text-gray-700 uppercase bg-gray-200 sticky top-0 z-10">
                            <tr>
                                <th rowSpan="2" className="px-4 py-3 align-middle border-r border-b border-gray-300">Ponto</th>
                                <th rowSpan="2" className="px-4 py-3 align-middle border-r border-b border-gray-300">Data</th>
                                <th rowSpan="2" className="px-4 py-3 align-middle border-r border-b border-gray-300">Hora</th>
                                {movimentosUnicos.map(mov => (
                                    <th key={mov} colSpan={tiposUnicos.length} className="px-4 py-2 text-center border-r border-b border-gray-300">{`Movimento (${mov})`}</th>
                                ))}
                            </tr>
                            <tr>
                                {movimentosUnicos.flatMap(mov => 
                                    tiposUnicos.map(tipo => (
                                        <th key={`${mov}-${tipo}`} className="px-2 py-2 font-medium text-center border-r border-gray-300 bg-gray-100">{tipo}</th>
                                    ))
                                )}
                            </tr>
                        </thead>
                        <tbody>
                            {dadosHorarios.map((row, index) => (
                                <tr key={index} className="bg-white border-b hover:bg-blue-50 transition-colors">
                                    <td className="px-4 py-2 font-medium text-gray-900 border-r">{pontoName}</td>
                                    <td className="px-4 py-2 border-r">{new Date(row.Data).toLocaleDateString('pt-BR')}</td>
                                    <td className="px-4 py-2 border-r">{row.HORA}</td>
                                    {movimentosUnicos.flatMap(mov => 
                                        tiposUnicos.map(tipo => (
                                            <td key={`${mov}-${tipo}`} className="px-2 py-2 text-center border-r">{row[`${tipo}_${mov}`] || 0}</td>
                                        ))
                                    )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}