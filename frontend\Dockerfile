# frontend/Dockerfile

# Usar uma imagem oficial do Node.js como base
FROM node:20-alpine

# Definir o diretório de trabalho dentro do container
WORKDIR /app

# Copiar os arquivos de dependência primeiro para aproveitar o cache do Docker
COPY package*.json ./

# Instalar as dependências do projeto
RUN npm ci

# Copiar o resto dos arquivos do projeto
COPY . .

# Expor a porta que o Vite (servidor do React) usa por padrão
EXPOSE 5173

# Comando para iniciar o servidor de desenvolvimento
# O '-- --host' é crucial para que o servidor seja acessível de fora do container
CMD ["npm", "run", "dev", "--", "--host"]