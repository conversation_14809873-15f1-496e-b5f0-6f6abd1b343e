// frontend/src/utils/mapHelpers.js - CONFIGURAÇÃO COMPLETA COM INDICADOR DE ÔNIBUS

import L from 'leaflet';

// Configuração completa dos indicadores com cores e legendas
export const configIndicadores = {
  'GS': {
    titulo: '<PERSON><PERSON><PERSON> de Saturação',
    descricao: 'Indica o nível de ocupação do ponto de ônibus',
    isStatic: false,
    worstIs: 'max',
    unidade: '%',
    niveis: [
      { 
        cor: '#4CAF50', // Verde - Ocioso
        texto: 'Ocioso (≤ 30%)', 
        condicao: (v) => v <= 0.3,
        descricao: 'Ponto subutilizado, com baixa demanda'
      },
      { 
        cor: '#2196F3', // Azul - Normal
        texto: 'Normal (30% - 70%)', 
        condicao: (v) => v > 0.3 && v <= 0.7,
        descricao: 'Ponto funcionando em condições adequadas'
      },
      { 
        cor: '#FF9800', // Laranja - Atenção
        texto: 'Atenção (70% - 90%)', 
        condicao: (v) => v > 0.7 && v <= 0.9,
        descricao: 'Ponto próximo da saturação, requer monitoramento'
      },
      { 
        cor: '#F44336', // Vermelho - Crítico
        texto: 'Crítico (> 90%)', 
        condicao: (v) => v > 0.9,
        descricao: 'Ponto saturado, demanda excessiva'
      }
    ]
  },
  
  'P(f)': {
    titulo: 'Probabilidade de Fila',
    descricao: 'Probabilidade de formar filas de ônibus no ponto',
    isStatic: false,
    worstIs: 'max',
    unidade: '%',
    niveis: [
      { 
        cor: '#4CAF50', // Verde
        texto: 'Baixa (≤ 20%)', 
        condicao: (v) => v <= 0.2,
        descricao: 'Raro formar filas'
      },
      { 
        cor: '#FF9800', // Laranja
        texto: 'Média (20% - 50%)', 
        condicao: (v) => v > 0.2 && v <= 0.5,
        descricao: 'Filas ocasionais'
      },
      { 
        cor: '#F44336', // Vermelho
        texto: 'Alta (> 50%)', 
        condicao: (v) => v > 0.5,
        descricao: 'Filas frequentes'
      }
    ]
  },
  
  'P(0)': {
    titulo: 'Probabilidade de Ponto Vazio',
    descricao: 'Probabilidade do ponto estar sem ônibus',
    isStatic: false,
    worstIs: 'min', // Para P(0), menor é pior
    unidade: '%',
    niveis: [
      { 
        cor: '#4CAF50', // Vermelho - Baixa (PIOR - ponto sempre ocupado)
        texto: 'Baixa (< 20%)', 
        condicao: (v) => v < 0.2,
        descricao: 'Ponto quase sempre ocupado - sobrecarga'
      },
      { 
        cor: '#FF9800', // Laranja - Média
        texto: 'Média (20% - 50%)', 
        condicao: (v) => v >= 0.2 && v <= 0.5,
        descricao: 'Disponibilidade moderada'
      },
      { 
        cor: '#F44336', // Verde - Alta (MELHOR - boa disponibilidade)
        texto: 'Alta (> 50%)', 
        condicao: (v) => v > 0.5,
        descricao: 'Ponto disponível na maior parte do tempo'
      }
    ]
  },
  
  'Passageiros': {
    titulo: 'Proporção Embarque/Desembarque',
    descricao: 'Distribuição entre embarques e desembarques',
    isStatic: false,
    worstIs: 'pie',
    cores: {
      embarque: '#43A047', // Verde para embarque
      desembarque: '#D32F2F' // Vermelho para desembarque
    },
    niveis: [
      { cor: '#43A047', texto: 'Embarque', tipo: 'embarque' },
      { cor: '#D32F2F', texto: 'Desembarque', tipo: 'desembarque' }
    ]
  },
  
  'Total Travessias': {
    titulo: 'Total de Travessias',
    descricao: 'Número total de travessias de pedestres',
    isStatic: false,
    worstIs: 'sum',
    unidade: 'travessias',
    niveis: [
      { 
        cor: '#9E9E9E', // Cinza
        texto: 'Nenhuma (0)', 
        condicao: (v) => v === 0,
        descricao: 'Sem movimento de pedestres'
      },
      { 
        cor: '#2196F3', // Azul
        texto: 'Baixo (1 - 20)', 
        condicao: (v) => v > 0 && v <= 20,
        descricao: 'Movimento leve de pedestres'
      },
      { 
        cor: '#FF9800', // Laranja
        texto: 'Médio (21 - 50)', 
        condicao: (v) => v > 20 && v <= 50,
        descricao: 'Movimento moderado'
      },
      { 
        cor: '#F44336', // Vermelho
        texto: 'Alto (> 50)', 
        condicao: (v) => v > 50,
        descricao: 'Movimento intenso de pedestres'
      }
    ]
  },

  'Onibus': {
    titulo: 'Frequência de Ônibus',
    descricao: 'Número de ônibus que passam pelo ponto por período',
    isStatic: false,
    worstIs: 'sum',
    unidade: 'ônibus',
    niveis: [
      { 
        cor: '#9E9E9E', // Cinza
        texto: 'Nenhum (0)', 
        condicao: (v) => v === 0,
        descricao: 'Nenhum ônibus no período'
      },
      { 
        cor: '#4CAF50', // Verde
        texto: 'Baixa (1 - 5)', 
        condicao: (v) => v > 0 && v <= 5,
        descricao: 'Frequência baixa de ônibus'
      },
      { 
        cor: '#2196F3', // Azul
        texto: 'Média (6 - 15)', 
        condicao: (v) => v > 5 && v <= 15,
        descricao: 'Frequência moderada'
      },
      { 
        cor: '#FF9800', // Laranja
        texto: 'Alta (16 - 30)', 
        condicao: (v) => v > 15 && v <= 30,
        descricao: 'Frequência alta'
      },
      { 
        cor: '#F44336', // Vermelho
        texto: 'Muito Alta (> 30)', 
        condicao: (v) => v > 30,
        descricao: 'Frequência muito alta de ônibus'
      }
    ]
  },
  
  // Indicadores categóricos (Sim/Não) - CORES PADRONIZADAS
  'Placa': {
    titulo: 'Presença de Placa',
    descricao: 'Indica se o ponto possui sinalização adequada',
    isStatic: true,
    isCategorical: true,
    niveis: [
      { 
        cor: '#4CAF50', // Verde padrão para SIM
        texto: 'Sim', 
        condicao: (v) => ['sim', 'Sim', 'SIM', 1, '1', 'true', true].includes(v),
        descricao: 'Ponto possui placa identificadora'
      },
      { 
        cor: '#F44336', // Vermelho padrão para NÃO
        texto: 'Não', 
        condicao: (v) => ['nao', 'não', 'Não', 'NÃO', 'Nao', 'NAO', 0, '0', 'false', false].includes(v),
        descricao: 'Ponto sem placa identificadora'
      }
    ]
  },
  
  'Abrigo': {
    titulo: 'Presença de Abrigo',
    descricao: 'Indica se o ponto possui cobertura para usuários',
    isStatic: true,
    isCategorical: true,
    niveis: [
      { 
        cor: '#4CAF50', // Verde padrão para SIM
        texto: 'Sim', 
        condicao: (v) => ['sim', 'Sim', 'SIM', 1, '1', 'true', true].includes(v),
        descricao: 'Ponto possui abrigo/cobertura'
      },
      { 
        cor: '#F44336', // Vermelho padrão para NÃO
        texto: 'Não', 
        condicao: (v) => ['nao', 'não', 'Não', 'NÃO', 'Nao', 'NAO', 0, '0', 'false', false].includes(v),
        descricao: 'Ponto descoberto'
      }
    ]
  },
  
  'Baia': {
    titulo: 'Presença de Baia',
    descricao: 'Indica se o ponto possui área específica para parada',
    isStatic: true,
    isCategorical: true,
    niveis: [
      { 
        cor: '#4CAF50', // Verde padrão para SIM
        texto: 'Sim', 
        condicao: (v) => ['sim', 'Sim', 'SIM', 1, '1', 'true', true].includes(v),
        descricao: 'Ponto com baia para ônibus'
      },
      { 
        cor: '#F44336', // Vermelho padrão para NÃO
        texto: 'Não', 
        condicao: (v) => ['nao', 'não', 'Não', 'NÃO', 'Nao', 'NAO', 0, '0', 'false', false].includes(v),
        descricao: 'Ponto sem baia específica'
      }
    ]
  },
  
  'Mancha Urbana': {
    titulo: 'Localização na Mancha Urbana',
    descricao: 'Indica se o ponto está em área urbana consolidada',
    isStatic: true,
    isCategorical: true,
    niveis: [
      { 
        cor: '#4CAF50', // Verde padrão para SIM
        texto: 'Sim', 
        condicao: (v) => ['sim', 'Sim', 'SIM', 1, '1', 'true', true].includes(v),
        descricao: 'Localizado em área urbana'
      },
      { 
        cor: '#F44336', // Vermelho padrão para NÃO
        texto: 'Não', 
        condicao: (v) => ['nao', 'não', 'Não', 'NÃO', 'Nao', 'NAO', 0, '0', 'false', false].includes(v),
        descricao: 'Localizado em área rural/suburbana'
      }
    ]
  },
  
  'SH': {
    titulo: 'Segmento Homogêneo (SH)',
    descricao: 'Classificação por segmento de rodovia',
    isStatic: true,
    isCategorical: true,
    // Cores automáticas para categorias (será calculado dinamicamente)
    coresCategoricas: [
      '#1976D2', // Azul
      '#388E3C', // Verde
      '#F57C00', // Laranja
      '#7B1FA2', // Roxo
      '#C62828', // Vermelho
      '#5D4037', // Marrom
      '#455A64', // Azul cinza
      '#E91E63'  // Rosa
    ]
  }
};

// Função para gerar ícone de pizza (embarque/desembarque)
// Função para gerar ícone de pizza (embarque/desembarque)
export function generatePieIcon(embarques, desembarques, volumeTotal, maxVolume, tamanhoMultiplier) {
  const total = embarques + desembarques;
  const baseSize = 20;
  const maxSizeIncrease = 40;
  const size = (baseSize + maxSizeIncrease * (volumeTotal / maxVolume)) * tamanhoMultiplier;
  
  if (total === 0) {
    return L.divIcon({ 
      html: `<div style="
        width: ${size}px; 
        height: ${size}px; 
        background-color: #9E9E9E; 
        border-radius: 50%; 
        border: 3px solid white; 
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: ${Math.max(10, size * 0.3)}px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        line-height: 1;
        font-family: Arial, sans-serif;
      ">0</div>`, 
      className: 'leaflet-pie-icon', 
      iconSize: [size, size], 
      iconAnchor: [size / 2, size / 2] 
    });
  }
  
  const embPercent = (embarques / total) * 100;
  const config = configIndicadores['Passageiros'];
  
  // Ajusta o tamanho da fonte do número central baseado no tamanho do ícone e número de dígitos
  const totalStr = String(total);
  const numChars = totalStr.length;
  let fontSize;
  
  if (numChars <= 2) {
    fontSize = Math.max(8, size * 0.25); 
  } else if (numChars <= 3) {
    fontSize = Math.max(7, size * 0.2); 
  } else {
    fontSize = Math.max(6, size * 0.15); 
  }
  
  const html = `<div style="
    width: ${size}px; 
    height: ${size}px; 
    background: conic-gradient(
      ${config.cores.embarque} 0% ${embPercent}%, 
      ${config.cores.desembarque} ${embPercent}% 100%
    ); 
    border-radius: 50%; 
    border: 3px solid white; 
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    position: relative;
  ">
    <div style="
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255,255,255,0.95);
      border-radius: 50%;
      width: ${size * 0.5}px;
      height: ${size * 0.5}px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: ${fontSize}px;
      color: #333;
      text-shadow: none;
      line-height: 1;
      font-family: Arial, sans-serif;
      border: 1px solid rgba(0,0,0,0.1);
    ">${total}</div>
  </div>`;
  
  return L.divIcon({ 
    html, 
    className: 'leaflet-pie-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
}

// Função para gerar ícone colorido simples
export function generatePinIcon(cor, tamanhoMultiplier, valor = null) {
  const size = 25 * tamanhoMultiplier;
  
  // Ajusta o tamanho da fonte baseado no valor e tamanho do ícone
  let fontSize;
  if (valor !== null && valor !== undefined) {
    const valorStr = String(valor);
    const numChars = valorStr.length;
    
    if (numChars <= 2) {
      fontSize = Math.max(8, size * 0.5); // Números pequenos: fonte maior
    } else if (numChars <= 3) {
      fontSize = Math.max(7, size * 0.35); // Números médios
    } else {
      fontSize = Math.max(6, size * 0.25); // Números grandes: fonte menor
    }
  } else {
    fontSize = Math.max(10, size * 0.4);
  }
  
  const html = `<div style="
    width: ${size}px; 
    height: ${size}px; 
    background-color: ${cor}; 
    border-radius: 50%; 
    border: 3px solid white; 
    box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: ${fontSize}px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    line-height: 1;
    font-family: Arial, sans-serif;
  ">${valor !== null && valor !== undefined ? valor : ''}</div>`;
  
  return L.divIcon({ 
    html, 
    className: 'leaflet-pin-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
}

// Função para determinar a cor baseada no valor e configuração
export function getColorForValue(indicator, value, allValues = null) {
  const config = configIndicadores[indicator];
  if (!config) {
    console.warn(`Configuração não encontrada para indicador: ${indicator}`);
    return '#808080'; // Cinza padrão
  }

  // Debug para indicadores categóricos
  if (config.isCategorical) {
    console.log(`Indicador: ${indicator}, Valor recebido: "${value}" (tipo: ${typeof value})`);
  }

  // Para indicadores categóricos dinâmicos (como SH)
  if (config.isCategorical && config.coresCategoricas) {
    if (!allValues) return config.coresCategoricas[0];
    
    const uniqueValues = [...new Set(allValues)].sort();
    const index = uniqueValues.indexOf(value);
    return config.coresCategoricas[index % config.coresCategoricas.length];
  }
  
  // Para indicadores com níveis definidos (Sim/Não e numéricos)
  if (config.niveis) {
    const nivel = config.niveis.find(n => n.condicao(value));
    if (nivel) {
      console.log(`Indicador: ${indicator}, Valor: "${value}" → Cor: ${nivel.cor} (${nivel.texto})`);
      return nivel.cor;
    } else {
      console.warn(`Nenhum nível encontrado para indicador: ${indicator}, valor: "${value}"`);
      return '#808080'; // Cinza padrão para valores não mapeados
    }
  }
  
  return '#808080';
}

// Função para formatar valor para exibição
export function formatValue(indicator, value) {
  const config = configIndicadores[indicator];
  if (!config) return value;
  
  if (value === null || value === undefined) return 'N/A';
  
  // Para indicadores percentuais
  if (config.unidade === '%') {
    return `${Math.round(value * 100)}%`;
  }
  
  // Para indicadores numéricos
  if (typeof value === 'number') {
    return Math.round(value);
  }
  
  return value;
}

// Função para obter a legenda de um indicador
export function getLegendItems(indicator, allValues = null) {
  const config = configIndicadores[indicator];
  if (!config) return [];
  
  // Para indicadores categóricos dinâmicos
  if (config.isCategorical && config.coresCategoricas && allValues) {
    const uniqueValues = [...new Set(allValues)].filter(v => v !== null && v !== undefined).sort();
    return uniqueValues.map((value, index) => ({
      cor: config.coresCategoricas[index % config.coresCategoricas.length],
      texto: value,
      descricao: `Categoria: ${value}`
    }));
  }
  
  // Para indicadores com níveis predefinidos
  if (config.niveis) {
    return config.niveis.map(nivel => ({
      cor: nivel.cor,
      texto: nivel.texto,
      descricao: nivel.descricao || nivel.texto
    }));
  }
  
  return [];
}