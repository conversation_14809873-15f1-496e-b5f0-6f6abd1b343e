// frontend/src/api/pedService.js - CORREÇÃO FINAL DEFINITIVA

import axios from 'axios';

// Configura a instância do axios com configurações específicas
const API = axios.create({ 
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
  // CRITICAL: Force response to be parsed as JSON
  responseType: 'json',
  // Force JSON transformation
  transformResponse: [function (data) {
    console.log('🔄 Transform Response - Raw data type:', typeof data);
    console.log('🔄 Transform Response - Raw data preview:', String(data).substring(0, 200));
    
    // Se já é um objeto, retorna como está
    if (typeof data === 'object' && data !== null) {
      console.log('✅ Data already is object');
      return data;
    }
    
    // Se é string, tenta fazer parse
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        console.log('✅ Successfully parsed string to object');
        return parsed;
      } catch (e) {
        console.error('❌ Failed to parse string as JSON:', e);
        console.error('❌ String content:', data.substring(0, 500));
        
        // Verifica se é HTML (erro comum)
        if (data.includes('<html>') || data.includes('<!DOCTYPE')) {
          throw new Error('API retornou HTML em vez de JSON - possível erro no servidor');
        }
        
        throw new Error(`Resposta não é JSON válido: ${e.message}`);
      }
    }
    
    throw new Error(`Tipo de resposta inesperado: ${typeof data}`);
  }]
});

// Interceptor de requisição para debug
API.interceptors.request.use(
  (config) => {
    console.log('🚀 Sending request:', {
      url: config.url,
      method: config.method,
      fullURL: `${config.baseURL}${config.url}`,
      headers: config.headers,
      responseType: config.responseType
    });
    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Interceptor de resposta para debug e validation
API.interceptors.response.use(
  (response) => {
    console.log('✅ Response received:', {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers['content-type'],
      dataType: typeof response.data,
      dataIsObject: typeof response.data === 'object' && response.data !== null,
      dataKeys: response.data && typeof response.data === 'object' ? Object.keys(response.data) : 'N/A',
      url: response.config.url
    });
    
    // Validação adicional para garantir que é um objeto
    if (typeof response.data !== 'object' || response.data === null) {
      console.error('❌ Response data is not an object:', typeof response.data);
      throw new Error(`API retornou ${typeof response.data} em vez de object`);
    }
    
    return response;
  },
  (error) => {
    console.error('❌ Response interceptor error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      responseType: typeof error.response?.data,
      url: error.config?.url
    });
    return Promise.reject(error);
  }
);

export async function fetchAllPeds() {
  try {
    console.log('📡 Starting fetchAllPeds...');
    
    // Método alternativo: fazer requisição direta sem health check
    console.log('🎯 Making direct request to /api/dados');
    
    const response = await API.get('/api/dados');
    
    console.log('📊 Raw response analysis:', {
      responseType: typeof response.data,
      responseConstructor: response.data?.constructor?.name,
      responseKeys: response.data ? Object.keys(response.data) : null,
      hasIndicadores: response.data && response.data.hasOwnProperty('indicadores'),
      hasPedsData: response.data && response.data.hasOwnProperty('pedsData')
    });
    
    const data = response.data;
    
    // Validação ultra-robusta
    if (!data) {
      throw new Error('Resposta da API está vazia (null/undefined)');
    }
    
    if (typeof data !== 'object') {
      throw new Error(`Tipo de dados inválido: esperado 'object', recebido '${typeof data}'`);
    }
    
    if (Array.isArray(data)) {
      throw new Error('API retornou array em vez de objeto');
    }
    
    // Verifica estrutura específica
    const hasIndicadores = data.hasOwnProperty('indicadores');
    const hasPedsData = data.hasOwnProperty('pedsData');
    
    if (!hasIndicadores) {
      throw new Error('Propriedade "indicadores" ausente na resposta da API');
    }
    
    if (!hasPedsData) {
      throw new Error('Propriedade "pedsData" ausente na resposta da API');
    }
    
    if (typeof data.indicadores !== 'object') {
      throw new Error(`"indicadores" deve ser um objeto, recebido: ${typeof data.indicadores}`);
    }
    
    if (typeof data.pedsData !== 'object') {
      throw new Error(`"pedsData" deve ser um objeto, recebido: ${typeof data.pedsData}`);
    }
    
    // Validação dos dados dos PEDs
    const pedsKeys = Object.keys(data.pedsData);
    if (pedsKeys.length === 0) {
      console.warn('⚠️ Nenhum PED encontrado em pedsData');
    } else {
      console.log(`✅ Encontrados ${pedsKeys.length} PEDs`);
      
      // Verifica se pelo menos um PED tem estrutura válida
      const firstPedKey = pedsKeys[0];
      const firstPed = data.pedsData[firstPedKey];
      
      if (!firstPed || typeof firstPed !== 'object') {
        throw new Error(`Estrutura inválida do PED: ${firstPedKey}`);
      }
      
      if (!firstPed.dados_horarios || typeof firstPed.dados_horarios !== 'object') {
        throw new Error(`PED "${firstPedKey}" não possui dados_horarios válidos`);
      }
      
      console.log(`✅ Primeiro PED "${firstPedKey}" tem ${Object.keys(firstPed.dados_horarios).length} horários`);
    }
    
    console.log('✅ Dados validados com sucesso:', {
      indicadoresCount: Object.keys(data.indicadores).length,
      pedsCount: pedsKeys.length
    });
    
    return data;
    
  } catch (error) {
    console.error('💥 Erro detalhado em fetchAllPeds:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      isAxiosError: error.isAxiosError,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        dataType: typeof error.response.data,
        data: error.response.data
      } : null
    });
    
    // Re-throw com mensagem específica baseada no tipo de erro
    let errorMessage = error.message;
    
    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Não foi possível conectar ao servidor backend (ECONNREFUSED)';
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = 'Servidor backend não encontrado (ENOTFOUND)';
    } else if (error.message.includes('timeout')) {
      errorMessage = 'Timeout na requisição - servidor demorou muito para responder';
    } else if (error.response?.status === 404) {
      errorMessage = 'Endpoint /api/dados não encontrado no servidor';
    } else if (error.response?.status === 500) {
      errorMessage = 'Erro interno do servidor (500)';
    }
    
    throw new Error(`Falha ao buscar dados: ${errorMessage}`);
  }
}

// Função para testar usando fetch nativo (fallback)
export async function fetchAllPedsNative() {
  try {
    console.log('🧪 Testing with native fetch...');
    
    const response = await fetch('http://localhost:5000/api/dados', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      mode: 'cors'
    });
    
    console.log('🧪 Native fetch response:', {
      ok: response.ok,
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type')
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const text = await response.text();
    console.log('🧪 Raw response text type:', typeof text);
    console.log('🧪 Raw response preview:', text.substring(0, 200));
    
    const data = JSON.parse(text);
    console.log('🧪 Parsed data type:', typeof data);
    
    return data;
    
  } catch (error) {
    console.error('🧪 Native fetch error:', error);
    throw error;
  }
}

// Função auxiliar para testar a API manualmente
export async function testAPI() {
  try {
    console.log('🧪 Testing API with both methods...');
    
    console.log('--- Testing with Axios ---');
    const axiosData = await fetchAllPeds();
    console.log('✅ Axios test successful');
    
    console.log('--- Testing with Native Fetch ---');
    const fetchData = await fetchAllPedsNative();
    console.log('✅ Native fetch test successful');
    
    console.log('🧪 Both methods successful');
    return axiosData;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // Tenta fallback com native fetch
    try {
      console.log('🔄 Trying fallback with native fetch...');
      return await fetchAllPedsNative();
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError.message);
      throw error;
    }
  }
}

// Exporta a instância do axios para debug
export { API };