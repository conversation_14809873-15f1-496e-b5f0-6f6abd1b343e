// frontend/src/pages/DashboardCCV.jsx - VERSÃO FINAL COMPLETA

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// ATUALIZAÇÃO: Importa a nova função para buscar dados agrupados
import { fetchGroupedCCVData, fetchEmpresas, fetchProjetos } from '../api/ccvImportService';
import SidebarCCV from '../components/SidebarCCV';
import MapCCV from '../components/MapCCV';
import LegendCCV from '../components/LegendCCV';

export default function DashboardCCV({ isSidebarVisible }) {
  const [loading, setLoading] = useState(true);
  const [loadingStep, setLoadingStep] = useState('Iniciando...');
  const [dados, setDados] = useState(null);
  const [empresas, setEmpresas] = useState([]);
  const [projetos, setProjetos] = useState([]);
  const [error, setError] = useState(null);
  
  // Estados de filtros
  const [empresaSelecionada, setEmpresaSelecionada] = useState(null);
  const [projetoSelecionado, setProjetoSelecionado] = useState(null);
  const [indicadorSelecionado, setIndicadorSelecionado] = useState('Volume Total');
  
  // ATUALIZAÇÃO: O estado agora controla o grupo selecionado, não mais o tipo de veículo
  const [grupoSelecionado, setGrupoSelecionado] = useState(null);
  
  // Estado para controlar a visibilidade das aproximações
  const [mostrarMovimentos, setMostrarMovimentos] = useState(false);
  
  const navigate = useNavigate();

  useEffect(() => {
    carregarDadosIniciais();
  }, []);

  useEffect(() => {
    if (empresaSelecionada) {
      carregarProjetos();
    } else {
      setProjetos([]);
      setProjetoSelecionado(null);
    }
  }, [empresaSelecionada]);

  useEffect(() => {
    if (projetoSelecionado) {
      carregarDadosCCV();
    } else {
      setDados(null);
    }
  }, [projetoSelecionado]);

  const carregarDadosIniciais = async () => {
    try {
      setLoading(true);
      setLoadingStep('Carregando empresas...');
      const empresasData = await fetchEmpresas();
      setEmpresas(empresasData.empresas);
      if (empresasData.empresas.length > 0) {
        setEmpresaSelecionada(empresasData.empresas[0].id);
      } else {
        setLoading(false);
      }
    } catch (err) {
      setError(`Erro ao carregar empresas: ${err.message}`);
      setLoading(false);
    }
  };

  const carregarProjetos = async () => {
    try {
      setLoadingStep('Carregando projetos...');
      const projetosData = await fetchProjetos(empresaSelecionada);
      setProjetos(projetosData.projetos);
      if (projetosData.projetos.length > 0) {
        setProjetoSelecionado(projetosData.projetos[0].id);
      } else {
        setProjetoSelecionado(null);
        setLoading(false);
      }
    } catch (err) {
      setError(`Erro ao carregar projetos: ${err.message}`);
      setLoading(false);
    }
  };

  const carregarDadosCCV = async () => {
    if (!projetoSelecionado) return;
    try {
      setLoading(true);
      setLoadingStep('Carregando dados CCV agrupados...');
      
      // ATUALIZAÇÃO: Chama a nova função da API
      const data = await fetchGroupedCCVData(projetoSelecionado);
      setDados(data);
      
      // Define o primeiro grupo como padrão, se existir
      if (data.grupos_configurados && data.grupos_configurados.length > 0) {
        setGrupoSelecionado(data.grupos_configurados[0].codigo);
      } else {
        setGrupoSelecionado(null); // Nenhum grupo configurado
      }
      
      setError(null);
    } catch (err) {
      setError(`Erro ao carregar dados de contagem: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkerClick = (nomePonto) => {
    // Garante que temos um projeto selecionado antes de navegar
    if (projetoSelecionado) {
      // Adiciona o projeto_id como um parâmetro de busca na URL
      navigate(`/ccv/${encodeURIComponent(nomePonto)}?projeto_id=${projetoSelecionado}`);
    } else {
      console.error("Nenhum projeto selecionado para abrir os detalhes do ponto.");
      setError("Selecione um projeto para ver os detalhes do ponto.");
    }
  };

  const toggleMovimentos = () => {
    setMostrarMovimentos(!mostrarMovimentos);
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Carregando dados CCV...</h2>
          <p className="text-gray-600 mb-4">{loadingStep}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-2xl">
          <i className="fas fa-exclamation-triangle text-red-500 text-5xl mb-4"></i>
          <h2 className="text-2xl font-bold text-red-800 mb-4">Erro ao Carregar Dados CCV</h2>
          <p className="text-red-600 mb-4 text-lg">{error}</p>
          <button onClick={carregarDadosIniciais} className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700">
            <i className="fas fa-redo mr-2"></i>Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {isSidebarVisible && (
        <SidebarCCV
          empresas={empresas}
          projetos={projetos}
          dados={dados}
          empresaSelecionada={empresaSelecionada}
          setEmpresaSelecionada={setEmpresaSelecionada}
          projetoSelecionado={projetoSelecionado}
          setProjetoSelecionado={setProjetoSelecionado}
          indicadorSelecionado={indicadorSelecionado}
          setIndicadorSelecionado={setIndicadorSelecionado}
          
          // ATUALIZAÇÃO: Passa os grupos para a sidebar
          grupos={dados?.grupos_configurados || []}
          grupoSelecionado={grupoSelecionado}
          setGrupoSelecionado={setGrupoSelecionado}
        />
      )}
      
      <main className="flex-1 relative">
        <MapCCV
          dados={dados}
          indicador={indicadorSelecionado}
          // ATUALIZAÇÃO: Passa o grupo selecionado para o mapa
          grupoSelecionado={grupoSelecionado}
          onMarkerClick={handleMarkerClick}
          mostrarMovimentos={mostrarMovimentos}
        />
        
        <LegendCCV 
          indicador={indicadorSelecionado}
          // ATUALIZAÇÃO: Passa os grupos para a legenda
          grupos={dados?.grupos_configurados || []}
          grupoSelecionado={grupoSelecionado}
          dados={dados}
        />
        
        <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]" style={{ marginRight: '60px' }}>
          <label className="flex items-center space-x-2 cursor-pointer text-sm">
            <input
              type="checkbox"
              checked={mostrarMovimentos}
              onChange={toggleMovimentos}
              className="form-checkbox h-4 w-4 text-blue-600 rounded"
            />
            <span className="font-medium text-gray-700">Mostrar Aproximações</span>
          </label>
          <button 
            onClick={() => navigate('/ccv-configuracao')}
            className="mt-2 w-full text-left text-sm font-medium text-gray-700 hover:bg-gray-100 p-2 rounded"
            title="Configurar grupos de veículos"
          >
            <i className="fas fa-cogs mr-2"></i>
            Configurar Grupos
          </button>
        </div>
      </main>
    </>
  );
}