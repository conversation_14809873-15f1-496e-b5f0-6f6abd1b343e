// frontend/src/pages/PedDetail.jsx - TABELA COMPLETA COM CAMPOS ESPECIFICADOS

import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect, useState, useRef } from 'react';
import { fetchAllPeds } from '../api/pedService';
import { Chart, BarElement, LineElement, ArcElement, CategoryScale, LinearScale, PointElement, Legend, Title, Tooltip } from 'chart.js';
import { Bar, Line, Pie } from 'react-chartjs-2';
Chart.register(BarElement, LineElement, ArcElement, CategoryScale, LinearScale, PointElement, Legend, Title, Tooltip);

export default function PedDetail() {
  const { pedName } = useParams();
  const [loading, setLoading] = useState(true);
  const [ped, setPed] = useState(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [modalImageSrc, setModalImageSrc] = useState('');
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [helpContent, setHelpContent] = useState({ title: '', content: '' });
  
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const helpModalContentRef = useRef(null);

  // Definição das colunas fixas com os campos e labels especificados
  const fixedColumns = [
    { key: 'Nome do PED', label: 'Nome do PED', isPedData: true },
    { key: 'HORA', label: 'HORA' },
    { key: 'Ponto', label: 'Ponto', isPedData: true },
    { key: 'SH', label: 'SH' },
    { key: 'Km', label: 'Km' },
    { key: 'lat', label: 'lat', isPedData: true },
    { key: 'long', label: 'long', isPedData: true },
    { key: 'Sentido', label: 'Sentido' },
    { key: 'Placa', label: 'Placa' },
    { key: 'Abrigo', label: 'Abrigo' },
    { key: 'Baia', label: 'Baia' },
    { key: 'Mancha Urbana', label: 'Mancha Urbana' },
    { key: 'Intervalo', label: 'Intervalo' },
    { key: 'Onibus', label: 'Ônibus' },
    { key: 'Embarque', label: 'Embarque' },
    { key: 'Desembarque', label: 'Desembarque' },
    { key: 'Total', label: 'Total' },
    { key: '1 - 2', label: '1 - 2' },
    { key: '2 - 1', label: '2 - 1' },
    { key: 'GS_c1', label: 'GS_c1' },
    { key: 'GS_c2', label: 'GS_c2' },
    { key: 'GS_c3', label: 'GS_c3' },
    { key: 'GS_c4', label: 'GS_c4' },
    { key: 'GS_c5', label: 'GS_c5' },
    { key: 'P(0)_c1', label: 'P(0)_c1' },
    { key: 'P(0)_c2', label: 'P(0)_c2' },
    { key: 'P(0)_c3', label: 'P(0)_c3' },
    { key: 'P(0)_c4', label: 'P(0)_c4' },
    { key: 'P(0)_c5', label: 'P(0)_c5' },
    { key: 'P(f)_c1', label: 'P(f)_c1' },
    { key: 'P(f)_c2', label: 'P(f)_c2' },
    { key: 'P(f)_c3', label: 'P(f)_c3' },
    { key: 'P(f)_c4', label: 'P(f)_c4' },
    { key: 'P(f)_c5', label: 'P(f)_c5' }
  ];

  // Configuração do MathJax
  useEffect(() => {
    // Configura o MathJax
    if (window.MathJax) {
      window.MathJax.config = {
        tex: {
          inlineMath: [['$', '$'], ['\\(', '\\)']],
          displayMath: [['$$', '$$'], ['\\[', '\\]']]
        },
        svg: { fontCache: 'global' }
      };
    } else {
      // Carrega o MathJax se não estiver disponível
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
      script.async = true;
      document.head.appendChild(script);
      
      window.MathJax = {
        tex: {
          inlineMath: [['$', '$'], ['\\(', '\\)']],
          displayMath: [['$$', '$$'], ['\\[', '\\]']]
        },
        svg: { fontCache: 'global' }
      };
    }
  }, []);

  // Conteúdo de ajuda com LaTeX
  const helpContentData = {
    basicas: {
      title: "Informações Básicas",
      content: `<p class="mb-4">Esta seção exibe dados cadastrais e de localização do Ponto de Embarque e Desembarque (PED).</p>
                <ul class="list-disc list-inside space-y-2">
                  <li><strong>Localização:</strong> Endereço ou rodovia onde o ponto está localizado.</li>
                  <li><strong>Segmento Homogêneo (SH):</strong> Código do trecho da via com características uniformes.</li>
                  <li><strong>Latitude/Longitude:</strong> Coordenadas geográficas exatas do ponto.</li>
                  <li><strong>Placa de Identificação:</strong> Informa se o ponto possui uma placa oficial de identificação.</li>
                  <li><strong>Possui baia?:</strong> Indica se o ponto possui uma baia de acostamento para os ônibus.</li>
                  <li><strong>Área Urbana?:</strong> Indica se o ponto está localizado dentro do perímetro urbano.</li>
                </ul>`
    },
    campo: {
      title: "Levantamento de Dados de Campo",
      content: `<p class="mb-4">Esta seção apresenta os dados brutos coletados em campo durante o período de observação.</p>
                <ul class="list-disc list-inside space-y-2">
                  <li><strong>Análise das Paradas de Ônibus:</strong> Mostra a quantidade de ônibus que pararam no ponto em intervalos de 15 minutos.</li>
                  <li><strong>Fluxo Diário de Passageiros:</strong> Gráfico empilhado que exibe o volume de embarques e desembarques a cada 15 minutos.</li>
                  <li><strong>Flutuação Horária dos Fluxos de Travessias:</strong> Apresenta o volume de pedestres que atravessam a via nos sentidos 1-2 e 2-1.</li>
                </ul>
                <hr class="my-4">
                <p>A <strong>Soma Móvel (Janela de 1h)</strong> é uma linha de tendência que suaviza as flutuações, mostrando a média do indicador na última hora.</p>`
    },
    desempenho: {
      title: "Indicadores de Desempenho - Teoria das Filas",
      content: `<p class="mb-4">Estes indicadores são calculados com base em modelos da Teoria das Filas para avaliar a eficiência do ponto em diferentes cenários de capacidade (número de posições de parada).</p>
                <hr class="my-4">
                <div class="space-y-4">
                  <div>
                      <h4 class="font-bold text-lg text-gray-800">Taxa de Utilização ($\\rho$)</h4>
                      <p>Representa a porcentagem do tempo em que as posições de parada estão ocupadas. É a razão entre a taxa de chegada de ônibus ($\\lambda$) e a capacidade total de atendimento do ponto ($c \\cdot \\mu$).</p>
                      <p class="text-center my-2">$$ \\rho = \\frac{\\lambda}{c \\cdot \\mu} $$</p>
                      <p>Onde $c$ é o número de posições e $\\mu$ é a taxa de serviço por posição.</p>
                  </div>
                  <hr class="my-4">
                  <div>
                      <h4 class="font-bold text-lg text-gray-800">Probabilidade de Ponto Vazio ($P_0$)</h4>
                      <p>Indica a probabilidade de o ponto de parada estar completamente desocupado. Valores muito altos podem indicar ociosidade.</p>
                      <p class="text-center my-2">$$ P_0 = \\left[ \\sum_{n=0}^{c-1} \\frac{(\\lambda/\\mu)^n}{n!} + \\frac{(\\lambda/\\mu)^c}{c!(1 - \\rho)} \\right]^{-1} $$</p>
                  </div>
                  <hr class="my-4">
                  <div>
                      <h4 class="font-bold text-lg text-gray-800">Probabilidade de Fila ($P(f)$)</h4>
                      <p>Indica a probabilidade de um ônibus chegar e encontrar todas as posições de parada ocupadas, precisando esperar para atracar. É um indicador direto de congestionamento.</p>
                      <p class="text-center my-2">$$ P(\\text{fila}) = 1 - \\sum_{n=0}^{c-1} \\left( \\frac{(\\lambda/\\mu)^n}{n!} \\times P_0 \\right) $$</p>
                  </div>
                </div>`
    },
    tabela: {
      title: "Tabulação dos Dados do PED",
      content: `<p class="mb-4">Esta tabela apresenta todos os dados brutos e calculados para cada intervalo de 15 minutos do dia.</p>
                <p class="mb-4">As colunas incluem:</p>
                <ul class="list-disc list-inside space-y-2">
                  <li><strong>Dados Identificadores:</strong> Nome do PED, Hora, Ponto, SH, Km, Coordenadas</li>
                  <li><strong>Infraestrutura:</strong> Sentido, Placa, Abrigo, Baia, Mancha Urbana</li>
                  <li><strong>Dados Operacionais:</strong> Intervalo, Ônibus, Embarques, Desembarques, Total</li>
                  <li><strong>Travessias:</strong> Movimento nos sentidos 1-2 e 2-1</li>
                  <li><strong>Indicadores de Desempenho:</strong> Grau de Saturação (GS), Probabilidade de Ponto Vazio P(0) e Probabilidade de Fila P(f) para 5 cenários diferentes</li>
                </ul>
                <p class="mt-4">Use o botão <strong>'Copiar CSV'</strong> para copiar os dados da tabela para a sua área de transferência no formato CSV.</p>`
    }
  };

  useEffect(() => {
    loadPedDetails();
  }, [pedName]);

  // Renderizar MathJax quando o modal de ajuda abrir
  useEffect(() => {
    if (isHelpModalOpen && helpModalContentRef.current && window.MathJax) {
      window.MathJax.typesetClear && window.MathJax.typesetClear();
      window.MathJax.typesetPromise && window.MathJax.typesetPromise([helpModalContentRef.current]).catch(console.error);
    }
  }, [isHelpModalOpen, helpContent]);

  const loadPedDetails = async () => {
    try {
      const data = await fetchAllPeds();
      const pedData = data.pedsData[pedName];
      
      if (!pedData) {
        throw new Error(`PED "${pedName}" não encontrado`);
      }

      setPed(pedData);
      
    } catch (error) {
      console.error('Erro ao carregar detalhes:', error);
      setPed(null);
    } finally {
      setLoading(false);
    }
  };

  const handleVoltar = () => {
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const zoom = searchParams.get('zoom');
    
    if (lat && lng && zoom) {
      const params = new URLSearchParams({ lat, lng, zoom });
      navigate(`/?${params.toString()}`);
    } else {
      navigate('/');
    }
  };

  const openImageModal = (src) => {
    if (src && !src.includes('placehold.co')) {
      setModalImageSrc(src);
      setIsImageModalOpen(true);
    }
  };

  const openHelpModal = (topic) => {
    const content = helpContentData[topic];
    if (content) {
      setHelpContent(content);
      setIsHelpModalOpen(true);
    }
  };

  const copyTableAsCSV = () => {
    if (!ped) return;
    
    const horarios = Object.keys(ped.dados_horarios).sort();
    
    // Cabeçalho CSV com nomes personalizados
    const csvHeaders = fixedColumns.map(col => col.label).join(',');
    let csvContent = csvHeaders + '\r\n';
    
    // Dados das linhas
    horarios.forEach(h => {
      const rowData = ped.dados_horarios[h];
      const rowValues = fixedColumns.map(col => {
        let value;
        
        // Para dados do PED (não variam por horário)
        if (col.isPedData) {
          if (col.key === 'Nome do PED') {
            value = ped['Nome do PED'];
          } else if (col.key === 'lat') {
            value = ped.lat;
          } else if (col.key === 'long') {
            value = ped.long;
          } else if (col.key === 'Ponto') {
            value = ped.ponto || rowData.Ponto;
          }
        } else {
          // Para dados que variam por horário
          value = rowData[col.key];
          
          // Fallback para dados estáticos
          if (value === undefined || value === null) {
            if (col.key === 'SH') value = ped.sh || rowData.SH;
            else if (col.key === 'Km') value = ped.km || rowData.Km;
            else if (col.key === 'Sentido') value = ped.sentido || rowData.Sentido;
            else if (col.key === 'Placa') value = ped.placa || rowData.Placa;
            else if (col.key === 'Abrigo') value = ped.abrigo || rowData.Abrigo;
            else if (col.key === 'Baia') value = ped.baia || rowData.Baia;
            else if (col.key === 'Mancha Urbana') value = ped.mancha_urbana || rowData['Mancha Urbana'];
          }
        }
        
        if (value === null || value === undefined) value = '';
        let stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"')) {
          stringValue = `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      });
      csvContent += rowValues.join(',') + '\r\n';
    });
    
    navigator.clipboard.writeText(csvContent).then(() => {
      // Feedback visual melhorado
      const button = document.getElementById('copy-csv-btn');
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-check mr-2"></i>Copiado!';
      button.classList.add('bg-green-600', 'hover:bg-green-700');
      button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
      
      setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('bg-green-600', 'hover:bg-green-700');
        button.classList.add('bg-gray-600', 'hover:bg-gray-700');
      }, 2000);
    }).catch(() => {
      alert('Erro ao copiar dados.');
    });
  };

  if (loading) return <div className="p-6">Carregando…</div>;
  if (!ped) return <div className="p-6 text-red-600">PED não encontrado.</div>;

  const horarios = Object.keys(ped.dados_horarios).sort();
  const primeiroHorarioData = horarios.length > 0 ? ped.dados_horarios[horarios[0]] : {};
  
  // Cálculos dos totais
  const totalOnibus = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.Onibus || 0), 0);
  const totalPassageiros = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.Total || 0), 0);
  const totalEmbarques = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.Embarque || 0), 0);
  const totalDesembarques = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.Desembarque || 0), 0);

  return (
    <div className="min-h-screen bg-gray-100 font-sans">
      {/* CABEÇALHO ÚNICO FIXO */}
      <header className="bg-[#111827] text-white h-16 flex items-center px-6 shadow-lg z-30 flex-shrink-0 justify-between sticky top-0">
        <div className="flex items-center">
          <img 
            src="/images/logo1.png" 
            alt="Logótipo 1" 
            className="h-9" 
            onError={(e) => (e.currentTarget.style.display = 'none')} 
          />
        </div>
        <h1 className="text-xl font-semibold text-white absolute left-1/2 -translate-x-1/2 whitespace-nowrap">
          Dashboard de Performance dos Pontos de Embarque e Desembarque
        </h1>
        <div className="flex items-center">
          <img 
            src="/images/logo2.svg" 
            alt="Logótipo 2" 
            className="h-10" 
            onError={(e) => (e.currentTarget.style.display = 'none')} 
          />
        </div>
      </header>

      {/* CONTEÚDO PRINCIPAL COM SCROLL */}
      <main className="w-full">
        <div className="container mx-auto p-6 space-y-8">
          
          {/* CABEÇALHO DO PED */}
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-800">{ped['Nome do PED']}</h1>
            <div className="flex items-center gap-4">
              <a 
                href={`/pdf/${pedName}.pdf`} 
                target="_blank" 
                className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
              >
                <i className="fas fa-file-pdf"></i>Baixar Relatório
              </a>
              <button
                onClick={handleVoltar}
                className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <i className="fas fa-arrow-left"></i>Voltar ao Dashboard
              </button>
            </div>
          </div>

          {/* BLOCO 1: INFORMAÇÕES BÁSICAS */}
          <section>
            <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl flex justify-between items-center">
              <h2 className="text-xl font-bold">1. Informações Básicas</h2>
              <button 
                className="text-xl hover:text-gray-300" 
                onClick={() => openHelpModal('basicas')}
                aria-label="Ajuda sobre Informações Básicas"
              >
                <i className="fas fa-question-circle"></i>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 bg-white p-6 rounded-b-2xl shadow-md items-center">
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 h-full">
                <h3 className="text-lg font-bold text-gray-800 mb-3">Informações Básicas</h3>
                <div className="space-y-2 text-gray-700">
                  <div><span className="font-semibold">Localização:</span> {primeiroHorarioData.Km || ped.km || 'N/A'}</div>
                  <div><span className="font-semibold">Segmento Homogêneo:</span> {primeiroHorarioData.SH || ped.sh || 'N/A'}</div>
                  <div><span className="font-semibold">Lado:</span> {primeiroHorarioData.Sentido || ped.sentido || 'N/A'}</div>
                  <div><span className="font-semibold">Latitude:</span> {ped.lat || 'N/A'}</div>
                  <div><span className="font-semibold">Longitude:</span> {ped.long || 'N/A'}</div>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 h-full">
                <h3 className="text-lg font-bold text-gray-800 mb-3">Informações do PED</h3>
                <div className="space-y-2 text-gray-700">
                  <div><span className="font-semibold">Placa de Identificação:</span> {primeiroHorarioData.Placa || ped.placa || 'N/A'}</div>
                  <div><span className="font-semibold">Possui baia?:</span> {primeiroHorarioData.Baia || ped.baia || 'N/A'}</div>
                  <div><span className="font-semibold">Localizado em área urbana?:</span> {primeiroHorarioData['Mancha Urbana'] || ped.mancha_urbana || 'N/A'}</div>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <img 
                  src={`/images/${pedName}_sv.jpg`}
                  alt="Imagem da vista superior" 
                  className="w-full h-auto rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => openImageModal(`/images/${pedName}_sv.jpg`)}
                  onError={(e) => {
                    e.target.src = 'https://placehold.co/400x300/e2e8f0/64748b?text=Imagem+N/D';
                  }}
                />
              </div>
            </div>
          </section>

          {/* BLOCO 2: LEVANTAMENTO DE DADOS DE CAMPO */}
          <section>
            <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl flex justify-between items-center">
              <h2 className="text-xl font-bold">2. Levantamento de Dados de Campo</h2>
              <button 
                className="text-xl hover:text-gray-300" 
                onClick={() => openHelpModal('campo')}
                aria-label="Ajuda sobre Levantamento de Dados"
              >
                <i className="fas fa-question-circle"></i>
              </button>
            </div>
            <div className="bg-white p-6 rounded-b-2xl shadow-md space-y-8">
              {/* Cards de totais */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard icon="fa-bus" color="text-orange-500" label="Total de Ônibus (Dia)" value={totalOnibus} />
                <StatCard icon="fa-users" color="text-blue-500" label="Movimentação Total (Dia)" value={totalPassageiros} />
                <StatCard icon="fa-upload" color="text-green-500" label="Embarques" value={totalEmbarques} />
                <StatCard icon="fa-download" color="text-red-500" label="Desembarques" value={totalDesembarques} />
              </div>
              
              {/* Gráficos */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200">
                  <ChartOnibus ped={ped} horarios={horarios} />
                </div>
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200">
                  <ChartPassageiros ped={ped} horarios={horarios} />
                </div>
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 lg:col-span-2 grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
                  <div className="md:col-span-2">
                    <ChartTravessias ped={ped} horarios={horarios} />
                  </div>
                  <div>
                    <ChartTravessiasPizza ped={ped} horarios={horarios} />
                  </div>
                  <div className="text-center">
                    <h4 className="font-semibold text-gray-700 mb-2">Movimento da Travessia</h4>
                    <img 
                      src={`/images/${pedName}_t.jpg`}
                      alt="Imagem da travessia" 
                      className="w-full h-auto rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => openImageModal(`/images/${pedName}_t.jpg`)}
                      onError={(e) => {
                        e.target.src = 'https://placehold.co/400x300/e2e8f0/64748b?text=Imagem+N/D';
                        e.target.classList.add('bg-gray-200');
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* BLOCO 3: INDICADORES DE DESEMPENHO */}
          <section>
            <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl flex justify-between items-center">
              <h2 className="text-xl font-bold">3. Indicadores de Desempenho - Teoria das Filas</h2>
              <button 
                className="text-xl hover:text-gray-300" 
                onClick={() => openHelpModal('desempenho')}
                aria-label="Ajuda sobre Indicadores de Desempenho"
              >
                <i className="fas fa-question-circle"></i>
              </button>
            </div>
            <div className="bg-white p-6 rounded-b-2xl shadow-md">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200">
                  <ChartSaturacao ped={ped} horarios={horarios} />
                </div>
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200">
                  <ChartFila ped={ped} horarios={horarios} />
                </div>
                <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 lg:col-span-2">
                  <ChartOcioso ped={ped} horarios={horarios} />
                </div>
              </div>
            </div>
          </section>

          {/* BLOCO 4: TABULAÇÃO DOS DADOS - COLUNAS FIXAS */}
          <section>
            <div className="bg-gray-700 text-white py-2 px-4 rounded-t-2xl flex justify-between items-center">
              <h2 className="text-xl font-bold">4. Tabulação dos Dados do PED</h2>
              <button 
                className="text-xl hover:text-gray-300" 
                onClick={() => openHelpModal('tabela')}
                aria-label="Ajuda sobre a Tabulação de Dados"
              >
                <i className="fas fa-question-circle"></i>
              </button>
            </div>
            <div className="bg-white p-6 rounded-b-2xl shadow-md">
              {/* Controles da tabela */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <button 
                    id="copy-csv-btn"
                    onClick={copyTableAsCSV}
                    className="bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
                  >
                    <i className="fas fa-copy"></i>Copiar CSV
                  </button>
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">{fixedColumns.length} colunas</span> × 
                    <span className="font-medium"> {horarios.length} registros</span>
                  </div>
                </div>
              </div>
              
              {/* Container da tabela com scroll */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="overflow-x-auto overflow-y-auto" style={{ maxHeight: '600px' }}>
                  <table className="w-full text-sm text-left text-gray-500">
                    {/* Cabeçalho fixo */}
                    <thead className="text-xs text-gray-700 uppercase bg-gray-200 sticky top-0 z-10">
                      <tr>
                        {fixedColumns.map((col, index) => (
                          <th 
                            key={col.key} 
                            scope="col" 
                            className={`px-4 py-3 whitespace-nowrap font-semibold ${
                              index < fixedColumns.length - 1 ? 'border-r border-gray-300' : ''
                            }`}
                            style={{ minWidth: '120px' }}
                          >
                            {col.label}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    
                    {/* Corpo da tabela */}
                    <tbody>
                      {horarios.map((h, rowIndex) => (
                        <tr 
                          key={h} 
                          className={`${
                            rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                          } border-b hover:bg-blue-50 transition-colors`}
                        >
                          {fixedColumns.map((col, colIndex) => {
                            let value;
                            const rowData = ped.dados_horarios[h];
                            
                            // Lógica para obter o valor correto
                            if (col.isPedData) {
                              // Dados do PED que não variam por horário
                              if (col.key === 'Nome do PED') {
                                value = ped['Nome do PED'];
                              } else if (col.key === 'lat') {
                                value = ped.lat;
                              } else if (col.key === 'long') {
                                value = ped.long;
                              } else if (col.key === 'Ponto') {
                                value = ped.ponto || rowData?.Ponto;
                              }
                            } else {
                              // Para dados que variam por horário
                              if (col.key === 'HORA') {
                                value = h; // O horário é a chave
                              } else {
                                value = rowData?.[col.key];
                                
                                // Fallback para dados estáticos que podem estar no objeto ped
                                if (value === undefined || value === null) {
                                  if (col.key === 'SH') value = ped.sh || rowData?.SH;
                                  else if (col.key === 'Km') value = ped.km || rowData?.Km;
                                  else if (col.key === 'Sentido') value = ped.sentido || rowData?.Sentido;
                                  else if (col.key === 'Placa') value = ped.placa || rowData?.Placa;
                                  else if (col.key === 'Abrigo') value = ped.abrigo || rowData?.Abrigo;
                                  else if (col.key === 'Baia') value = ped.baia || rowData?.Baia;
                                  else if (col.key === 'Mancha Urbana') value = ped.mancha_urbana || rowData?.['Mancha Urbana'];
                                }
                              }
                            }
                            
                            // Formatação do valor para exibição
                            let displayValue;
                            if (value === null || value === undefined || value === '') {
                              displayValue = 'N/A';
                            } else if (typeof value === 'number') {
                              if (Number.isInteger(value)) {
                                displayValue = value.toString();
                              } else {
                                displayValue = value.toFixed(4);
                              }
                            } else {
                              displayValue = String(value);
                            }
                            
                            return (
                              <td 
                                key={`${h}-${col.key}`} 
                                className={`px-4 py-3 whitespace-nowrap ${
                                  colIndex < fixedColumns.length - 1 ? 'border-r border-gray-200' : ''
                                } ${
                                  displayValue === 'N/A' ? 'text-gray-400 italic' : 'text-gray-900'
                                }`}
                                style={{ minWidth: '120px' }}
                              >
                                {displayValue}
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              {/* Informações da tabela */}
              <div className="mt-4 text-sm text-gray-600 flex justify-between items-center">
                <div>
                  Exibindo <span className="font-medium">{horarios.length}</span> registros com <span className="font-medium">{fixedColumns.length}</span> campos cada
                </div>
                <div className="text-xs text-gray-500">
                  Dados coletados em intervalos de 15 minutos
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>

      {/* MODAL DE IMAGEM */}
      {isImageModalOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 transition-opacity duration-300"
          onClick={() => setIsImageModalOpen(false)}
        >
          <span 
            className="absolute top-5 right-8 text-white text-5xl font-bold cursor-pointer hover:text-gray-300"
            onClick={() => setIsImageModalOpen(false)}
          >
            &times;
          </span>
          <img 
            src={modalImageSrc}
            alt="Imagem ampliada" 
            className="max-w-[90vw] max-h-[90vh] rounded-lg"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      )}

      {/* MODAL DE AJUDA COM LATEX */}
      {isHelpModalOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 transition-opacity duration-300"
          onClick={() => setIsHelpModalOpen(false)}
        >
          <div 
            className="bg-white rounded-lg shadow-2xl max-w-3xl w-full m-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-2xl font-bold text-gray-800">{helpContent.title}</h3>
              <button 
                onClick={() => setIsHelpModalOpen(false)}
                className="text-gray-500 hover:text-gray-800 text-3xl"
              >
                &times;
              </button>
            </div>
            <div 
              ref={helpModalContentRef}
              className="p-6 text-gray-700 leading-relaxed max-h-[70vh] overflow-y-auto"
              dangerouslySetInnerHTML={{ __html: helpContent.content }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

// Componente para cards de estatísticas
function StatCard({ icon, color, label, value }) {
  return (
    <div className="bg-gray-50 p-6 rounded-2xl shadow-md border border-gray-200 flex items-center">
      <i className={`fa-solid ${icon} text-4xl ${color} mr-4`} />
      <div>
        <p className="text-gray-500 text-sm">{label}</p>
        <p className="text-2xl font-bold">{value}</p>
      </div>
    </div>
  );
}

// Componentes de gráficos
function ChartOnibus({ ped, horarios }) {
  const qteStopsData = horarios.map(h => ped.dados_horarios[h]?.Onibus || 0);
  const movingSumStops = [];
  
  for (let i = 0; i < qteStopsData.length; i++) {
    const window = qteStopsData.slice(i, i + 4);
    movingSumStops.push(window.length === 4 ? window.reduce((a, b) => a + b, 0) : null);
  }

  const data = {
    labels: horarios,
    datasets: [
      {
        label: 'Nº de Ônibus (Intervalo 15 min)',
        data: qteStopsData,
        backgroundColor: 'rgba(255, 159, 64, 0.6)',
        order: 2
      },
      {
        type: 'line',
        label: 'Soma Móvel (Janela de 1h)',
        data: movingSumStops,
        borderColor: 'rgba(0, 0, 0, 0.8)',
        backgroundColor: 'transparent',
        borderWidth: 2,
        pointRadius: 2,
        fill: false,
        tension: 0.4,
        order: 1
      }
    ]
  };

  const options = {
    scales: { y: { beginAtZero: true } },
    plugins: { 
      title: { 
        display: true, 
        text: 'Análise das Paradas de Ônibus', 
        font: { size: 16 } 
      } 
    }
  };

  return <Bar data={data} options={options} />;
}

function ChartPassageiros({ ped, horarios }) {
  const qteTotalPassageirosData = horarios.map(h => ped.dados_horarios[h]?.Total || 0);
  const movingSumPassageiros = [];
  
  for (let i = 0; i < qteTotalPassageirosData.length; i++) {
    const window = qteTotalPassageirosData.slice(i, i + 4);
    movingSumPassageiros.push(window.length === 4 ? window.reduce((a, b) => a + b, 0) : null);
  }

  const data = {
    labels: horarios,
    datasets: [
      { 
        label: 'Embarques', 
        data: horarios.map(h => ped.dados_horarios[h]?.Embarque || 0), 
        backgroundColor: 'rgba(40, 167, 69, 0.7)', 
        stack: 'Stack 0' 
      },
      { 
        label: 'Desembarques', 
        data: horarios.map(h => ped.dados_horarios[h]?.Desembarque || 0), 
        backgroundColor: 'rgba(220, 53, 69, 0.7)', 
        stack: 'Stack 0' 
      },
      { 
        type: 'line', 
        label: 'Soma Móvel Passageiros (1h)', 
        data: movingSumPassageiros, 
        borderColor: 'rgba(0, 0, 0, 0.8)', 
        backgroundColor: 'transparent', 
        borderWidth: 2, 
        pointRadius: 2, 
        fill: false, 
        tension: 0.4 
      }
    ]
  };

  const options = {
    scales: { 
      x: { stacked: true }, 
      y: { stacked: true, beginAtZero: true } 
    },
    plugins: { 
      title: { 
        display: true, 
        text: 'Fluxo Diário de Passageiros', 
        font: { size: 16 } 
      } 
    }
  };

  return <Bar data={data} options={options} />;
}

function ChartTravessias({ ped, horarios }) {
  const travessias12 = horarios.map(h => ped.dados_horarios[h]?.['1 - 2'] || 0);
  const travessias21 = horarios.map(h => ped.dados_horarios[h]?.['2 - 1'] || 0);
  const totalTravessiasData = horarios.map(h => 
    (ped.dados_horarios[h]?.['1 - 2'] || 0) + (ped.dados_horarios[h]?.['2 - 1'] || 0)
  );
  
  const movingSumTravessias = [];
  for (let i = 0; i < totalTravessiasData.length; i++) {
    const window = totalTravessiasData.slice(i, i + 4);
    movingSumTravessias.push(window.length === 4 ? window.reduce((a, b) => a + b, 0) : null);
  }

  const data = {
    labels: horarios,
    datasets: [
      { 
        label: 'Travessias 1-2', 
        data: travessias12, 
        backgroundColor: 'rgba(54, 162, 235, 0.7)', 
        stack: 'Stack 0' 
      },
      { 
        label: 'Travessias 2-1', 
        data: travessias21, 
        backgroundColor: 'rgba(255, 99, 132, 0.7)', 
        stack: 'Stack 0' 
      },
      { 
        type: 'line', 
        label: 'Soma Móvel Travessias (1h)', 
        data: movingSumTravessias, 
        borderColor: 'rgba(0, 0, 0, 0.8)', 
        backgroundColor: 'transparent', 
        borderWidth: 2, 
        pointRadius: 2, 
        fill: false, 
        tension: 0.4 
      }
    ]
  };

  const options = {
    scales: { 
      x: { stacked: true }, 
      y: { stacked: true, beginAtZero: true } 
    },
    plugins: { 
      title: { 
        display: true, 
        text: 'Flutuação Horária dos Fluxos de Travessias', 
        font: { size: 16 } 
      } 
    }
  };

  return <Bar data={data} options={options} />;
}

function ChartTravessiasPizza({ ped, horarios }) {
  const total12 = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.['1 - 2'] || 0), 0);
  const total21 = horarios.reduce((sum, h) => sum + (ped.dados_horarios[h]?.['2 - 1'] || 0), 0);
  const totalGeral = total12 + total21;

  const data = {
    labels: [
      `1-2 (${totalGeral > 0 ? ((total12 / totalGeral) * 100).toFixed(0) : 0}%)`,
      `2-1 (${totalGeral > 0 ? ((total21 / totalGeral) * 100).toFixed(0) : 0}%)`
    ],
    datasets: [{
      data: [total12, total21],
      backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(255, 99, 132, 0.7)']
    }]
  };

  const options = {
    plugins: { 
      title: { 
        display: true, 
        text: 'Proporção Total', 
        font: { size: 16 } 
      } 
    }
  };

  return <Pie data={data} options={options} />;
}

function ChartSaturacao({ ped, horarios }) {
  const cenarios = [1, 2, 3, 4, 5];
  const cores = [
    'rgba(255, 99, 132, 1)', 
    'rgba(54, 162, 235, 1)', 
    'rgba(255, 206, 86, 1)', 
    'rgba(75, 192, 192, 1)', 
    'rgba(153, 102, 255, 1)'
  ];

  const data = {
    labels: horarios,
    datasets: cenarios.map(c => ({
      label: `${c} ${c > 1 ? 'posições' : 'posição'}`,
      data: horarios.map(h => ped.dados_horarios[h]?.[`GS_c${c}`]),
      borderColor: cores[c - 1],
      tension: 0.1,
      fill: false
    }))
  };

  const options = {
    plugins: { 
      title: { 
        display: true, 
        text: 'Taxa de Utilização das Baias', 
        font: { size: 16 } 
      } 
    }
  };

  return <Line data={data} options={options} />;
}

function ChartFila({ ped, horarios }) {
  const cenarios = [1, 2, 3, 4, 5];
  const cores = [
    'rgba(255, 99, 132, 1)', 
    'rgba(54, 162, 235, 1)', 
    'rgba(255, 206, 86, 1)', 
    'rgba(75, 192, 192, 1)', 
    'rgba(153, 102, 255, 1)'
  ];

  const data = {
    labels: horarios,
    datasets: cenarios.map(c => ({
      label: `${c} ${c > 1 ? 'posições' : 'posição'}`,
      data: horarios.map(h => ped.dados_horarios[h]?.[`P(f)_c${c}`]),
      borderColor: cores[c - 1],
      tension: 0.1,
      fill: false
    }))
  };

  const options = {
    plugins: { 
      title: { 
        display: true, 
        text: 'Probabilidade de Formação de Filas', 
        font: { size: 16 } 
      } 
    }
  };

  return <Line data={data} options={options} />;
}

function ChartOcioso({ ped, horarios }) {
  const cenarios = [1, 2, 3, 4, 5];
  const cores = [
    'rgba(255, 99, 132, 1)', 
    'rgba(54, 162, 235, 1)', 
    'rgba(255, 206, 86, 1)', 
    'rgba(75, 192, 192, 1)', 
    'rgba(153, 102, 255, 1)'
  ];

  const data = {
    labels: horarios,
    datasets: cenarios.map(c => ({
      label: `${c} ${c > 1 ? 'posições' : 'posição'}`,
      data: horarios.map(h => ped.dados_horarios[h]?.[`P(0)_c${c}`]),
      borderColor: cores[c - 1],
      tension: 0.1,
      fill: false
    }))
  };

  const options = {
    plugins: { 
      title: { 
        display: true, 
        text: 'Probabilidade das Baias Estarem Vazias', 
        font: { size: 16 } 
      } 
    }
  };

  return <Line data={data} options={options} />;
}