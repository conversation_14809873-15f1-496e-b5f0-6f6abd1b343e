import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { fetchEmpresas, fetchProjetos, uploadContagem, analisarTiposVeiculosExcel, confirmarClassificacaoEImportar, fileToBase64 } from '../api/ccvImportService';
import { uploadGeojson, createProjeto, checkProjectData } from '../api/geojsonService';
import { uploadPedData } from '../api/pedImportService';
import { confirmarImportacaoVIA } from '../api/viaService';
import ViaImportForm from '../components/ViaImportForm';
import VehicleClassificationModal from '../components/VehicleClassificationModal'; // NOVA IMPORTAÇÃO

// Configuração dos produtos disponíveis - ATUALIZADA
const PRODUTOS_CONFIG = {
  PED: {
    nome: 'PED - Pontos de Embarque e Desembarque',
    icone: 'fas fa-bus',
    cor: 'blue',
    descricao: 'Importação de dados de pesquisa operacional em pontos de ônibus',
    formatos: ['Excel (.xlsx)'],
    campos: ['nome_ped', 'hora', 'embarque', 'desembarque', 'onibus', 'travessias'],
    exemploArquivo: 'ped_dados_exemplo.xlsx'
  },
  CCV: {
    nome: 'CCV - Contagem Classificada de Veículos',
    icone: 'fas fa-car',
    cor: 'green',
    descricao: 'Importação de estrutura (GeoJSON) e dados de contagem (Excel)',
    formatos: ['GeoJSON (.geojson)', 'Excel (.xlsx)'],
    campos: ['ponto', 'data', 'hora', 'movimento', 'tipo_veiculo', 'qte'],
    exemploArquivo: 'ccv_dados_exemplo.xlsx'
  },
  VIA: {
    nome: 'VIA - Sistema Viário',
    icone: 'fas fa-road',
    cor: 'indigo',
    descricao: 'Importação de redes viárias do Aimsun com indicadores personalizáveis',
    formatos: ['GeoJSON (.geojson)'],
    campos: ['Configurável baseado nos atributos da rede'],
    exemploArquivo: 'rede_aimsun.geojson'
  },
  FTP: {
    nome: 'FTP - Fluxo de Travessias de Pedestres',
    icone: 'fas fa-walking',
    cor: 'orange',
    descricao: 'Importação de dados de contagem de pedestres em travessias',
    formatos: ['Excel (.xlsx)', 'GeoJSON (.geojson)'],
    campos: ['ponto', 'data', 'hora', 'direcao', 'tipo_pedestre', 'quantidade'],
    exemploArquivo: 'ftp_dados_exemplo.xlsx',
    status: 'em_breve'
  },
  TLP: {
    nome: 'TLP - Tempo de Viagem em Links e Percursos',
    icone: 'fas fa-route',
    cor: 'purple',
    descricao: 'Importação de dados de tempo de viagem e velocidades',
    formatos: ['Excel (.xlsx)', 'GPX (.gpx)'],
    campos: ['segmento', 'origem', 'destino', 'tempo_viagem', 'velocidade'],
    exemploArquivo: 'tlp_dados_exemplo.xlsx',
    status: 'em_breve'
  },
  POD: {
    nome: 'POD - Pesquisa Origem-Destino',
    icone: 'fas fa-map-marked-alt',
    cor: 'red',
    descricao: 'Importação de dados de pesquisa origem-destino domiciliar',
    formatos: ['Excel (.xlsx)', 'CSV (.csv)'],
    campos: ['zona_origem', 'zona_destino', 'modo_transporte', 'motivo_viagem'],
    exemploArquivo: 'pod_dados_exemplo.xlsx',
    status: 'em_breve'
  }
};

const cores = {
  blue: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800', button: 'bg-blue-600 hover:bg-blue-700' },
  green: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800', button: 'bg-green-600 hover:bg-green-700' },
  indigo: { bg: 'bg-indigo-50', border: 'border-indigo-200', text: 'text-indigo-800', button: 'bg-indigo-600 hover:bg-indigo-700' },
  orange: { bg: 'bg-orange-50', border: 'border-orange-200', text: 'text-orange-800', button: 'bg-orange-600 hover:bg-orange-700' },
  purple: { bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-800', button: 'bg-purple-600 hover:bg-purple-700' },
  red: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800', button: 'bg-red-600 hover:bg-red-700' }
};

export default function CcvImport() {
    const navigate = useNavigate();
    
    // Estados da UI
    const [produtoSelecionado, setProdutoSelecionado] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [errorDetails, setErrorDetails] = useState([]);
    const [success, setSuccess] = useState('');
    const [activeTab, setActiveTab] = useState('estrutura');
    const [showNewProject, setShowNewProject] = useState(false);

    // NOVOS ESTADOS PARA CLASSIFICAÇÃO DE TIPOS
    const [showClassificationModal, setShowClassificationModal] = useState(false);
    const [analysisData, setAnalysisData] = useState(null);
    const [currentFile, setCurrentFile] = useState(null);

    // Estados dos Dados
    const [empresas, setEmpresas] = useState([]);
    const [projetos, setProjetos] = useState([]);
    const [selectedEmpresa, setSelectedEmpresa] = useState('');
    const [selectedProject, setSelectedProject] = useState('');
    
    // Estados dos Formulários
    const [newProjectName, setNewProjectName] = useState('');
    const [newProjectCode, setNewProjectCode] = useState('');
    const [pontosFile, setPontosFile] = useState(null);
    const [movimentosFile, setMovimentosFile] = useState(null);
    const [dadosFile, setDadosFile] = useState(null);

    // Carregamento inicial de empresas
    useEffect(() => {
        const loadEmpresas = async () => {
            try {
                const data = await fetchEmpresas();
                setEmpresas(data.empresas || []);
            } catch (err) {
                setError('Falha ao carregar empresas.');
            }
        };
        loadEmpresas();
    }, []);

    // Carregar projetos quando uma empresa é selecionada
    useEffect(() => {
        if (!selectedEmpresa) {
            setProjetos([]);
            setSelectedProject('');
            return;
        }
        const loadProjetos = async () => {
            setLoading(true);
            try {
                const data = await fetchProjetos(selectedEmpresa);
                setProjetos(data.projetos || []);
            } catch (err) {
                setError('Falha ao carregar projetos.');
            } finally {
                setLoading(false);
            }
        };
        loadProjetos();
    }, [selectedEmpresa]);

    const resetMessages = () => {
        setError('');
        setErrorDetails([]);
        setSuccess('');
    };

    const handleCreateProject = async (e) => {
        e.preventDefault();
        resetMessages();
        if (!newProjectName || !newProjectCode || !selectedEmpresa) {
            setError('Preencha todos os campos para criar um novo projeto.');
            return;
        }
        setLoading(true);
        try {
            const result = await createProjeto({
                nome_projeto: newProjectName,
                codigo_projeto: newProjectCode,
                empresa_id: selectedEmpresa,
            });
            const newProject = result.projeto;
            setProjetos(prev => [...prev, newProject]);
            setSelectedProject(newProject.id);
            setSuccess(`Projeto "${newProject.nome_projeto}" criado com sucesso!`);
            setShowNewProject(false);
            setNewProjectName('');
            setNewProjectCode('');
        } catch (err) {
            setError(err.error || 'Falha ao criar o projeto.');
        } finally {
            setLoading(false);
        }
    };

    // Função para importação de dados PED
    const handlePedImport = async (e) => {
        e.preventDefault();
        resetMessages();
        if (!selectedProject || !dadosFile) {
            setError('Selecione um projeto e o arquivo Excel de dados PED.');
            return;
        }
        setLoading(true);
        try {
            const result = await uploadPedData({
                projetoId: selectedProject,
                dadosFile: dadosFile
            });
            setSuccess(result.message);
            if (result.detalhes) {
                console.log('Detalhes da importação PED:', result.detalhes);
            }
        } catch (err) {
            setError(err.error || 'Falha na importação PED.');
            if (err.details) setErrorDetails(err.details);
        } finally {
            setLoading(false);
        }
    };

    // Função para importação GeoJSON (CCV)
    const handleGeoJsonUpload = async (e, overwrite = false) => {
        e.preventDefault();
        resetMessages();
        if (!selectedProject || !pontosFile) {
            setError('Selecione um projeto e um arquivo de pontos GeoJSON.');
            return;
        }
        if (!overwrite) {
            const hasData = await checkProjectData(selectedProject);
            if (hasData) {
                if (window.confirm('Este projeto já contém dados. Deseja sobrescrevê-los?')) {
                    await handleGeoJsonUpload(e, true);
                }
                return;
            }
        }
        setLoading(true);
        try {
            const result = await uploadGeojson({
                projetoId: selectedProject,
                pontosFile,
                movimentosFile,
                overwrite
            });
            setSuccess(result.message);
        } catch (err) {
            setError(err.error || 'Falha no upload do GeoJSON.');
        } finally {
            setLoading(false);
        }
    };

    // NOVA FUNÇÃO: Análise de tipos de veículos antes da importação
    const handleAnaliseTipos = async (e) => {
        e.preventDefault();
        resetMessages();
        if (!selectedProject || !dadosFile) {
            setError('Selecione um projeto e o arquivo Excel de contagem.');
            return;
        }
        
        setLoading(true);
        try {
            const result = await analisarTiposVeiculosExcel(selectedProject, dadosFile);
            setAnalysisData(result);
            setCurrentFile(dadosFile);
            
            // Se não há tipos para classificar, importa direto
            if (result.tipos_para_classificar.length === 0) {
                setSuccess('Todos os tipos de veículos já existem. Importando dados...');
                await handleContagemUpload(e);
            } else {
                setShowClassificationModal(true);
            }
        } catch (err) {
            setError(err.error || 'Falha na análise do arquivo.');
            if (err.details) setErrorDetails(err.details);
        } finally {
            setLoading(false);
        }
    };

    // NOVA FUNÇÃO: Confirmação da classificação e importação
    const handleConfirmClassification = async (tiposClassificados) => {
        setLoading(true);
        try {
            // Converte o arquivo para base64
            const arquivoBase64 = await fileToBase64(currentFile);
            
            const result = await confirmarClassificacaoEImportar(
                selectedProject, 
                tiposClassificados, 
                arquivoBase64
            );
            
            setSuccess(result.message);
            setShowClassificationModal(false);
            setAnalysisData(null);
            setCurrentFile(null);
            
            if (result.detalhes) {
                console.log('Detalhes da importação:', result.detalhes);
            }
        } catch (err) {
            setError(err.error || 'Falha na importação com classificação.');
            if (err.details) setErrorDetails(err.details);
        } finally {
            setLoading(false);
        }
    };

    // Função para importação de dados CCV (método tradicional - mantida para compatibilidade)
    const handleContagemUpload = async (e) => {
        e.preventDefault();
        resetMessages();
        if (!selectedProject || !dadosFile) {
            setError('Selecione um projeto e o arquivo Excel de contagem.');
            return;
        }
        setLoading(true);
        try {
            const result = await uploadContagem(selectedProject, dadosFile);
            setSuccess(result.message);
        } catch (err) {
            setError(err.error || 'Falha no upload da contagem.');
            if (err.details) setErrorDetails(err.details);
        } finally {
            setLoading(false);
        }
    };

    // Handler para importação VIA
    const handleViaImport = async (importData) => {
        resetMessages();
        setLoading(true);
        try {
            const result = await confirmarImportacaoVIA(importData);
            setSuccess(result.message);
            if (result.projeto_via_id) {
                console.log('Projeto VIA criado:', result.projeto_via_id);
            }
        } catch (err) {
            setError(err.error || 'Falha na importação VIA.');
            if (err.details) setErrorDetails(err.details);
        } finally {
            setLoading(false);
        }
    };

    const voltarParaSeleção = () => {
        setProdutoSelecionado(null);
        resetMessages();
        setActiveTab('estrutura');
        setShowClassificationModal(false);
        setAnalysisData(null);
        setCurrentFile(null);
    };

    if (!produtoSelecionado) {
        return (
            <div className="min-h-screen bg-gray-100 flex flex-col">
                <header className="bg-white shadow-md">
                    <nav className="container mx-auto px-6 py-3 flex justify-between items-center">
                        <div className="flex items-center">
                            <img src="/images/logo2.svg" alt="Logo" className="h-10" />
                            <h1 className="text-xl font-bold text-gray-700 ml-4">Sistema de Importação</h1>
                        </div>
                        <button onClick={() => navigate('/ccv')} className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                            <i className="fas fa-arrow-left mr-2"></i>Voltar
                        </button>
                    </nav>
                </header>

                <main className="flex-grow container mx-auto p-6">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold text-gray-800 mb-4">Selecione o Produto para Importação</h1>
                            <p className="text-lg text-gray-600">Escolha o tipo de dados que deseja importar para o sistema</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {Object.entries(PRODUTOS_CONFIG).map(([codigo, config]) => {
                                const corConfig = cores[config.cor];
                                const isDisponivel = !config.status || config.status !== 'em_breve';
                                
                                return (
                                    <div 
                                        key={codigo}
                                        className={`${corConfig.bg} ${corConfig.border} border-2 rounded-xl p-6 transition-all duration-200 ${
                                            isDisponivel 
                                                ? 'hover:shadow-lg cursor-pointer transform hover:-translate-y-1' 
                                                : 'opacity-60 cursor-not-allowed'
                                        }`}
                                        onClick={() => isDisponivel && setProdutoSelecionado(codigo)}
                                    >
                                        <div className="text-center">
                                            <div className={`${corConfig.text} text-4xl mb-4`}>
                                                <i className={config.icone}></i>
                                            </div>
                                            <h3 className={`text-xl font-bold ${corConfig.text} mb-3`}>
                                                {config.nome}
                                            </h3>
                                            <p className="text-gray-700 text-sm mb-4 h-12">
                                                {config.descricao}
                                            </p>
                                            
                                            <div className="space-y-2 text-xs text-gray-600">
                                                <div>
                                                    <span className="font-medium">Formatos:</span> {config.formatos.join(', ')}
                                                </div>
                                                <div>
                                                    <span className="font-medium">Campos:</span> {config.campos.length} campos obrigatórios
                                                </div>
                                            </div>

                                            {config.status === 'em_breve' && (
                                                <div className="mt-4 inline-block bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">
                                                    <i className="fas fa-clock mr-1"></i>Em Breve
                                                </div>
                                            )}
                                            
                                            {isDisponivel && (
                                                <div className="mt-4">
                                                    <div className={`${corConfig.button} text-white text-sm px-4 py-2 rounded-lg transition-colors inline-block`}>
                                                        <i className="fas fa-upload mr-2"></i>Importar
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        <div className="mt-12 bg-white rounded-xl shadow-md p-6">
                            <h2 className="text-xl font-bold text-gray-800 mb-4">
                                <i className="fas fa-info-circle mr-2"></i>Informações Importantes
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                                <div>
                                    <h3 className="font-semibold text-gray-800 mb-2">Formatos Suportados</h3>
                                    <ul className="space-y-1">
                                        <li><strong>Excel (.xlsx):</strong> Para dados tabulares</li>
                                        <li><strong>GeoJSON (.geojson):</strong> Para dados geográficos</li>
                                        <li><strong>CSV (.csv):</strong> Para dados tabulares simples</li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-gray-800 mb-2">Processo de Importação</h3>
                                    <ul className="space-y-1">
                                        <li>1. Selecione o produto desejado</li>
                                        <li>2. Escolha o projeto de destino</li>
                                        <li>3. Faça upload dos arquivos</li>
                                        <li>4. Valide e confirme a importação</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        );
    }

    // Interface específica para o produto selecionado
    const config = PRODUTOS_CONFIG[produtoSelecionado];
    const corConfig = cores[config.cor];

    return (
        <div className="min-h-screen bg-gray-100 flex flex-col">
            <header className="bg-white shadow-md">
                <nav className="container mx-auto px-6 py-3 flex justify-between items-center">
                    <div className="flex items-center">
                        <img src="/images/logo2.svg" alt="Logo" className="h-10" />
                        <h1 className="text-xl font-bold text-gray-700 ml-4">
                            Importação - {config.nome}
                        </h1>
                    </div>
                    <div className="flex items-center space-x-3">
                        <button 
                            onClick={voltarParaSeleção} 
                            className="bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
                        >
                            <i className="fas fa-arrow-left mr-2"></i>Trocar Produto
                        </button>
                        <button 
                            onClick={() => navigate('/ccv')} 
                            className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                        >
                            <i className="fas fa-home mr-2"></i>Dashboard
                        </button>
                    </div>
                </nav>
            </header>

            <main className="flex-grow container mx-auto p-6">
                <div className="max-w-2xl mx-auto">
                    
                    {/* Header do produto */}
                    <div className={`${corConfig.bg} ${corConfig.border} border-2 rounded-xl p-6 mb-6`}>
                        <div className="text-center">
                            <div className={`${corConfig.text} text-3xl mb-3`}>
                                <i className={config.icone}></i>
                            </div>
                            <h2 className={`text-2xl font-bold ${corConfig.text} mb-2`}>
                                {config.nome}
                            </h2>
                            <p className="text-gray-700">{config.descricao}</p>
                        </div>
                    </div>

                    {/* Mensagens */}
                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                            <strong className="font-bold">Erro:</strong>
                            <span className="block sm:inline ml-2">{error}</span>
                            {errorDetails.length > 0 && (
                                <ul className="mt-2 list-disc list-inside text-sm">{errorDetails.map((d, i) => <li key={i}>{d}</li>)}</ul>
                            )}
                        </div>
                    )}
                    {success && <div className="bg-green-100 border-green-400 text-green-700 px-4 py-3 rounded mb-4">{success}</div>}
                    
                    <div className="bg-white rounded-xl shadow-lg p-8 space-y-6">
                        
                        {/* Seleção de projeto (comum a todos os produtos) */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">1. Selecione o Projeto de Destino</h3>
                            <div className="space-y-4">
                                <select value={selectedEmpresa} onChange={(e) => setSelectedEmpresa(e.target.value)} className="w-full p-2 border-gray-300 rounded-md">
                                    <option value="">-- Selecione uma Empresa --</option>
                                    {empresas.map(e => <option key={e.id} value={e.id}>{e.nome_empresa}</option>)}
                                </select>
                                {selectedEmpresa && (
                                    <div className="flex gap-2">
                                        <select value={selectedProject} onChange={(e) => setSelectedProject(e.target.value)} disabled={showNewProject} className="w-full p-2 border-gray-300 rounded-md disabled:bg-gray-200">
                                            <option value="">-- Selecione um Projeto --</option>
                                            {projetos.map(p => <option key={p.id} value={p.id}>{p.codigo_projeto} - {p.nome_projeto}</option>)}
                                        </select>
                                        <button type="button" onClick={() => setShowNewProject(!showNewProject)} className="px-4 py-2 bg-gray-600 text-white rounded-md whitespace-nowrap">
                                            {showNewProject ? 'Cancelar' : 'Novo Projeto'}
                                        </button>
                                    </div>
                                )}
                                {showNewProject && (
                                    <form onSubmit={handleCreateProject} className="border p-4 rounded-md space-y-3 bg-gray-50">
                                        <h4 className="font-semibold">Criar Novo Projeto</h4>
                                        <input type="text" placeholder="Nome do Novo Projeto" value={newProjectName} onChange={(e) => setNewProjectName(e.target.value)} className="w-full p-2 border-gray-300 rounded-md" />
                                        <input type="text" placeholder="Código do Novo Projeto" value={newProjectCode} onChange={(e) => setNewProjectCode(e.target.value)} className="w-full p-2 border-gray-300 rounded-md" />
                                        <button type="submit" disabled={loading} className="w-full p-2 bg-indigo-600 text-white rounded-md disabled:bg-indigo-300">
                                            {loading ? 'Criando...' : 'Salvar Projeto'}
                                        </button>
                                    </form>
                                )}
                            </div>
                        </div>

                        {/* Formulários específicos por produto */}
                        {produtoSelecionado === 'PED' && (
                            <PedImportForm 
                                onSubmit={handlePedImport}
                                dadosFile={dadosFile}
                                setDadosFile={setDadosFile}
                                loading={loading}
                                selectedProject={selectedProject}
                                config={config}
                                corConfig={corConfig}
                            />
                        )}

                        {produtoSelecionado === 'CCV' && (
                            <CcvImportForm 
                                onGeoJsonSubmit={handleGeoJsonUpload}
                                onDadosSubmit={handleContagemUpload}
                                onAnaliseTipos={handleAnaliseTipos} // NOVA PROP
                                pontosFile={pontosFile}
                                setPontosFile={setPontosFile}
                                movimentosFile={movimentosFile}
                                setMovimentosFile={setMovimentosFile}
                                dadosFile={dadosFile}
                                setDadosFile={setDadosFile}
                                loading={loading}
                                selectedProject={selectedProject}
                                activeTab={activeTab}
                                setActiveTab={setActiveTab}
                                config={config}
                                corConfig={corConfig}
                            />
                        )}

                        {/* Formulário VIA */}
                        {produtoSelecionado === 'VIA' && (
                            <ViaImportForm 
                                onAnalyzeGeoJson={() => {}}
                                onConfirmImport={handleViaImport}
                                selectedProject={selectedProject}
                                loading={loading}
                                config={config}
                                corConfig={corConfig}
                            />
                        )}

                        {produtoSelecionado === 'FTP' && (
                            <FutureProductCard 
                                config={config}
                                corConfig={corConfig}
                                message="O módulo FTP (Fluxo de Travessias de Pedestres) está em desenvolvimento. Em breve você poderá importar dados de contagem de pedestres."
                            />
                        )}

                        {['TLP', 'POD'].includes(produtoSelecionado) && (
                            <FutureProductCard 
                                config={config}
                                corConfig={corConfig}
                                message={`O módulo ${config.nome} está em desenvolvimento. Fique atento às próximas atualizações do sistema!`}
                            />
                        )}
                    </div>
                </div>
            </main>

            {/* NOVO: Modal de Classificação de Tipos de Veículos */}
            <VehicleClassificationModal
                isOpen={showClassificationModal}
                onClose={() => {
                    setShowClassificationModal(false);
                    setAnalysisData(null);
                    setCurrentFile(null);
                }}
                analysisData={analysisData}
                onConfirmClassification={handleConfirmClassification}
                loading={loading}
            />
        </div>
    );
}

// Componente para importação PED
function PedImportForm({ onSubmit, dadosFile, setDadosFile, loading, selectedProject, config, corConfig }) {
    return (
        <div>
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">2. Importar Dados PED</h3>
            <form onSubmit={onSubmit}>
                <div className="space-y-4">
                    <div className={`${corConfig.bg} p-4 rounded-lg border border-gray-200`}>
                        <h4 className="font-semibold text-gray-800 mb-2">Formato do Arquivo Excel</h4>
                        <p className="text-sm text-gray-600 mb-3">
                            O arquivo deve conter as seguintes colunas obrigatórias:
                        </p>
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-700">
                            {config.campos.map(campo => (
                                <div key={campo} className="flex items-center">
                                    <i className="fas fa-check text-green-600 mr-2"></i>
                                    <code className="bg-gray-100 px-1 rounded">{campo}</code>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                            Arquivo de Dados PED (Excel)
                        </label>
                        <input 
                            type="file" 
                            onChange={(e) => setDadosFile(e.target.files[0])} 
                            accept=".xlsx,.xls" 
                            className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                    </div>
                </div>
                <div className="mt-6 border-t pt-4">
                    <button 
                        type="submit" 
                        disabled={loading || !selectedProject || !dadosFile} 
                        className={`w-full p-3 ${corConfig.button} text-white rounded-md disabled:bg-gray-400 transition-colors`}
                    >
                        {loading ? 'Importando...' : 'Importar Dados PED'}
                    </button>
                </div>
            </form>
        </div>
    );
}

// Componente para importação CCV
function CcvImportForm({ 
    onGeoJsonSubmit, 
    onDadosSubmit, 
    onAnaliseTipos, // NOVA PROP
    pontosFile, 
    setPontosFile, 
    movimentosFile, 
    setMovimentosFile, 
    dadosFile, 
    setDadosFile, 
    loading, 
    selectedProject, 
    activeTab, 
    setActiveTab,
    config,
    corConfig 
}) {
    return (
        <div>
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">2. Selecione o Tipo de Importação</h3>
            
            {/* Seleção de abas */}
            <div className="flex border-b mb-6">
                <button 
                    onClick={() => setActiveTab('estrutura')} 
                    className={`py-2 px-4 text-sm font-medium ${activeTab === 'estrutura' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500'}`}
                >
                    Importar Estrutura (GeoJSON)
                </button>
                <button 
                    onClick={() => setActiveTab('dados')} 
                    className={`py-2 px-4 text-sm font-medium ${activeTab === 'dados' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500'}`}
                >
                    Importar Dados (Excel)
                </button>
            </div>

            {/* Conteúdo das abas */}
            {activeTab === 'estrutura' && (
                <form onSubmit={onGeoJsonSubmit}>
                    <div className={`${corConfig.bg} p-4 rounded-lg border border-gray-200 mb-4`}>
                        <p className="text-sm text-gray-600">
                            Use esta opção para criar ou substituir a estrutura de um projeto, importando os pontos e movimentos a partir de arquivos GeoJSON.
                        </p>
                    </div>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-600 mb-2">Arquivo de Pontos (GeoJSON)</label>
                            <input type="file" onChange={(e) => setPontosFile(e.target.files[0])} accept=".geojson,.json" className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-green-50 file:text-green-700"/>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-600 mb-2">Arquivo de Movimentos (GeoJSON)</label>
                            <input type="file" onChange={(e) => setMovimentosFile(e.target.files[0])} accept=".geojson,.json" className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-green-50 file:text-green-700"/>
                        </div>
                    </div>
                    <div className="mt-6 border-t pt-4">
                        <button type="submit" disabled={loading || !selectedProject} className={`w-full p-3 ${corConfig.button} text-white rounded-md disabled:bg-gray-400`}>
                            {loading ? 'Importando...' : 'Importar Estrutura'}
                        </button>
                    </div>
                </form>
            )}

            {activeTab === 'dados' && (
                <div>
                    <div className={`${corConfig.bg} p-4 rounded-lg border border-gray-200 mb-4`}>
                        <h4 className="font-semibold text-gray-800 mb-2">
                            <i className="fas fa-magic mr-2"></i>
                            Importação Inteligente de Tipos de Veículos
                        </h4>
                        <p className="text-sm text-gray-600 mb-3">
                            O sistema analisará automaticamente os tipos de veículos no arquivo Excel e permitirá que você os classifique antes da importação.
                        </p>
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-700">
                            {config.campos.map(campo => (
                                <div key={campo} className="flex items-center">
                                    <i className="fas fa-check text-green-600 mr-2"></i>
                                    <code className="bg-gray-100 px-1 rounded">{campo}</code>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-600 mb-2">
                                <i className="fas fa-file-excel mr-2 text-green-600"></i>
                                Arquivo de Contagem (Excel)
                            </label>
                            <input 
                                type="file" 
                                onChange={(e) => setDadosFile(e.target.files[0])} 
                                accept=".xlsx,.xls" 
                                className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-green-50 file:text-green-700"
                            />
                            {dadosFile && (
                                <p className="text-xs text-gray-600 mt-1">
                                    <i className="fas fa-info-circle mr-1"></i>
                                    Arquivo selecionado: {dadosFile.name}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="mt-6 border-t pt-4 space-y-3">
                        {/* NOVO: Botão Principal de Análise */}
                        <button 
                            onClick={onAnaliseTipos}
                            disabled={loading || !selectedProject || !dadosFile} 
                            className={`w-full p-3 ${corConfig.button} text-white rounded-md disabled:bg-gray-400 transition-colors`}
                        >
                            {loading ? (
                                <>
                                    <i className="fas fa-spinner fa-spin mr-2"></i>
                                    Analisando Tipos de Veículos...
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-search mr-2"></i>
                                    Analisar e Importar Dados
                                </>
                            )}
                        </button>

                        {/* Botão secundário para importação direta (para usuários avançados) */}
                        <details className="text-sm">
                            <summary className="cursor-pointer text-gray-600 hover:text-gray-800 font-medium">
                                <i className="fas fa-cogs mr-2"></i>
                                Opções Avançadas
                            </summary>
                            <div className="mt-3 p-3 bg-gray-50 rounded border">
                                <p className="text-xs text-gray-600 mb-3">
                                    <i className="fas fa-exclamation-triangle text-orange-500 mr-1"></i>
                                    <strong>Modo Avançado:</strong> Importa diretamente sem análise prévia. Use apenas se todos os tipos de veículos já existem no sistema.
                                </p>
                                <button 
                                    onClick={onDadosSubmit}
                                    disabled={loading || !selectedProject || !dadosFile} 
                                    className="w-full p-2 bg-gray-600 text-white rounded-md disabled:bg-gray-400 hover:bg-gray-700 transition-colors text-sm"
                                >
                                    <i className="fas fa-upload mr-2"></i>
                                    Importação Direta (Sem Análise)
                                </button>
                            </div>
                        </details>
                    </div>
                </div>
            )}
        </div>
    );
}

// Componente para produtos em desenvolvimento
function FutureProductCard({ config, corConfig, message }) {
    return (
        <div>
            <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">2. Status do Produto</h3>
            
            <div className={`${corConfig.bg} border-2 ${corConfig.border} rounded-xl p-6 text-center`}>
                <div className={`${corConfig.text} text-4xl mb-4`}>
                    <i className="fas fa-clock"></i>
                </div>
                
                <h4 className={`text-xl font-bold ${corConfig.text} mb-3`}>
                    Em Desenvolvimento
                </h4>
                
                <p className="text-gray-700 mb-6">
                    {message}
                </p>
                
                <div className="space-y-4 text-sm text-gray-600">
                    <div className={`bg-white p-4 rounded-lg border border-gray-200`}>
                        <h5 className="font-semibold text-gray-800 mb-2">Recursos Planejados:</h5>
                        <ul className="space-y-1 text-left">
                            <li><i className="fas fa-check-circle text-green-500 mr-2"></i>Importação via {config.formatos.join(' e ')}</li>
                            <li><i className="fas fa-check-circle text-green-500 mr-2"></i>Validação automática de dados</li>
                            <li><i className="fas fa-check-circle text-green-500 mr-2"></i>Visualização interativa</li>
                            <li><i className="fas fa-check-circle text-green-500 mr-2"></i>Relatórios automatizados</li>
                        </ul>
                    </div>
                    
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center justify-center">
                            <i className="fas fa-info-circle text-yellow-600 mr-2"></i>
                            <span className="text-yellow-800 font-medium">
                                Previsão de disponibilidade: Próximas versões
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}