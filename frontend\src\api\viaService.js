import axios from 'axios';

const API = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // Timeout maior para uploads
});

// Interceptor de erros para logging
API.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('❌ VIA API Error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      responseData: error.response?.data
    });
    return Promise.reject(error.response?.data || error);
  }
);

/**
 * Busca projetos VIA de uma empresa
 */
export async function fetchProjetosVIA(empresaId) {
  try {
    const response = await API.get('/api/via/projetos', { 
      params: { empresa_id: empresaId } 
    });
    return response.data.projetos_via || [];
  } catch (error) {
    console.error("Erro ao buscar projetos VIA:", error);
    throw error;
  }
}

/**
 * Faz upload e análise do GeoJSON
 */
export async function uploadGeoJsonVIA(formData) {
  try {
    const response = await API.post('/api/via/upload-geojson', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error("Erro no upload GeoJSON VIA:", error);
    throw error;
  }
}

/**
 * Confirma a importação com configurações
 */
export async function confirmarImportacaoVIA(importData) {
  try {
    const response = await API.post('/api/via/confirmar-importacao', importData);
    return response.data;
  } catch (error) {
    console.error("Erro na confirmação de importação VIA:", error);
    throw error;
  }
}

/**
 * Busca dados da rede VIA para visualização
 */
export async function fetchDadosVIA(projetoViaId) {
  try {
    const response = await API.get(`/api/via/projeto/${projetoViaId}/dados`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar dados VIA:", error);
    throw error;
  }
}