# backend/app.py - VERSÃO COM ROTAS DE ADMINISTRAÇÃO

import os
import pandas as pd
import numpy as np
import math
from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import text
import traceback
import json

# --- 1. CONFIGURAÇÃO DA APLICAÇÃO ---

# Inicializa a aplicação Flask
app = Flask(__name__)

# Configuração de CORS 
CORS(app, resources={
    r"/api/*": {
        "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "Accept"],
        "supports_credentials": True
    },
    r"/*": {
        "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
        "methods": ["GET", "OPTIONS"],
        "allow_headers": ["Content-Type", "Accept"],
    }
})

# Carrega as configurações do arquivo .env
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JSON_AS_ASCII'] = False

# Inicializa a extensão SQLAlchemy para interagir com o banco de dados
db = SQLAlchemy(app)


# --- MODIFICAÇÃO ESSENCIAL: Importação unificada ---
try:
    # Importa todos os modelos do arquivo unificado
    import models 
    print("✅ Modelos unificados importados com sucesso")

    # Importa e registra o blueprint do CCV (que agora usa os modelos unificados)
    from routes_ccv import ccv_bp
    app.register_blueprint(ccv_bp)
    print("✅ Blueprint CCV registrado com sucesso")
    
    # Importa e registra o blueprint do GeoJSON
    from routes_geojson import geojson_bp
    app.register_blueprint(geojson_bp)
    print("✅ Blueprint GeoJSON registrado com sucesso")
    
    # Importa e registra o blueprint do PED
    from routes_ped import ped_bp
    app.register_blueprint(ped_bp)
    print("✅ Blueprint PED registrado com sucesso")

    # Importa e registra o blueprint do VIA
    from routes_via import via_bp
    app.register_blueprint(via_bp)
    print("✅ Blueprint VIA registrado com sucesso")

    # NOVO: Importa e registra o blueprint de administração
    from routes_admin import admin_bp
    app.register_blueprint(admin_bp)
    print("✅ Blueprint ADMIN registrado com sucesso")

except Exception as e:
    print(f"❌ Erro fatal ao carregar módulos ou registrar blueprints: {e}")
    # Define 'models' como None para que as rotas saibam da falha
    models = None
# --- FIM DA MODIFICAÇÃO ---


# Novo comando CLI para setup do CCV:
@app.cli.command("setup-ccv")
def setup_ccv_command():
    """Setup completo do produto CCV"""
    # Este script (setup_ccv.py) já foi corrigido para usar o modelo unificado
    from setup_ccv import setup_ccv
    setup_ccv()


# --- 2. FUNÇÃO PARA LIMPAR VALORES NaN/Infinity ---
def clean_numeric_value(value):
    """
    Converte valores NaN, Infinity e None para valores JSON-safe.
    """
    if value is None:
        return None
    
    # Verifica se é um número
    if isinstance(value, (int, float, np.integer, np.floating)):
        # Converte numpy types para Python natives
        if isinstance(value, (np.integer,)):
            value = int(value)
        elif isinstance(value, (np.floating,)):
            value = float(value)
        
        # Verifica NaN e Infinity
        if math.isnan(value):
            return None
        elif math.isinf(value):
            return None
        else:
            return value
    
    return value

def clean_data_dict(data_dict):
    """
    Recursivamente limpa um dicionário de valores NaN/Infinity.
    """
    if isinstance(data_dict, dict):
        cleaned = {}
        for key, value in data_dict.items():
            cleaned[key] = clean_data_dict(value)
        return cleaned
    elif isinstance(data_dict, list):
        return [clean_data_dict(item) for item in data_dict]
    else:
        return clean_numeric_value(data_dict)

# --- 3. MIDDLEWARE ADICIONAL PARA CORS ---
@app.after_request
def after_request(response):
    """
    Middleware que antes adicionava headers CORS manualmente, causando duplicidade.
    A extensão Flask-CORS já gerencia isso de forma correta.
    Esta função foi mantida, mas as linhas que adicionam os cabeçalhos foram removidas.
    """
    # As linhas abaixo foram a causa do erro e devem ser removidas ou comentadas.
    # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:3000')
    # response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,Accept')
    # response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    # response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response

# --- 4. HANDLER GLOBAL PARA EXCEÇÕES ---
@app.errorhandler(Exception)
def handle_exception(e):
    """Captura todas as exceções e retorna JSON em vez de HTML."""
    print(f"🚨 EXCEÇÃO CAPTURADA: {type(e).__name__}: {str(e)}")
    print(f"🚨 TRACEBACK: {traceback.format_exc()}")
    
    status_code = getattr(e, 'code', 500)
    
    error_response = {
        "error": True,
        "message": str(e),
        "type": type(e).__name__,
        "status_code": status_code
    }
    
    return jsonify(error_response), status_code

# --- 5. ROTAS DA API ---

@app.route('/')
def hello():
    """Rota de teste para verificar se o servidor está no ar."""
    return '<h1>API do Backend da Plataforma de Tráfego está no ar!</h1>'

@app.route('/health')
def health_check():
    """Endpoint para verificação de saúde da API."""
    try:
        db.engine.execute(text('SELECT 1'))
        db_status = "connected"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    return jsonify({
        "status": "healthy",
        "message": "Backend da Plataforma de Tráfego funcionando",
        "cors": "enabled",
        "database": db_status,
        "models_loaded": models is not None,
        "nan_cleaning": "enabled",
        "produtos_disponiveis": ["PED", "CCV", "FTP", "TLP", "POD", "VIA"],
        "admin_available": True  # NOVO: Indica que as rotas de admin estão disponíveis
    })

@app.route('/api/dados')
def get_dados_ped():
    """Endpoint principal que serve os dados dos PEDs no formato JSON esperado pelo frontend."""
    print(">>> 🚀 INICIANDO get_dados_ped")
    
    try:
        if models is None:
            print("❌ Models não importados")
            return jsonify({
                "error": "Modelos de dados não foram carregados",
                "debug": "Verifique se o arquivo models.py está correto e se o banco de dados está configurado"
            }), 500
        
        print(">>> 📊 Consultando PEDs no banco de dados...")
        all_peds = models.Ped.query.options(joinedload(models.Ped.dados_horarios)).all()
        print(f">>> 📈 Encontrados {len(all_peds)} PEDs")

        if not all_peds:
            print("⚠️ Nenhum PED encontrado")
            return jsonify({
                "pedsData": {},
                "indicadores": {
                    'GS': 'Grau de Saturacao', 
                    'P(f)': 'Probabilidade de Fila', 
                    'P(0)': 'Probabilidade de Ponto Vazio',
                    'Passageiros': 'Proporcao Embarque/Desembarque', 
                    'Total Travessias': 'Total de Travessias',
                    'Placa': 'Presenca de Placa', 
                    'Abrigo': 'Presenca de Abrigo', 
                    'Baia': 'Presenca de Baia',
                    'Mancha Urbana': 'Dentro da Mancha Urbana', 
                    'SH': 'Segmento Homogeneo (SH)'
                },
                "debug": "Banco de dados está vazio - nenhum PED encontrado"
            })

        print(">>> 🔄 Processando dados dos PEDs...")
        peds_data_dict = {}
        
        for ped in all_peds:
            dados_horarios_formatados = {}
            
            for item in ped.dados_horarios:
                raw_data = {
                    "Embarque": item.embarque, "Desembarque": item.desembarque,
                    "Total": item.total, "Onibus": item.onibus,
                    "GS_c1": item.gs_c1, "P(f)_c1": item.pf_c1, "P(0)_c1": item.p0_c1,
                    "GS_c2": item.gs_c2, "P(f)_c2": item.pf_c2, "P(0)_c2": item.p0_c2,
                    "GS_c3": item.gs_c3, "P(f)_c3": item.pf_c3, "P(0)_c3": item.p0_c3,
                    "GS_c4": item.gs_c4, "P(f)_c4": item.pf_c4, "P(0)_c4": item.p0_c4,
                    "GS_c5": item.gs_c5, "P(f)_c5": item.pf_c5, "P(0)_c5": item.p0_c5,
                    "1 - 2": item.travessia_1_2, "2 - 1": item.travessia_2_1,
                }
                cleaned_data = clean_data_dict(raw_data)
                dados_horarios_formatados[item.hora] = cleaned_data
            
            peds_data_dict[ped.nome_ped] = {
                "Nome do PED": ped.nome_ped,
                "lat": clean_numeric_value(ped.latitude),
                "long": clean_numeric_value(ped.longitude),
                "placa": ped.placa,
                "abrigo": ped.abrigo,
                "baia": ped.baia,
                "mancha_urbana": ped.mancha_urbana,
                "sh": ped.sh,
                "km": ped.km,
                "sentido": ped.sentido,
                "dados_horarios": dados_horarios_formatados
            }

        payload = {
            "pedsData": peds_data_dict,
            "indicadores": {
                'GS': 'Grau de Saturacao', 'P(f)': 'Probabilidade de Fila', 
                'P(0)': 'Probabilidade de Ponto Vazio', 'Passageiros': 'Proporcao Embarque/Desembarque',
                'Total Travessias': 'Total de Travessias', 'Placa': 'Presenca de Placa', 
                'Abrigo': 'Presenca de Abrigo', 'Baia': 'Presenca de Baia',
                'Mancha Urbana': 'Dentro da Mancha Urbana', 'SH': 'Segmento Homogeneo (SH)'
            }
        }
        
        return jsonify(clean_data_dict(payload))

    except Exception as e:
        print(f"🚨 ERRO CRÍTICO em get_dados_ped: {type(e).__name__}: {str(e)}")
        print(f"🚨 TRACEBACK COMPLETO:\n{traceback.format_exc()}")
        return jsonify({"error": f"Erro interno do servidor: {str(e)}", "type": type(e).__name__}), 500

# Para criar um projeto via API
@app.route('/api/projetos', methods=['POST'])
def create_projeto():
    """Cria um novo projeto."""
    data = request.get_json()
    if not data or 'nome_projeto' not in data or 'codigo_projeto' not in data or 'empresa_id' not in data:
        return jsonify({'error': 'Dados insuficientes para criar projeto'}), 400

    try:
        # Verifica se o projeto já existe para a empresa
        existing_project = models.Projeto.query.filter_by(
            empresa_id=data['empresa_id'], 
            codigo_projeto=data['codigo_projeto']
        ).first()

        if existing_project:
            return jsonify({'error': 'Um projeto com este código já existe para a empresa selecionada'}), 409

        novo_projeto = models.Projeto(
            empresa_id=data['empresa_id'],
            nome_projeto=data['nome_projeto'],
            codigo_projeto=data['codigo_projeto'],
            descricao=data.get('descricao', '')
        )
        db.session.add(novo_projeto)
        db.session.commit()
        
        return jsonify({
            'message': 'Projeto criado com sucesso!',
            'projeto': {
                'id': novo_projeto.id,
                'nome_projeto': novo_projeto.nome_projeto,
                'codigo_projeto': novo_projeto.codigo_projeto
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Erro ao criar projeto: {e}")
        return jsonify({'error': str(e)}), 500
    

# --- 6. COMANDOS DE LINHA DE COMANDO (CLI) ---

@app.cli.command("db-create")
def create_tables():
    """Cria as tabelas do banco de dados com base nos modelos."""
    with app.app_context():
        db.create_all()
    print("Tabelas do banco de dados criadas com sucesso!")

@app.cli.command("import-ped")
def import_ped_data():
    """Lê o arquivo dadosped.xlsx e popula o banco de dados."""
    try:
        # 1. GARANTE QUE A EMPRESA PADRÃO EXISTA
        empresa_padrao = models.Empresa.query.filter_by(codigo_empresa='FRATAR').first()
        if not empresa_padrao:
            print("ERRO: Empresa padrão 'FRATAR' não encontrada. Execute 'flask setup-ccv' primeiro.")
            return

        # 2. CRIA O PROJETO PADRÃO ASSOCIADO À EMPRESA
        nome_projeto_padrao = "Levantamento PED - Carga Inicial"
        projeto = models.Projeto.query.filter_by(nome_projeto=nome_projeto_padrao).first()
        if not projeto:
            projeto = models.Projeto(
                empresa_id=empresa_padrao.id, # <-- CORREÇÃO: Adiciona o ID da empresa
                nome_projeto=nome_projeto_padrao, 
                codigo_projeto="PED_INICIAL"
            )
            db.session.add(projeto)
            db.session.commit()
        projeto_id_padrao = projeto.id
        print(f"Todos os PEDs serao associados ao projeto ID: {projeto_id_padrao}")

        # 3. LÊ A PLANILHA EXCEL
        df = pd.read_excel('dadosped.xlsx', engine='openpyxl')
        print(f"Arquivo Excel lido. {len(df)} linhas encontradas.")
        df.columns = [str(col).strip().lower().replace(' ', '_') for col in df.columns]
        df = df.rename(columns={"1_-_2": "travessia_1_2", "2_-_1": "travessia_2_1"})
        df = df.replace({np.nan: None})

        # Limpa dados antigos do projeto padrão
        models.Ped.query.filter_by(projeto_id=projeto_id_padrao).delete()
        db.session.commit()
        print("Dados de PED antigos do projeto padrao foram limpos.")

        # 4. CRIA OS PEDS
        peds_unicos = df.drop_duplicates(subset=['nome_do_ped']).copy()
        peds_unicos['latitude'] = peds_unicos['latitude'].astype(str).str.replace(',', '.').astype(float)
        peds_unicos['longitude'] = peds_unicos['longitude'].astype(str).str.replace(',', '.').astype(float)
        
        for _, row in peds_unicos.iterrows():
            novo_ped = models.Ped(
                projeto_id=projeto_id_padrao, nome_ped=row['nome_do_ped'], ponto=row.get('ponto'), 
                sh=row.get('sh'), km=row.get('km'), latitude=row['latitude'], longitude=row['longitude'], 
                sentido=row.get('sentido'), placa=row.get('placa'), abrigo=row.get('abrigo'), 
                baia=row.get('baia'), mancha_urbana=row.get('mancha_urbana')
            )
            db.session.add(novo_ped)
        db.session.commit()
        print(f"{len(peds_unicos)} PEDs unicos criados.")
        
        # 5. CRIA OS DADOS HORÁRIOS
        ped_map = {ped.nome_ped: ped.id for ped in models.Ped.query.all()}
        for _, row in df.iterrows():
            ped_id = ped_map.get(row['nome_do_ped'])
            if ped_id:
                dados_horario = models.PedDadosHorarios(
                    ped_id=ped_id, hora=str(row['hora']), intervalo=row.get('intervalo'), onibus=row.get('onibus'),
                    embarque=row.get('embarque'), desembarque=row.get('desembarque'), total=row.get('total'),
                    travessia_1_2=row.get('travessia_1_2'), travessia_2_1=row.get('travessia_2_1'),
                    gs_c1=row.get('gs_c1'), gs_c2=row.get('gs_c2'), gs_c3=row.get('gs_c3'), gs_c4=row.get('gs_c4'), gs_c5=row.get('gs_c5'),
                    p0_c1=row.get('p(0)_c1'), p0_c2=row.get('p(0)_c2'), p0_c3=row.get('p(0)_c3'), p0_c4=row.get('p(0)_c4'), p0_c5=row.get('p(0)_c5'),
                    pf_c1=row.get('p(f)_c1'), pf_c2=row.get('p(f)_c2'), pf_c3=row.get('p(f)_c3'), pf_c4=row.get('p(f)_c4'), pf_c5=row.get('p(f)_c5')
                )
                db.session.add(dados_horario)
        
        db.session.commit()
        print("Todos os dados horarios foram importados com sucesso!")

    except FileNotFoundError:
        print("ERRO: Arquivo 'dadosped.xlsx' não encontrado na pasta 'backend/'.")
    except KeyError as e:
        db.session.rollback()
        print(f"ERRO DE IMPORTACAO: A coluna {e} nao foi encontrada na planilha Excel.")
    except Exception as e:
        db.session.rollback()
        print(f"Ocorreu um erro durante a importacao: {e}")
        traceback.print_exc()

# --- NOVO COMANDO 'import-ccv' REGISTRADO CORRETAMENTE ---
@app.cli.command("import-ccv")
def import_ccv_data_command():
    """Comando para executar a importação de dados CCV: flask import-ccv"""
    from import_ccv_data import importar_dados_excel
    # O arquivo de dados deve estar na pasta 'backend/data/'
    arquivo_dados = os.path.join('data', 'ccv_dados.xlsx')
    importar_dados_excel(arquivo_dados)


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)