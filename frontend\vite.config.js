// frontend/vite.config.js

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: true, // Garante que o servidor seja acessível pela rede (importante para Docker)
    port: 5173, // Define a porta explicitamente (opcional, mas boa prática)
    watch: {
      // Habilita o polling, que é necessário para o Hot Reloading funcionar no Docker/WSL
      usePolling: true
    }
  }
})