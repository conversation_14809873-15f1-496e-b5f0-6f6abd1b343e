# backend/setup_ccv.py - VERSÃO CORRIGIDA

import sys
from app import app, db
from sqlalchemy import text  # ADICIONAR ESTA IMPORTAÇÃO

def setup_empresa_tipos():
    """Setup específico dos tipos de veículos da empresa."""
    
    print("🚛 Iniciando setup dos tipos de veículos da empresa...")
    
    with app.app_context():
        try:
            from models import CCVTipoVeiculo, CCVCategoriaVeiculo
            
            # 1. CRIAR/ATUALIZAR CATEGORIAS DA EMPRESA
            print("📋 Criando categorias da empresa...")
            
            categorias_empresa = [
                ('MOTO', 'Moto'),
                ('BICICLETA', 'Bicicleta'),
                ('CARRO', 'Carro'),
                ('CAM_LEVE', 'Cam Leve'),
                ('CAM_PESADO', 'Cam Pesado'),
                ('ONIBUS', 'Onibus'),
                ('PEDESTRE', 'Pedestre'),
                ('ESPECIAL', 'Especial')
            ]
            
            categorias_map = {}
            for codigo, nome in categorias_empresa:
                categoria = CCVCategoriaVeiculo.query.filter_by(codigo=codigo).first()
                if not categoria:
                    categoria = CCVCategoriaVeiculo(codigo=codigo, nome=nome)
                    db.session.add(categoria)
                    db.session.flush()  # Para obter o ID
                categorias_map[codigo] = categoria.id
            
            db.session.commit()
            print("✅ Categorias da empresa criadas/atualizadas")
            
            # 2. CRIAR TIPOS DE VEÍCULOS GLOBAIS
            print("🚗 Criando tipos de veículos globais...")
            
            tipos_empresa = [
                # (categoria_codigo, codigo, nome, descricao, equivalente_auto, cor_padrao, codigo_original, ordem)
                ('MOTO', 'MC', 'Motocicleta', 'Motocicletas em geral', 0.5, '#1abc9c', 'Mc', 1),
                
                ('BICICLETA', 'PC', 'Bicicleta', 'Bicicletas convencionais', 0.2, '#27ae60', 'Pc', 2),
                ('BICICLETA', 'CYCLIST_PEDALING', 'Ciclista Pedalando', 'Ciclista em movimento', 0.2, '#2ecc71', 'cyclist pedaling', 3),
                ('BICICLETA', 'CYCLIST_PUSHING', 'Ciclista Empurrando', 'Ciclista empurrando bicicleta', 0.3, '#58d68d', 'cyclist pushing', 4),
                
                ('CARRO', 'CAR', 'Automóvel', 'Carros de passeio', 1.0, '#3498db', 'Car', 5),
                ('CARRO', 'CAR_TRAILER_1', 'Carro c/ Reboque 1 Eixo', 'Carro com reboque de 1 eixo', 1.3, '#2980b9', 'Car_trailer_1_axle', 6),
                ('CARRO', 'CAR_TRAILER_2', 'Carro c/ Reboque 2 Eixos', 'Carro com reboque de 2 eixos', 1.5, '#1f4e79', 'Car_trailer_2_axle', 7),
                
                ('CAM_LEVE', 'LGV', 'Veículo Comercial Leve', 'Light Goods Vehicle', 1.8, '#f39c12', 'Lgv', 10),
                ('CAM_LEVE', 'OGV1', 'Veículo Comercial 1', 'Other Goods Vehicle tipo 1', 1.8, '#e67e22', 'Ogv1', 11),
                ('CAM_LEVE', 'CAM_2E', 'Caminhão 2 Eixos', 'Caminhão de 2 eixos', 1.8, '#d35400', '2 axles', 12),
                ('CAM_LEVE', 'CAM_3E_1SUSP', 'Caminhão 3 Eixos (1 Suspenso)', 'Caminhão 3 eixos com 1 suspenso', 2.0, '#c0392b', '3axles_1suspended', 13),
                ('CAM_LEVE', 'CAM_3E_0SUSP', 'Caminhão 3 Eixos', 'Caminhão 3 eixos sem suspensos', 2.0, '#a93226', '3axles_0suspended', 14),
                ('CAM_LEVE', 'CAM_2C', 'Caminhão 2C', 'Caminhão tipo 2C', 1.8, '#922b21', '2C', 15),
                ('CAM_LEVE', 'CAM_3C', 'Caminhão 3C', 'Caminhão tipo 3C', 2.0, '#7b241c', '3C', 16),
                
                ('ONIBUS', 'ONIBUS_2CB', 'Ônibus 2CB', 'Ônibus tipo 2CB', 2.5, '#9b59b6', '2CB', 20),
                ('ONIBUS', 'ONIBUS_3CB', 'Ônibus 3CB', 'Ônibus tipo 3CB', 2.5, '#8e44ad', '3CB', 21),
                ('ONIBUS', 'ONIBUS_4DB', 'Ônibus 4DB', 'Ônibus articulado tipo 4DB', 2.8, '#7d3c98', '4DB', 22),
                ('ONIBUS', 'ONIBUS_2SB1', 'Ônibus 2SB1', 'Ônibus tipo 2SB1', 2.5, '#6c3483', '2SB1', 23),
                ('ONIBUS', 'ONIBUS_2IB2', 'Ônibus 2IB2', 'Ônibus tipo 2IB2', 2.5, '#5b2c6f', '2IB2', 24),
                
                ('CAM_PESADO', 'OGV2', 'Veículo Comercial Pesado 2', 'Other Goods Vehicle tipo 2', 2.5, '#e74c3c', 'Ogv2', 30),
                ('CAM_PESADO', 'CAM_4C', 'Caminhão 4C', 'Caminhão tipo 4C (4 eixos)', 2.5, '#bd2130', '4C', 34),
                ('CAM_PESADO', 'CAM_4CD', 'Caminhão 4CD', 'Caminhão tipo 4CD (4 eixos)', 2.5, '#b21f2d', '4CD', 35),
                ('CAM_PESADO', 'CAM_2S1', 'Caminhão 2S1', 'Caminhão tipo 2S1 (3 eixos)', 2.0, '#a71e2a', '2S1', 36),
                ('CAM_PESADO', 'CAM_2S2', 'Caminhão 2S2', 'Caminhão tipo 2S2 (4 eixos)', 2.5, '#9c1c27', '2S2', 37),
                ('CAM_PESADO', 'CAM_2I2', 'Caminhão 2I2', 'Caminhão tipo 2I2 (4 eixos)', 2.5, '#911a24', '2I2', 38),
                ('CAM_PESADO', 'CAM_3S1', 'Caminhão 3S1', 'Caminhão tipo 3S1 (4 eixos)', 2.5, '#861921', '3S1', 39),
                ('CAM_PESADO', 'CAM_2C2', 'Caminhão 2C2', 'Caminhão tipo 2C2 (4 eixos)', 2.5, '#7b171e', '2C2', 40),
                ('CAM_PESADO', 'CAM_2S3', 'Caminhão 2S3', 'Caminhão tipo 2S3 (5 eixos)', 3.0, '#9f3022', '2S3', 44),
                ('CAM_PESADO', 'CAM_3S2', 'Caminhão 3S2', 'Caminhão tipo 3S2 (5 eixos)', 3.0, '#942d1f', '3S2', 45),
                ('CAM_PESADO', 'CAM_2I3', 'Caminhão 2I3', 'Caminhão tipo 2I3 (5 eixos)', 3.0, '#892a1c', '2I3', 46),
                ('CAM_PESADO', 'CAM_2J3', 'Caminhão 2J3', 'Caminhão tipo 2J3 (5 eixos)', 3.0, '#7e2719', '2J3', 47),
                ('CAM_PESADO', 'CAM_3I2', 'Caminhão 3I2', 'Caminhão tipo 3I2 (5 eixos)', 3.0, '#732416', '3I2', 48),
                ('CAM_PESADO', 'CAM_2C3', 'Caminhão 2C3', 'Caminhão tipo 2C3 (5 eixos)', 3.0, '#682113', '2C3', 49),
                ('CAM_PESADO', 'CAM_3C2', 'Caminhão 3C2', 'Caminhão tipo 3C2 (5 eixos)', 3.0, '#5d1e10', '3C2', 50),
                ('CAM_PESADO', 'CAM_3S3', 'Caminhão 3S3', 'Caminhão tipo 3S3 (6 eixos)', 3.5, '#7d261a', '3S3', 55),
                ('CAM_PESADO', 'CAM_3I3', 'Caminhão 3I3', 'Caminhão tipo 3I3 (6 eixos)', 3.5, '#722317', '3I3', 56),
                ('CAM_PESADO', 'CAM_3J3', 'Caminhão 3J3', 'Caminhão tipo 3J3 (6 eixos)', 3.5, '#672014', '3J3', 57),
                ('CAM_PESADO', 'CAM_3C3', 'Caminhão 3C3', 'Caminhão tipo 3C3 (6 eixos)', 3.5, '#5c1d11', '3C3', 58),
                ('CAM_PESADO', 'CAM_3D3', 'Caminhão 3D3', 'Caminhão tipo 3D3 (6 eixos)', 3.5, '#511a0e', '3D3', 59),
                ('CAM_PESADO', 'CAM_3D4', 'Caminhão 3D4', 'Caminhão tipo 3D4 (7 eixos)', 4.0, '#661f15', '3D4', 64),
                ('CAM_PESADO', 'CAM_3Q4', 'Caminhão 3Q4', 'Caminhão tipo 3Q4 (7 eixos)', 4.0, '#5b1c12', '3Q4', 65),
                ('CAM_PESADO', 'CAM_3T6', 'Caminhão 3T6', 'Caminhão tipo 3T6 (9 eixos)', 5.0, '#2d0f07', '3T6', 85),
                
                # Adicionando mais tipos importantes baseados na sua lista
                ('CAM_PESADO', 'CAM_4E_0SUSP', 'Caminhão 4 Eixos', 'Caminhão 4 eixos sem suspensos', 2.5, '#e74c3c', '4axles_0suspended', 31),
                ('CAM_PESADO', 'CAM_4E_1SUSP', 'Caminhão 4 Eixos (1 Suspenso)', 'Caminhão 4 eixos com 1 suspenso', 2.3, '#dc3545', '4axles_1suspended', 32),
                ('CAM_PESADO', 'CAM_4E_2SUSP', 'Caminhão 4 Eixos (2 Suspensos)', 'Caminhão 4 eixos com 2 suspensos', 2.0, '#c82333', '4axles_2suspended', 33),
                ('CAM_PESADO', 'CAM_5E_0SUSP', 'Caminhão 5 Eixos', 'Caminhão 5 eixos sem suspensos', 3.0, '#c0392b', '5axles_0suspended', 41),
                ('CAM_PESADO', 'CAM_5E_1SUSP', 'Caminhão 5 Eixos (1 Suspenso)', 'Caminhão 5 eixos com 1 suspenso', 2.8, '#b53628', '5axles_1suspended', 42),
                ('CAM_PESADO', 'CAM_5E_2SUSP', 'Caminhão 5 Eixos (2 Suspensos)', 'Caminhão 5 eixos com 2 suspensos', 2.5, '#aa3325', '5axles_2suspended', 43),
                ('CAM_PESADO', 'CAM_6E_0SUSP', 'Caminhão 6 Eixos', 'Caminhão 6 eixos sem suspensos', 3.5, '#a93226', '6axles_0suspended', 51),
                ('CAM_PESADO', 'CAM_6E_1SUSP', 'Caminhão 6 Eixos (1 Suspenso)', 'Caminhão 6 eixos com 1 suspenso', 3.3, '#9e2f23', '6axles_1suspended', 52),
                ('CAM_PESADO', 'CAM_6E_2SUSP', 'Caminhão 6 Eixos (2 Suspensos)', 'Caminhão 6 eixos com 2 suspensos', 3.0, '#932c20', '6axles_2suspended', 53),
                ('CAM_PESADO', 'CAM_6E_3SUSP', 'Caminhão 6 Eixos (3 Suspensos)', 'Caminhão 6 eixos com 3 suspensos', 2.8, '#88291d', '6axles_3suspended', 54),
                ('CAM_PESADO', 'CAM_7E_0SUSP', 'Caminhão 7 Eixos', 'Caminhão 7 eixos sem suspensos', 4.0, '#922b21', '7axles_0suspended', 60),
                ('CAM_PESADO', 'CAM_8E_0SUSP', 'Caminhão 8 Eixos', 'Caminhão 8 eixos sem suspensos', 4.5, '#7b241c', '8axles_0suspended', 70),
                ('CAM_PESADO', 'CAM_9E_0SUSP', 'Caminhão 9 Eixos', 'Caminhão 9 eixos sem suspensos', 5.0, '#641e16', '9axles_0suspended', 80),
                ('CAM_PESADO', 'CAM_9MAIS_0SUSP', 'Caminhão 9+ Eixos', 'Caminhão 9+ eixos sem suspensos', 5.5, '#4a1a11', '9+axles_0suspended', 90),
                
                ('PEDESTRE', 'PEDESTRE', 'Pedestre', 'Pedestres em geral', 0.1, '#95a5a6', 'PM', 100),
                ('PEDESTRE', 'PEDESTRE_MR', 'Pedestre Mobilidade Reduzida', 'Pedestre com mobilidade reduzida', 0.1, '#7f8c8d', 'PMR', 101),
                
                ('ESPECIAL', 'ANIMAL', 'Animal', 'Animais diversos', 0.2, '#34495e', 'Animal', 110)
            ]
            
            tipos_criados = 0
            for categoria_codigo, codigo, nome, descricao, equivalente_auto, cor_padrao, codigo_original, ordem in tipos_empresa:
                # Verificar se já existe
                tipo_existente = CCVTipoVeiculo.query.filter_by(
                    codigo=codigo, 
                    projeto_id=None
                ).first()
                
                if not tipo_existente:
                    categoria_id = categorias_map[categoria_codigo]
                    novo_tipo = CCVTipoVeiculo(
                        projeto_id=None,
                        categoria_id=categoria_id,
                        codigo=codigo,
                        nome=nome,
                        descricao=descricao,
                        equivalente_auto=equivalente_auto,
                        cor_padrao=cor_padrao,
                        codigo_original=codigo_original,
                        ordem_exibicao=ordem,
                        ativo=True
                    )
                    db.session.add(novo_tipo)
                    tipos_criados += 1
            
            db.session.commit()
            print(f"✅ {tipos_criados} novos tipos de veículos criados")
            
            # 3. VERIFICAÇÃO FINAL
            total_tipos = CCVTipoVeiculo.query.filter_by(projeto_id=None).count()
            total_categorias = CCVCategoriaVeiculo.query.count()
            
            print(f"📊 Resumo final:")
            print(f"   - {total_categorias} categorias configuradas")
            print(f"   - {total_tipos} tipos de veículos globais disponíveis")
            print("✅ Setup dos tipos da empresa concluído com sucesso!")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro durante setup dos tipos da empresa: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

def setup_ccv():
    """Setup completo do produto CCV com a nova estrutura flexível."""
    
    print("🚀 Iniciando setup do produto CCV...")
    
    with app.app_context():
        try:
            print("📋 Criando tabelas...")
            db.create_all()
            print("✅ Tabelas criadas com sucesso")
            
            print("🌱 Inserindo dados iniciais...")
            
            from models import Empresa, CCVTipoVeiculo, CCVCategoriaVeiculo

            # 1. Garante que a empresa padrão exista
            if not Empresa.query.filter_by(codigo_empresa='FRATAR').first():
                empresa = Empresa(
                    nome_empresa='Fratar Engenharia',
                    codigo_empresa='FRATAR',
                    gerencia='Tráfego'
                )
                db.session.add(empresa)
                print("✅ Empresa padrão criada")
            
            db.session.commit()
            
            # 2. Setup básico concluído, agora setup da empresa
            print("🎉 Setup básico do CCV concluído!")
            
            # 3. Setup dos tipos da empresa
            setup_empresa_resultado = setup_empresa_tipos()
            
            if setup_empresa_resultado:
                print("🎉 Setup completo do CCV concluído com sucesso!")
                return True
            else:
                print("⚠️ Setup básico OK, mas falha no setup dos tipos da empresa")
                return False
            
        except Exception as e:
            print(f"❌ Erro durante setup: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False