# backend/routes_admin.py

from flask import Blueprint, jsonify, request, current_app
from app import db
# --- INÍCIO DA CORREÇÃO ---
# IMPORTAÇÃO FINAL E COMPLETA com todos os modelos necessários para a limpeza
from models import (Empresa, Projeto,
                    Ped, PedDia, PedDadosHorarios,
                    CCVPonto, CCVDia, CCVMovimento, CCVContagem,
                    VIAProjeto, VIANo, VIATrecho, VIACenario, VIADadosDesempenho,
                    FTPPonto, FTPDia, FTPMovimento, FTPContagem,
                    TLPSegmento, PODZona)
# --- FIM DA CORREÇÃO ---
from sqlalchemy import func
import traceback

admin_bp = Blueprint('admin', __name__, url_prefix='/api/admin')

# Mapeamento de produtos para seus modelos principais
# A lista de modelos foi expandida e ordenada para TODOS os produtos com dependências
PRODUCT_MODELS = {
    # --- INÍCIO DA CORREÇÃO ---
    'ped': [
        Ped<PERSON><PERSON>sHorarios, # Depende de Ped e PedDia
        PedDia,           # Depende de Ped
        Ped               # Modelo principal
    ],
    'ccv': [
        CCVContagem,      # Depende de CCVDia e CCVMovimento
        CCVDia,           # Depende de CCVPonto
        CCVMovimento,     # Depende de CCVPonto
        CCVPonto          # Modelo principal
    ],
    # --- FIM DA CORREÇÃO ---
    'via': [
        VIADadosDesempenho, 
        VIATrecho, 
        VIACenario, 
        VIANo, 
        VIAProjeto
    ],
    'ftp': [
        FTPContagem,
        FTPDia,
        FTPMovimento,
        FTPPonto
    ],
    'tlp': [TLPSegmento],
    'pod': [PODZona]
}

@admin_bp.route('/stats', methods=['GET'])
def get_database_stats():
    """Retorna estatísticas gerais do banco de dados."""
    try:
        stats = {
            'empresas': db.session.query(func.count(Empresa.id)).scalar(),
            'projetos': db.session.query(func.count(Projeto.id)).scalar(),
            'ped': {
                'pontos': db.session.query(func.count(Ped.id)).scalar(),
                'dias_coleta': db.session.query(func.count(PedDia.id)).scalar(),
                'registros_horarios': db.session.query(func.count(PedDadosHorarios.id)).scalar()
            },
            'ccv': {
                'pontos': db.session.query(func.count(CCVPonto.id)).scalar(),
                'contagens': db.session.query(func.count(CCVContagem.id)).scalar()
            },
            'via': {
                'redes': db.session.query(func.count(VIAProjeto.id)).scalar(),
                'trechos': db.session.query(func.count(VIATrecho.id)).scalar(),
                'nos': db.session.query(func.count(VIANo.id)).scalar()
            },
            'ftp': {
                'pontos': db.session.query(func.count(FTPPonto.id)).scalar(),
                'contagens': db.session.query(func.count(FTPContagem.id)).scalar()
            },
            'tlp': {
                'segmentos': db.session.query(func.count(TLPSegmento.id)).scalar()
            },
            'pod': {
                'zonas': db.session.query(func.count(PODZona.id)).scalar()
            }
        }
        return jsonify({'stats': stats})
    except Exception as e:
        current_app.logger.error(f"Erro ao buscar estatísticas: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/empresas', methods=['GET', 'POST'])
def manage_empresas():
    """Gerencia empresas (listar e criar)."""
    if request.method == 'POST':
        data = request.get_json()
        if not data or 'nome_empresa' not in data or 'codigo_empresa' not in data:
            return jsonify({'error': 'Dados insuficientes'}), 400
        nova_empresa = Empresa(**data)
        db.session.add(nova_empresa)
        db.session.commit()
        return jsonify({'message': 'Empresa criada com sucesso!', 'id': nova_empresa.id}), 201
    
    empresas = Empresa.query.all()
    resultado = [{
        'id': e.id,
        'nome_empresa': e.nome_empresa,
        'codigo_empresa': e.codigo_empresa,
        'total_projetos': len(e.projetos)
    } for e in empresas]
    return jsonify({'empresas': resultado})

@admin_bp.route('/empresas/<int:empresa_id>', methods=['DELETE'])
def delete_empresa(empresa_id):
    """Deleta uma empresa e seus dados associados."""
    empresa = Empresa.query.get_or_404(empresa_id)
    db.session.delete(empresa)
    db.session.commit()
    return jsonify({'message': f'Empresa "{empresa.nome_empresa}" e todos os seus dados foram deletados.'})

@admin_bp.route('/projetos', methods=['GET', 'POST'])
def manage_projetos():
    """Gerencia projetos (listar e criar)."""
    if request.method == 'POST':
        data = request.get_json()
        if not data or 'nome_projeto' not in data or 'codigo_projeto' not in data or 'empresa_id' not in data:
            return jsonify({'error': 'Dados insuficientes'}), 400
        novo_projeto = Projeto(**data)
        db.session.add(novo_projeto)
        db.session.commit()
        return jsonify({'message': 'Projeto criado com sucesso!', 'id': novo_projeto.id}), 201
    
    empresa_id = request.args.get('empresa_id', type=int)
    query = Projeto.query
    if empresa_id:
        query = query.filter_by(empresa_id=empresa_id)
    
    projetos = query.all()
    resultado = []
    for p in projetos:
        dados_count = {
            'ped': len(p.peds),
            'ccv': len(p.pontos_ccv),
            'via': len(p.projetos_via)
        }
        resultado.append({
            'id': p.id,
            'nome_projeto': p.nome_projeto,
            'codigo_projeto': p.codigo_projeto,
            'status': p.status,
            'empresa': {'id': p.empresa.id, 'nome_empresa': p.empresa.nome_empresa},
            'dados_count': dados_count
        })
    return jsonify({'projetos': resultado})

@admin_bp.route('/projetos/<int:projeto_id>', methods=['DELETE'])
def delete_projeto(projeto_id):
    """Deleta um projeto e seus dados associados."""
    projeto = Projeto.query.get_or_404(projeto_id)
    db.session.delete(projeto)
    db.session.commit()
    return jsonify({'message': f'Projeto "{projeto.nome_projeto}" e todos os seus dados foram deletados.'})

@admin_bp.route('/produtos/<produto>/clean', methods=['DELETE'])
def clean_product_data(produto):
    """Limpa todos os dados de um produto específico."""
    produto = produto.lower()
    if produto not in PRODUCT_MODELS:
        return jsonify({'error': 'Produto desconhecido'}), 404
    
    try:
        total_deletado = 0
        for model in PRODUCT_MODELS[produto]:
            # A função agora itera sobre a lista de modelos na ordem correta
            deletado = model.query.delete()
            total_deletado += deletado
        db.session.commit()
        return jsonify({'message': f'Todos os dados do produto {produto.upper()} foram deletados com sucesso.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/database/reset', methods=['DELETE'])
def reset_database():
    """Reseta o banco de dados completamente."""
    if request.args.get('confirm') != 'RESET_ALL_DATA':
        return jsonify({'error': 'Confirmação inválida. A operação foi cancelada.'}), 400
    
    try:
        db.drop_all()
        db.create_all()
        
        # Opcional: Rodar setup inicial novamente
        from setup_ccv import setup_ccv
        setup_ccv()

        return jsonify({'message': 'Banco de dados resetado e recriado com sucesso!'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erro ao resetar o banco: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500