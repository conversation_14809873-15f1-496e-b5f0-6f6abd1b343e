# docker-compose.yml (Production-ready for Coolify)
services:
  # --- BA<PERSON><PERSON><PERSON> (API FLASK) ---
  backend:
    build: ./backend
    command: flask --app app run --host=0.0.0.0 --port=5000
    # In production with Coolify proxy, do not publish host ports
    # ports:
    #   - "5000:5000"
    volumes:
      - ./backend:/app
    # For production on Coolify, manage secrets/configs via the UI instead of env_file.
    # If you keep env_file for local dev, leave it commented to avoid masking Coolify UI variables on deploy.
    # env_file:
    #   - ./.env
    environment:
      - PYTHONIOENCODING=utf-8
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      # Coolify-generated FQDN routed to container port 5000
      - SERVICE_FQDN_API_5000
      # Optional: expose the base URL to the app (uncomment if your app needs it)
      # - BASE_URL=$SERVICE_FQDN_API_5000
    depends_on:
      - db

  # --- FRONTEND (React + Vite 5173) ---
  frontend:
    build: ./frontend
    # In production with Coolify proxy, do not publish host ports
    # ports:
    #   - "3000:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      # Make the frontend reachable via Coolify proxy at port 5173
      - SERVICE_FQDN_FRONTEND_5173
      # If the frontend needs to call the backend at runtime/build, pass it here:
      # - VITE_API_URL=$SERVICE_FQDN_API_5000
    depends_on:
      - backend

  # --- DATABASE (Postgres) ---
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASS:-change_me}

volumes:
  postgres_data: