import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

export default function ViaImportForm({
  onAnalyzeGeoJson,
  onConfirmImport,
  selectedProject,
  loading,
  config,
  corConfig
}) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    nomeRede: '',
    versaoAimsun: '',
    geojsonFile: null
  });
  const [analysisResult, setAnalysisResult] = useState(null);
  const [indicadoresConfig, setIndicadoresConfig] = useState({});
  const [geojsonData, setGeojsonData] = useState(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({ ...prev, geojsonFile: file }));
    setAnalysisResult(null);
    setCurrentStep(1);
  };

  const handleAnalyzeFile = async () => {
    if (!formData.geojsonFile || !selectedProject) {
      alert('Selecione um projeto e arquivo GeoJSON');
      return;
    }

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('projeto_id', selectedProject);
      formDataToSend.append('nome_rede', formData.nomeRede || 'Rede Aimsun');
      formDataToSend.append('geojson_file', formData.geojsonFile);

      // Lê o arquivo para enviar na confirmação
      const fileText = await formData.geojsonFile.text();
      const geojsonParsed = JSON.parse(fileText);
      setGeojsonData(geojsonParsed);

      const response = await fetch('http://localhost:5000/api/via/upload-geojson', {
        method: 'POST',
        body: formDataToSend
      });

      const result = await response.json();
      
      if (response.ok) {
        setAnalysisResult(result);
        setCurrentStep(2);
        
        // Inicializa configuração de indicadores com recomendações
        const initialConfig = {};
        result.atributos_encontrados.forEach(attr => {
          if (attr.recomendado) {
            initialConfig[attr.nome] = {
              ativo: true,
              nome_personalizado: attr.descricao || attr.nome,
              tipo: attr.tipo,
              unidade: attr.unidade
            };
          }
        });
        setIndicadoresConfig(initialConfig);
      } else {
        throw new Error(result.error || 'Erro na análise');
      }
    } catch (error) {
      console.error('Erro ao analisar arquivo:', error);
      alert(`Erro: ${error.message}`);
    }
  };

  const handleIndicadorChange = (atributo, field, value) => {
    setIndicadoresConfig(prev => ({
      ...prev,
      [atributo]: {
        ...prev[atributo],
        [field]: value
      }
    }));
  };

  const toggleIndicador = (atributo, attr) => {
    setIndicadoresConfig(prev => {
      if (prev[atributo]) {
        const { [atributo]: removed, ...rest } = prev;
        return rest;
      } else {
        return {
          ...prev,
          [atributo]: {
            ativo: true,
            nome_personalizado: attr.descricao || attr.nome,
            tipo: attr.tipo,
            unidade: attr.unidade
          }
        };
      }
    });
  };

  const handleConfirmImport = async () => {
    if (!geojsonData || Object.keys(indicadoresConfig).length === 0) {
      alert('Configure pelo menos um indicador para continuar');
      return;
    }

    const importData = {
      projeto_id: selectedProject,
      nome_rede: formData.nomeRede || 'Rede Aimsun',
      versao_aimsun: formData.versaoAimsun,
      indicadores_config: indicadoresConfig,
      geojson_data: geojsonData,
      overwrite: false
    };

    await onConfirmImport(importData);
  };

  const getStepClass = (step) => {
    if (step < currentStep) return 'bg-green-500 text-white';
    if (step === currentStep) return `${corConfig.button.replace('hover:', '')} text-white`;
    return 'bg-gray-300 text-gray-600';
  };

  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">
        2. Importar Rede Viária do Aimsun
      </h3>

      {/* Progress Steps */}
      <div className="flex items-center mb-6">
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getStepClass(1)}`}>
            1
          </div>
          <span className="ml-2 text-sm font-medium">Upload e Configuração</span>
        </div>
        
        <div className="flex-1 h-0.5 bg-gray-300 mx-4"></div>
        
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getStepClass(2)}`}>
            2
          </div>
          <span className="ml-2 text-sm font-medium">Seleção de Indicadores</span>
        </div>
        
        <div className="flex-1 h-0.5 bg-gray-300 mx-4"></div>
        
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getStepClass(3)}`}>
            3
          </div>
          <span className="ml-2 text-sm font-medium">Confirmação</span>
        </div>
      </div>

      {/* Step 1: Upload e Configuração */}
      {currentStep === 1 && (
        <div className="space-y-6">
          <div className={`${corConfig.bg} p-4 rounded-lg border border-gray-200`}>
            <h4 className="font-semibold text-gray-800 mb-2">
              <i className="fas fa-info-circle mr-2"></i>
              Sobre o Sistema VIA
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              O sistema VIA (Sistema Viário) permite importar e analisar redes viárias do Aimsun, 
              configurando indicadores de desempenho personalizados para visualização e análise.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-700">
              <div>
                <strong>Formatos suportados:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• GeoJSON exportado do Aimsun</li>
                  <li>• Redes com geometria Point e LineString</li>
                  <li>• Atributos personalizados</li>
                </ul>
              </div>
              <div>
                <strong>Recursos incluídos:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• Visualização interativa da rede</li>
                  <li>• Indicadores configuráveis</li>
                  <li>• Análise de desempenho</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-600 mb-2">
                Nome da Rede
              </label>
              <input
                type="text"
                value={formData.nomeRede}
                onChange={(e) => setFormData(prev => ({ ...prev, nomeRede: e.target.value }))}
                placeholder="Ex: Rede Central BH"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-600 mb-2">
                Versão do Aimsun (Opcional)
              </label>
              <input
                type="text"
                value={formData.versaoAimsun}
                onChange={(e) => setFormData(prev => ({ ...prev, versaoAimsun: e.target.value }))}
                placeholder="Ex: 22.0.3"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-600 mb-2">
              Arquivo GeoJSON da Rede
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              accept=".geojson,.json"
              className="w-full text-sm file:mr-4 file:py-3 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
            />
            {formData.geojsonFile && (
              <div className="mt-2 text-sm text-gray-600">
                <i className="fas fa-file-code mr-1"></i>
                {formData.geojsonFile.name} ({Math.round(formData.geojsonFile.size / 1024)} KB)
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <button
              onClick={handleAnalyzeFile}
              disabled={!formData.geojsonFile || !selectedProject || loading}
              className={`px-6 py-3 ${corConfig.button} text-white rounded-lg disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors`}
            >
              {loading ? (
                <span>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Analisando...
                </span>
              ) : (
                <span>
                  <i className="fas fa-search mr-2"></i>
                  Analisar Arquivo
                </span>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Step 2: Seleção de Indicadores */}
      {currentStep === 2 && analysisResult && (
        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-2">
              <i className="fas fa-check-circle mr-2"></i>
              Análise Concluída
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-green-700">
              <div>
                <strong>Total de Features:</strong> {analysisResult.estatisticas.total_features}
              </div>
              <div>
                <strong>Nós (Points):</strong> {analysisResult.estatisticas.total_nos}
              </div>
              <div>
                <strong>Trechos (Lines):</strong> {analysisResult.estatisticas.total_trechos}
              </div>
              <div>
                <strong>Atributos:</strong> {analysisResult.atributos_encontrados.length}
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-gray-800 mb-4">
              Configurar Indicadores de Desempenho
            </h4>
            <p className="text-sm text-gray-600 mb-4">
              Selecione os atributos que deseja usar como indicadores e configure seus nomes de exibição.
              Os itens recomendados já estão pré-selecionados.
            </p>

            <div className="space-y-3 max-h-96 overflow-y-auto border border-gray-200 rounded-lg p-4">
              {analysisResult.atributos_encontrados.map((attr, index) => {
                const isSelected = indicadoresConfig.hasOwnProperty(attr.nome);
                
                return (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 transition-all duration-200 ${
                      isSelected
                        ? 'border-indigo-300 bg-indigo-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleIndicador(attr.nome, attr)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">{attr.nome}</span>
                            {attr.recomendado && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                Recomendado
                              </span>
                            )}
                            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                              attr.tipo === 'numerico' 
                                ? 'bg-blue-100 text-blue-800'
                                : attr.tipo === 'categoria'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {attr.tipo}
                            </span>
                          </div>
                          
                          {attr.unidade && (
                            <span className="text-sm text-gray-500 font-mono">
                              {attr.unidade}
                            </span>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mt-1">
                          {attr.descricao}
                        </p>
                        
                        {attr.estatisticas && (
                          <div className="mt-2 text-xs text-gray-500">
                            {attr.tipo === 'numerico' ? (
                              <span>
                                Faixa: {attr.estatisticas.min} - {attr.estatisticas.max} | 
                                Valores únicos: {attr.estatisticas.valores_unicos}
                              </span>
                            ) : (
                              <span>
                                {attr.estatisticas.valores_unicos} valores únicos
                                {attr.estatisticas.exemplos && (
                                  <span className="ml-2">
                                    (Ex: {attr.estatisticas.exemplos.slice(0, 3).join(', ')})
                                  </span>
                                )}
                              </span>
                            )}
                          </div>
                        )}
                        
                        {isSelected && (
                          <div className="mt-3">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Nome do Indicador
                            </label>
                            <input
                              type="text"
                              value={indicadoresConfig[attr.nome]?.nome_personalizado || ''}
                              onChange={(e) => handleIndicadorChange(attr.nome, 'nome_personalizado', e.target.value)}
                              placeholder="Nome personalizado para exibição"
                              className="w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-semibold text-blue-800 mb-2">
              Resumo da Configuração
            </h5>
            <div className="text-sm text-blue-700">
              <strong>{Object.keys(indicadoresConfig).length}</strong> indicadores selecionados
              {Object.keys(indicadoresConfig).length > 0 && (
                <div className="mt-2">
                  <strong>Indicadores:</strong> {Object.entries(indicadoresConfig)
                    .map(([key, config]) => config.nome_personalizado || key)
                    .join(', ')}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-between">
            <button
              onClick={() => setCurrentStep(1)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Voltar
            </button>
            
            <button
              onClick={() => setCurrentStep(3)}
              disabled={Object.keys(indicadoresConfig).length === 0}
              className={`px-6 py-3 ${corConfig.button} text-white rounded-lg disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors`}
            >
              Continuar
              <i className="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>
      )}

      {/* Step 3: Confirmação */}
      {currentStep === 3 && (
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">
              <i className="fas fa-exclamation-triangle mr-2"></i>
              Confirmação de Importação
            </h4>
            <p className="text-sm text-yellow-700">
              Revise as configurações antes de confirmar a importação. Este processo irá criar 
              a estrutura da rede e configurar os indicadores selecionados.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h5 className="font-semibold text-gray-800 mb-3">Informações da Rede</h5>
              <div className="space-y-2 text-sm">
                <div><strong>Nome:</strong> {formData.nomeRede || 'Rede Aimsun'}</div>
                <div><strong>Versão Aimsun:</strong> {formData.versaoAimsun || 'Não especificada'}</div>
                <div><strong>Arquivo:</strong> {formData.geojsonFile?.name}</div>
                {analysisResult && (
                  <>
                    <div><strong>Nós:</strong> {analysisResult.estatisticas.total_nos}</div>
                    <div><strong>Trechos:</strong> {analysisResult.estatisticas.total_trechos}</div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h5 className="font-semibold text-gray-800 mb-3">Indicadores Configurados</h5>
              <div className="space-y-2 text-sm max-h-32 overflow-y-auto">
                {Object.entries(indicadoresConfig).map(([key, config]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="font-medium">{config.nome_personalizado}</span>
                    <span className="text-gray-500 text-xs">
                      {config.tipo} {config.unidade && `(${config.unidade})`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <button
              onClick={() => setCurrentStep(2)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Voltar
            </button>
            
            <button
              onClick={handleConfirmImport}
              disabled={loading}
              className={`px-6 py-3 ${corConfig.button} text-white rounded-lg disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors`}
            >
              {loading ? (
                <span>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Importando...
                </span>
              ) : (
                <span>
                  <i className="fas fa-check mr-2"></i>
                  Confirmar Importação
                </span>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

ViaImportForm.propTypes = {
  onAnalyzeGeoJson: PropTypes.func.isRequired,
  onConfirmImport: PropTypes.func.isRequired,
  selectedProject: PropTypes.number,
  loading: PropTypes.bool.isRequired,
  config: PropTypes.object.isRequired,
  corConfig: PropTypes.object.isRequired
};