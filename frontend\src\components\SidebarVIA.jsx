import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

export default function SidebarVIA({
  empresas,
  projetos,
  projetosVIA,
  dados,
  empresaSelecionada,
  setEmpresaSelecionada,
  projetoSelecionado,
  setProjetoSelecionado,
  projetoVIASelecionado,
  setProjetoVIASelecionado,
  indicadorSelecionado,
  setIndicadorSelecionado,
  cenarioSelecionado,
  setCenarioSelecionado,
  mostrarNos,
  setMostrarNos,
  mostrarTrechos,
  setMostrarTrechos,
  opacidadeTrechos,
  setOpacidadeTrechos
}) {
  const [estatisticas, setEstatisticas] = useState(null);

  // Calcular estatísticas dos dados
  useEffect(() => {
    if (dados && dados.trechos && dados.nos) {
      const trechos = Object.values(dados.trechos);
      const nos = Object.values(dados.nos);
      
      // Calcula estatísticas da rede
      const comprimentoTotal = trechos.reduce((sum, trecho) => {
        return sum + (trecho.comprimento || 0);
      }, 0);
      
      const faixasTotal = trechos.reduce((sum, trecho) => {
        return sum + (trecho.numero_faixas || 0);
      }, 0);
      
      const stats = {
        totalTrechos: trechos.length,
        totalNos: nos.length,
        comprimentoTotal: comprimentoTotal / 1000, // Converte para km
        faixasTotal: faixasTotal,
        indicadoresConfigurados: Object.keys(dados.projeto_via?.indicadores_config || {}).length,
        nomeRede: dados.projeto_via?.nome_rede || 'Rede VIA'
      };
      setEstatisticas(stats);
    }
  }, [dados]);

  // Indicadores disponíveis baseados na configuração do projeto
  const indicadoresDisponiveis = dados?.projeto_via?.indicadores_config || {};

  return (
    <aside className="bg-white w-80 p-6 shadow-lg z-20 transform transition-transform duration-300 ease-in-out overflow-y-auto">
      <div className="space-y-6">
        
        {/* Cabeçalho */}
        <div>
          <h3 className="text-2xl font-bold text-gray-800 mb-2">VIA - Sistema Viário</h3>
          <p className="text-sm text-gray-600">Análise de redes viárias do Aimsun</p>
        </div>

        {/* Seleção de Empresa */}
        <div>
          <label htmlFor="empresa-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-building mr-2"></i>Empresa:
          </label>
          <select 
            id="empresa-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
            value={empresaSelecionada || ''} 
            onChange={(e) => setEmpresaSelecionada(Number(e.target.value))}
          >
            <option value="">Selecione uma empresa</option>
            {empresas.map(empresa => (
              <option key={empresa.id} value={empresa.id}>
                {empresa.nome_empresa}
                {empresa.gerencia && ` - ${empresa.gerencia}`}
              </option>
            ))}
          </select>
        </div>

        {/* Seleção de Projeto VIA */}
        <div>
          <label htmlFor="projeto-via-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-road mr-2"></i>Rede Viária:
          </label>
          <select 
            id="projeto-via-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
            value={projetoVIASelecionado || ''} 
            onChange={(e) => setProjetoVIASelecionado(Number(e.target.value))}
            disabled={!empresaSelecionada}
          >
            <option value="">Selecione uma rede viária</option>
            {projetosVIA.map(projetoVIA => (
              <option key={projetoVIA.id} value={projetoVIA.id}>
                {projetoVIA.nome_rede}
                {projetoVIA.versao_aimsun && ` (${projetoVIA.versao_aimsun})`}
              </option>
            ))}
          </select>
        </div>

        <hr className="border-gray-200" />

        {/* Seleção de Indicador */}
        <div>
          <label htmlFor="indicador-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-chart-line mr-2"></i>Indicador de Desempenho:
          </label>
          <select 
            id="indicador-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
            value={indicadorSelecionado} 
            onChange={(e) => setIndicadorSelecionado(e.target.value)}
            disabled={Object.keys(indicadoresDisponiveis).length === 0}
          >
            <option value="">Selecione um indicador</option>
            {Object.entries(indicadoresDisponiveis).map(([key, config]) => (
              <option key={key} value={key}>
                {config.nome_personalizado || key}
                {config.unidade && ` (${config.unidade})`}
              </option>
            ))}
          </select>
          
          {Object.keys(indicadoresDisponiveis).length === 0 && (
            <p className="text-xs text-gray-500 mt-1">
              Nenhum indicador configurado para esta rede
            </p>
          )}
        </div>

        {/* Estatísticas da Rede */}
        {estatisticas && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-800 mb-3">
              <i className="fas fa-chart-bar mr-2"></i>Estatísticas da Rede
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Nome da Rede:</span>
                <span className="font-semibold text-gray-900">{estatisticas.nomeRede}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total de Trechos:</span>
                <span className="font-semibold text-gray-900">{estatisticas.totalTrechos}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total de Nós:</span>
                <span className="font-semibold text-gray-900">{estatisticas.totalNos}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Comprimento Total:</span>
                <span className="font-semibold text-gray-900">{estatisticas.comprimentoTotal.toFixed(1)} km</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total de Faixas:</span>
                <span className="font-semibold text-gray-900">{estatisticas.faixasTotal}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Indicadores Config.:</span>
                <span className="font-semibold text-gray-900">{estatisticas.indicadoresConfigurados}</span>
              </div>
            </div>
          </div>
        )}

        {/* Configurações dos Indicadores */}
        {Object.keys(indicadoresDisponiveis).length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-800 mb-3">
              <i className="fas fa-cogs mr-2"></i>Indicadores Configurados
            </h4>
            <div className="space-y-2">
              {Object.entries(indicadoresDisponiveis).map(([key, config]) => (
                <div key={key} className="flex items-center gap-3 p-2 bg-white rounded border">
                  <div className={`w-3 h-3 rounded-full ${
                    indicadorSelecionado === key ? 'bg-indigo-500' : 'bg-gray-300'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {config.nome_personalizado || key}
                    </div>
                    <div className="text-xs text-gray-500">
                      {config.tipo} {config.unidade && `• ${config.unidade}`}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Informações do Sistema */}
        <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
          <h4 className="text-sm font-semibold text-indigo-800 mb-2">
            <i className="fas fa-info-circle mr-2"></i>Sistema VIA
          </h4>
          <div className="text-xs text-indigo-700 space-y-1">
            <div>• Importação de redes do Aimsun</div>
            <div>• Indicadores personalizáveis</div>
            <div>• Visualização interativa</div>
            <div>• Análise de desempenho viário</div>
          </div>
        </div>
      </div>
    </aside>
  );
}

SidebarVIA.propTypes = {
  empresas: PropTypes.array.isRequired,
  projetos: PropTypes.array.isRequired,
  projetosVIA: PropTypes.array.isRequired,
  dados: PropTypes.object,
  empresaSelecionada: PropTypes.number,
  setEmpresaSelecionada: PropTypes.func.isRequired,
  projetoSelecionado: PropTypes.number,
  setProjetoSelecionado: PropTypes.func.isRequired,
  projetoVIASelecionado: PropTypes.number,
  setProjetoVIASelecionado: PropTypes.func.isRequired,
  indicadorSelecionado: PropTypes.string.isRequired,
  setIndicadorSelecionado: PropTypes.func.isRequired,
  cenarioSelecionado: PropTypes.string.isRequired,
  setCenarioSelecionado: PropTypes.func.isRequired,
  mostrarNos: PropTypes.bool.isRequired,
  setMostrarNos: PropTypes.func.isRequired,
  mostrarTrechos: PropTypes.bool.isRequired,
  setMostrarTrechos: PropTypes.func.isRequired,
  opacidadeTrechos: PropTypes.number.isRequired,
  setOpacidadeTrechos: PropTypes.func.isRequired
};