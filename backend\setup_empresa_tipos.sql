-- backend/setup_empresa_tipos.sql
-- SETUP COMPLETO DOS TIPOS DE VEÍCULOS DA EMPRESA

-- 1. CRIAR/ATUALIZAR CATEGORIAS DA EMPRESA
INSERT INTO ccv_categoria_veiculo (codigo, nome) VALUES
('MOTO', 'Moto'),
('B<PERSON><PERSON><PERSON><PERSON>', 'Bicicleta'),
('CARRO', 'Carro'),
('CAM_LEVE', 'Cam Leve'),
('CAM_PESADO', 'Cam <PERSON>'),
('ONIBUS', 'Onibus'),
('PEDESTRE', 'Pedestre'),
('ESPECIAL', 'Especial')
ON CONFLICT (codigo) DO UPDATE SET nome = EXCLUDED.nome;

-- 2. INSERIR TODOS OS TIPOS DE VEÍCULOS GLOBAIS
INSERT INTO ccv_tipo_veiculo (projeto_id, categoria_id, codigo, nome, descricao, equivalente_auto, cor_padrao, codigo_original, ordem_exibicao, ativo) 
SELECT 
    NULL,
    (SELECT id FROM ccv_categoria_veiculo WHERE codigo = tipos.categoria),
    tipos.codigo,
    tipos.nome,
    tipos.descricao,
    tipos.equivalente_auto,
    tipos.cor_padrao,
    tipos.codigo_original,
    tipos.ordem_exibicao,
    TRUE
FROM (
    VALUES
    -- CATEGORIA MOTO
    ('MOTO', 'MC', 'Motocicleta', 'Motocicletas em geral', 0.5, '#1abc9c', 'Mc', 1),
    
    -- CATEGORIA BICICLETA
    ('BICICLETA', 'PC', 'Bicicleta', 'Bicicletas convencionais', 0.2, '#27ae60', 'Pc', 2),
    ('BICICLETA', 'CYCLIST_PEDALING', 'Ciclista Pedalando', 'Ciclista em movimento', 0.2, '#2ecc71', 'cyclist pedaling', 3),
    ('BICICLETA', 'CYCLIST_PUSHING', 'Ciclista Empurrando', 'Ciclista empurrando bicicleta', 0.3, '#58d68d', 'cyclist pushing', 4),
    
    -- CATEGORIA CARRO
    ('CARRO', 'CAR', 'Automóvel', 'Carros de passeio', 1.0, '#3498db', 'Car', 5),
    ('CARRO', 'CAR_TRAILER_1', 'Carro c/ Reboque 1 Eixo', 'Carro com reboque de 1 eixo', 1.3, '#2980b9', 'Car_trailer_1_axle', 6),
    ('CARRO', 'CAR_TRAILER_2', 'Carro c/ Reboque 2 Eixos', 'Carro com reboque de 2 eixos', 1.5, '#1f4e79', 'Car_trailer_2_axle', 7),
    
    -- CATEGORIA CAM LEVE
    ('CAM_LEVE', 'LGV', 'Veículo Comercial Leve', 'Light Goods Vehicle', 1.8, '#f39c12', 'Lgv', 10),
    ('CAM_LEVE', 'OGV1', 'Veículo Comercial 1', 'Other Goods Vehicle tipo 1', 1.8, '#e67e22', 'Ogv1', 11),
    ('CAM_LEVE', 'CAM_2E', 'Caminhão 2 Eixos', 'Caminhão de 2 eixos', 1.8, '#d35400', '2 axles', 12),
    ('CAM_LEVE', 'CAM_3E_1SUSP', 'Caminhão 3 Eixos (1 Suspenso)', 'Caminhão 3 eixos com 1 suspenso', 2.0, '#c0392b', '3axles_1suspended', 13),
    ('CAM_LEVE', 'CAM_3E_0SUSP', 'Caminhão 3 Eixos', 'Caminhão 3 eixos sem suspensos', 2.0, '#a93226', '3axles_0suspended', 14),
    ('CAM_LEVE', 'CAM_2C', 'Caminhão 2C', 'Caminhão tipo 2C', 1.8, '#922b21', '2C', 15),
    ('CAM_LEVE', 'CAM_3C', 'Caminhão 3C', 'Caminhão tipo 3C', 2.0, '#7b241c', '3C', 16),
    
    -- CATEGORIA ONIBUS
    ('ONIBUS', 'ONIBUS_2CB', 'Ônibus 2CB', 'Ônibus tipo 2CB', 2.5, '#9b59b6', '2CB', 20),
    ('ONIBUS', 'ONIBUS_3CB', 'Ônibus 3CB', 'Ônibus tipo 3CB', 2.5, '#8e44ad', '3CB', 21),
    ('ONIBUS', 'ONIBUS_4DB', 'Ônibus 4DB', 'Ônibus articulado tipo 4DB', 2.8, '#7d3c98', '4DB', 22),
    ('ONIBUS', 'ONIBUS_2SB1', 'Ônibus 2SB1', 'Ônibus tipo 2SB1', 2.5, '#6c3483', '2SB1', 23),
    ('ONIBUS', 'ONIBUS_2IB2', 'Ônibus 2IB2', 'Ônibus tipo 2IB2', 2.5, '#5b2c6f', '2IB2', 24),
    
    -- CATEGORIA CAM PESADO (seleção principal)
    ('CAM_PESADO', 'OGV2', 'Veículo Comercial Pesado 2', 'Other Goods Vehicle tipo 2', 2.5, '#e74c3c', 'Ogv2', 30),
    ('CAM_PESADO', 'CAM_4C', 'Caminhão 4C', 'Caminhão tipo 4C (4 eixos)', 2.5, '#bd2130', '4C', 34),
    ('CAM_PESADO', 'CAM_4CD', 'Caminhão 4CD', 'Caminhão tipo 4CD (4 eixos)', 2.5, '#b21f2d', '4CD', 35),
    ('CAM_PESADO', 'CAM_2S1', 'Caminhão 2S1', 'Caminhão tipo 2S1 (3 eixos)', 2.0, '#a71e2a', '2S1', 36),
    ('CAM_PESADO', 'CAM_2S2', 'Caminhão 2S2', 'Caminhão tipo 2S2 (4 eixos)', 2.5, '#9c1c27', '2S2', 37),
    ('CAM_PESADO', 'CAM_2I2', 'Caminhão 2I2', 'Caminhão tipo 2I2 (4 eixos)', 2.5, '#911a24', '2I2', 38),
    ('CAM_PESADO', 'CAM_3S1', 'Caminhão 3S1', 'Caminhão tipo 3S1 (4 eixos)', 2.5, '#861921', '3S1', 39),
    ('CAM_PESADO', 'CAM_2C2', 'Caminhão 2C2', 'Caminhão tipo 2C2 (4 eixos)', 2.5, '#7b171e', '2C2', 40),
    ('CAM_PESADO', 'CAM_2S3', 'Caminhão 2S3', 'Caminhão tipo 2S3 (5 eixos)', 3.0, '#9f3022', '2S3', 44),
    ('CAM_PESADO', 'CAM_3S2', 'Caminhão 3S2', 'Caminhão tipo 3S2 (5 eixos)', 3.0, '#942d1f', '3S2', 45),
    ('CAM_PESADO', 'CAM_2I3', 'Caminhão 2I3', 'Caminhão tipo 2I3 (5 eixos)', 3.0, '#892a1c', '2I3', 46),
    ('CAM_PESADO', 'CAM_2J3', 'Caminhão 2J3', 'Caminhão tipo 2J3 (5 eixos)', 3.0, '#7e2719', '2J3', 47),
    ('CAM_PESADO', 'CAM_3I2', 'Caminhão 3I2', 'Caminhão tipo 3I2 (5 eixos)', 3.0, '#732416', '3I2', 48),
    ('CAM_PESADO', 'CAM_2C3', 'Caminhão 2C3', 'Caminhão tipo 2C3 (5 eixos)', 3.0, '#682113', '2C3', 49),
    ('CAM_PESADO', 'CAM_3C2', 'Caminhão 3C2', 'Caminhão tipo 3C2 (5 eixos)', 3.0, '#5d1e10', '3C2', 50),
    ('CAM_PESADO', 'CAM_3S3', 'Caminhão 3S3', 'Caminhão tipo 3S3 (6 eixos)', 3.5, '#7d261a', '3S3', 55),
    ('CAM_PESADO', 'CAM_3I3', 'Caminhão 3I3', 'Caminhão tipo 3I3 (6 eixos)', 3.5, '#722317', '3I3', 56),
    ('CAM_PESADO', 'CAM_3J3', 'Caminhão 3J3', 'Caminhão tipo 3J3 (6 eixos)', 3.5, '#672014', '3J3', 57),
    ('CAM_PESADO', 'CAM_3C3', 'Caminhão 3C3', 'Caminhão tipo 3C3 (6 eixos)', 3.5, '#5c1d11', '3C3', 58),
    ('CAM_PESADO', 'CAM_3D3', 'Caminhão 3D3', 'Caminhão tipo 3D3 (6 eixos)', 3.5, '#511a0e', '3D3', 59),
    ('CAM_PESADO', 'CAM_3D4', 'Caminhão 3D4', 'Caminhão tipo 3D4 (7 eixos)', 4.0, '#661f15', '3D4', 64),
    ('CAM_PESADO', 'CAM_3Q4', 'Caminhão 3Q4', 'Caminhão tipo 3Q4 (7 eixos)', 4.0, '#5b1c12', '3Q4', 65),
    ('CAM_PESADO', 'CAM_3T6', 'Caminhão 3T6', 'Caminhão tipo 3T6 (9 eixos)', 5.0, '#2d0f07', '3T6', 85),
    
    -- CATEGORIA PEDESTRE
    ('PEDESTRE', 'PEDESTRE', 'Pedestre', 'Pedestres em geral', 0.1, '#95a5a6', 'PM', 100),
    ('PEDESTRE', 'PEDESTRE_MR', 'Pedestre Mobilidade Reduzida', 'Pedestre com mobilidade reduzida', 0.1, '#7f8c8d', 'PMR', 101),
    
    -- CATEGORIA ESPECIAL
    ('ESPECIAL', 'ANIMAL', 'Animal', 'Animais diversos', 0.2, '#34495e', 'Animal', 110)
) AS tipos(categoria, codigo, nome, descricao, equivalente_auto, cor_padrao, codigo_original, ordem_exibicao)
ON CONFLICT (codigo, COALESCE(projeto_id, 0)) DO NOTHING;