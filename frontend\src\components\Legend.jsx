// frontend/src/components/Legend.jsx - COMPONENTE DE LEGENDA MELHORADO

import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { configIndicadores, getLegendItems } from '../utils/mapHelpers';

export default function Legend({ indicator, data, isVisible = true }) {
  const [legendItems, setLegendItems] = useState([]);
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    if (!indicator || !data) {
      setLegendItems([]);
      return;
    }

    const config = configIndicadores[indicator];
    if (!config) {
      setLegendItems([]);
      return;
    }

    // Para indicadores categóricos dinâmicos, coletamos todos os valores únicos
    let allValues = null;
    if (config.isCategorical && config.coresCategoricas) {
      allValues = [];
      Object.values(data.pedsData || {}).forEach(ped => {
        const primeiroHorario = Object.keys(ped.dados_horarios || {})[0];
        if (primeiroHorario) {
          const valor = ped.dados_horarios[primeiroHorario][indicator];
          if (valor !== null && valor !== undefined) {
            allValues.push(valor);
          }
        }
      });
    }

    const items = getLegendItems(indicator, allValues);
    setLegendItems(items);
  }, [indicator, data]);

  if (!isVisible || !indicator || legendItems.length === 0) {
    return null;
  }

  const config = configIndicadores[indicator];

  return (
    <div 
      className="fixed bottom-4 right-4 map-legend" 
      style={{ 
        zIndex: 1000,
        position: 'fixed',
        bottom: '16px',
        right: '16px'
      }}
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden min-w-64 max-w-80" style={{ boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)' }}>
        {/* Header da Legenda */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 text-sm leading-tight">
                {config?.titulo || indicator}
              </h4>
              {config?.descricao && (
                <p className="text-xs text-gray-600 mt-1 leading-tight">
                  {config.descricao}
                </p>
              )}
            </div>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="ml-2 p-1 hover:bg-gray-200 rounded transition-colors duration-200"
              title={isMinimized ? "Expandir legenda" : "Minimizar legenda"}
            >
              <svg 
                className={`w-4 h-4 text-gray-600 transform transition-transform duration-200 ${isMinimized ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Conteúdo da Legenda */}
        {!isMinimized && (
          <div className="px-4 py-3">
            <div className="space-y-2">
              {legendItems.map((item, index) => (
                <LegendItem 
                  key={`${indicator}-${index}`} 
                  item={item} 
                  indicator={indicator}
                />
              ))}
            </div>

            {/* Informações adicionais para indicadores especiais */}
            {indicator === 'Passageiros' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  <span className="font-medium">Tamanho do círculo:</span> Proporcional ao volume total de passageiros
                </p>
                <p className="text-xs text-gray-600 mt-1">
                  <span className="font-medium">Número central:</span> Total de embarques + desembarques
                </p>
              </div>
            )}

            {config?.unidade && indicator !== 'Passageiros' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  <span className="font-medium">Unidade:</span> {config.unidade}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Componente para item individual da legenda
function LegendItem({ item, indicator }) {
  return (
    <div className="flex items-center gap-3 group">
      {/* Indicador de cor */}
      <div className="flex-shrink-0">
        {indicator === 'Passageiros' ? (
          // Para indicador de passageiros, mostra um círculo especial
          <div 
            className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
            style={{ backgroundColor: item.cor }}
          />
        ) : (
          // Para outros indicadores, mostra um círculo simples
          <div 
            className="w-4 h-4 rounded-full border border-gray-300 shadow-sm"
            style={{ backgroundColor: item.cor }}
          />
        )}
      </div>

      {/* Texto e descrição */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-800 truncate">
            {item.texto}
          </span>
        </div>
        
        {item.descricao && item.descricao !== item.texto && (
          <p className="text-xs text-gray-600 mt-0.5 leading-tight">
            {item.descricao}
          </p>
        )}
      </div>
    </div>
  );
}

// Componente de legenda compacta para usar na sidebar
export function CompactLegend({ indicator, data }) {
  const [legendItems, setLegendItems] = useState([]);

  useEffect(() => {
    if (!indicator || !data) {
      setLegendItems([]);
      return;
    }

    const config = configIndicadores[indicator];
    if (!config) {
      setLegendItems([]);
      return;
    }

    // Coleta valores únicos para indicadores categóricos
    let allValues = null;
    if (config.isCategorical && config.coresCategoricas) {
      allValues = [];
      Object.values(data.pedsData || {}).forEach(ped => {
        const primeiroHorario = Object.keys(ped.dados_horarios || {})[0];
        if (primeiroHorario) {
          const valor = ped.dados_horarios[primeiroHorario][indicator];
          if (valor !== null && valor !== undefined) {
            allValues.push(valor);
          }
        }
      });
    }

    const items = getLegendItems(indicator, allValues);
    setLegendItems(items);
  }, [indicator, data]);

  if (!indicator || legendItems.length === 0) {
    return null;
  }

  const config = configIndicadores[indicator];

  return (
    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
      <h5 className="text-sm font-semibold text-gray-800 mb-2">
        Legenda: {config?.titulo || indicator}
      </h5>
      
      <div className="space-y-1">
        {legendItems.map((item, index) => (
          <div key={`compact-${indicator}-${index}`} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full border border-gray-300"
              style={{ backgroundColor: item.cor }}
            />
            <span className="text-xs text-gray-700 truncate">
              {item.texto}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

Legend.propTypes = {
  indicator: PropTypes.string,
  data: PropTypes.object,
  isVisible: PropTypes.bool
};

LegendItem.propTypes = {
  item: PropTypes.shape({
    cor: PropTypes.string.isRequired,
    texto: PropTypes.string.isRequired,
    descricao: PropTypes.string
  }).isRequired,
  indicator: PropTypes.string.isRequired
};

CompactLegend.propTypes = {
  indicator: PropTypes.string,
  data: PropTypes.object
};