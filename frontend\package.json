{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "chart.js": "^4.5.0", "leaflet": "^1.9.4", "nouislider": "^15.8.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.19", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^7.0.5"}}