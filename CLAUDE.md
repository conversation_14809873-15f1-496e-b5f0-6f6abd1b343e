# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a full-stack traffic analysis platform with Flask backend and React frontend, running in Docker containers.

**Stack:**
- Backend: Flask + SQLAlchemy + PostgreSQL
- Frontend: React + Vite + TailwindCSS + Leaflet maps
- Deployment: Docker Compose with 3 services (backend, frontend, database)

**Core Structure:**
- `backend/`: Flask API with route blueprints for different traffic products
- `frontend/`: React SPA with product-specific dashboards and components
- Data flows from Excel imports → PostgreSQL → REST API → React components

## Traffic Analysis Products

The platform supports multiple traffic analysis products:
- **PED**: Pedestrian crossing analysis
- **CCV**: Vehicle counting and classification 
- **VIA**: Road analysis
- **FTP/TLP/POD**: Additional traffic products

Each product has its own:
- Database models in `backend/models.py`
- API routes in `backend/routes_*.py` blueprints
- Frontend dashboard in `frontend/src/pages/Dashboard*.jsx`
- Map components in `frontend/src/components/Map*.jsx`

## Development Commands

### Backend (Flask)
```bash
# Run development server
cd backend && flask --app app run --host=0.0.0.0 --port=5000

# Database operations
flask db-create              # Create all tables
flask setup-ccv              # Setup CCV product with default data
flask import-ped             # Import PED data from dadosped.xlsx
flask import-ccv             # Import CCV data from data/ccv_dados.xlsx

# Check application health
curl http://localhost:5000/health
```

### Frontend (React)
```bash
cd frontend
npm run dev        # Development server (Vite)
npm run build      # Production build
npm run lint       # ESLint checks
npm run preview    # Preview production build
```

### Docker Development
```bash
# Full stack development
docker-compose up --build

# Rebuild specific service
docker-compose up --build backend
docker-compose up --build frontend

# Access services
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
# Database: localhost:5432
```

## Database Architecture

**Organizational Structure:**
- `Empresa` → `Projeto` → Traffic Product Models
- Multi-tenant: each project belongs to a company
- Each traffic product (PED, CCV, VIA) links to a project

**Key Models:**
- Core: `Empresa`, `Projeto`, `UsuarioEmpresa`
- PED: `Ped`, `PedDadosHorarios` 
- CCV: `CcvPonto`, `CcvMovimento`, `CcvClassificacao`
- VIA: `Via`, `ViaDados`

## Import System

Excel data import is handled through:
1. Flask CLI commands (`flask import-*`)
2. Web interface at `/admin` (DatabaseAdmin component)
3. Product-specific import pages (`/importar`, CcvImport component)

**Import Flow:**
- Excel files → Pandas processing → SQLAlchemy models → PostgreSQL
- Data validation and cleaning in import scripts
- Progress tracking through ImportProgressModal component

## API Architecture

RESTful API with blueprint organization:
- `routes_admin.py`: Database administration endpoints
- `routes_ccv.py`: CCV product endpoints with optimization for large datasets
- `routes_ped.py`: PED product endpoints
- `routes_via.py`: VIA product endpoints  
- `routes_geojson.py`: Geographic data endpoints

**Key Endpoints:**
- `/health`: Application health check
- `/api/dados`: PED data for frontend
- `/api/ccv/*`: CCV-specific endpoints
- `/api/admin/*`: Database management

## Frontend Architecture

**Router Structure:**
- `/`: Main PED dashboard
- `/ccv`: CCV dashboard  
- `/via`: VIA dashboard
- `/admin`: Database administration
- `/importar`: Data import interface
- Product detail pages: `/ped/:pedName`, `/ccv/:pontoName`

**Key Components:**
- `Map*.jsx`: Leaflet-based map components for each product
- `Sidebar*.jsx`: Product-specific control panels
- `Dashboard*.jsx`: Main product views with data visualization
- Service files in `api/`: Axios-based API communication

## Data Visualization

- **Maps**: Leaflet with custom markers and popups
- **Charts**: Chart.js integration via react-chartjs-2
- **Filtering**: NoUISlider for range controls
- **Responsive**: TailwindCSS grid and flexbox layouts

## Environment Configuration

Environment variables in `.env` file:
- `DATABASE_URL`: PostgreSQL connection string
- `DB_NAME`, `DB_USER`, `DB_PASS`: Database credentials
- `SECRET_KEY`: Flask session secret

## Common Issues

- **NaN/Infinity values**: Backend has `clean_numeric_value()` function to handle Excel data issues
- **CORS**: Configured for localhost:3000 development
- **Large datasets**: CCV routes optimized with chunked processing and progress tracking
- **Database encoding**: UTF-8 configured for Portuguese text handling