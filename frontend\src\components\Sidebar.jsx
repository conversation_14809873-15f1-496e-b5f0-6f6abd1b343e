// frontend/src/components/Sidebar.jsx - CÓDIGO COMPLETO CORRIGIDO

import { useEffect, useRef } from "react";
import noUiSlider from "nouislider";
import "nouislider/dist/nouislider.css";
import { CompactLegend } from "./Legend";

export default function Sidebar({
  indicadores,
  indicadorSelecionado,
  setIndicadorSelecionado,
  cenario,
  setCenario,
  mostrarCenario,
  modoIntervalo,
  setModoIntervalo,
  horarios,
  intervaloDeTempo,
  setIntervaloDeTempo,
  tamanhoIcone,
  setTamanhoIcone,
  data // Adiciona data como prop para a legenda
}) {
  // Refs para os elementos do DOM onde os sliders serão montados
  const timeSliderElmRef = useRef(null);
  const iconSizeSliderElmRef = useRef(null);
  
  // Refs para guardar as instâncias da biblioteca noUiSlider
  const timeSliderInstanceRef = useRef(null);
  const iconSizeSliderInstanceRef = useRef(null);

  const horariosValidos = horarios && horarios.length > 1;
  const maxIdx = horariosValidos ? horarios.length - 1 : 0;

  // --- EFEITO PARA CRIAR/RECRIAR O SLIDER DE HORÁRIO ---
  useEffect(() => {
    const elm = timeSliderElmRef.current;
    
    // Valida se o elemento existe e se temos horários válidos
    if (!elm || !horariosValidos) {
      console.log("Slider de horário: aguardando elemento DOM ou horários válidos");
      return;
    }

    // Destroi a instância anterior se existir
    if (timeSliderInstanceRef.current) {
      console.log("Destruindo slider de horário anterior");
      timeSliderInstanceRef.current.destroy();
      timeSliderInstanceRef.current = null;
    }

    try {
      // Determina os valores iniciais baseado no modo
      const isFixo = modoIntervalo === "fixo";
      let startHandles;
      
      if (isFixo) {
        // Modo fixo: apenas um handle
        startHandles = [intervaloDeTempo[0] || 0];
      } else {
        // Modo livre: dois handles
        startHandles = [
          intervaloDeTempo[0] || 0, 
          intervaloDeTempo[1] || Math.min((intervaloDeTempo[0] || 0) + 3, maxIdx)
        ];
      }

      console.log("Criando slider de horário:", { modoIntervalo, startHandles, maxIdx });

      // Cria o slider
      const instance = noUiSlider.create(elm, {
        start: startHandles,
        connect: isFixo ? [true, false] : true,
        range: { min: 0, max: maxIdx },
        step: 1,
        behaviour: "tap-drag",
        tooltips: false, // Desabilita tooltips para evitar conflitos
      });

      timeSliderInstanceRef.current = instance;

      // Listener para quando o usuário termina de arrastar
      instance.on("set", (values) => {
        const newIntervalo = values.map(v => Math.round(Number(v)));
        console.log("Slider de horário atualizado:", newIntervalo);
        setIntervaloDeTempo(newIntervalo);
      });

      console.log("Slider de horário criado com sucesso");

    } catch (error) {
      console.error("Erro ao criar slider de horário:", error);
    }

    // Cleanup function
    return () => {
      if (timeSliderInstanceRef.current) {
        console.log("Limpando slider de horário");
        timeSliderInstanceRef.current.destroy();
        timeSliderInstanceRef.current = null;
      }
    };
  }, [modoIntervalo, horarios.length, maxIdx]); // Recria quando modo ou horários mudam

  // --- EFEITO PARA SINCRONIZAR VALORES DO SLIDER DE HORÁRIO ---
  useEffect(() => {
    const instance = timeSliderInstanceRef.current;
    if (!instance || !intervaloDeTempo || intervaloDeTempo.length === 0) return;

    try {
      // Obtém os valores atuais do slider
      const currentValues = instance.get(true);
      const currentValuesArray = Array.isArray(currentValues) ? currentValues : [currentValues];
      const currentValuesRounded = currentValuesArray.map(v => Math.round(Number(v)));
      
      // Compara com os valores do estado
      const shouldUpdate = JSON.stringify(currentValuesRounded) !== JSON.stringify(intervaloDeTempo);
      
      if (shouldUpdate) {
        console.log("Sincronizando slider de horário:", { current: currentValuesRounded, new: intervaloDeTempo });
        instance.set(intervaloDeTempo);
      }
    } catch (error) {
      console.error("Erro ao sincronizar slider de horário:", error);
    }
  }, [intervaloDeTempo]);

  // --- EFEITO PARA CRIAR O SLIDER DE TAMANHO DO ÍCONE ---
  useEffect(() => {
    const elm = iconSizeSliderElmRef.current;
    if (!elm) {
      console.log("Slider de tamanho: aguardando elemento DOM");
      return;
    }

    // Evita criar múltiplas instâncias
    if (iconSizeSliderInstanceRef.current) {
      console.log("Slider de tamanho já existe");
      return;
    }

    try {
      console.log("Criando slider de tamanho do ícone");
      
      const instance = noUiSlider.create(elm, {
        start: tamanhoIcone,
        connect: [true, false],
        range: { min: 0.5, max: 2.0 },
        step: 0.1,
        tooltips: false,
      });

      iconSizeSliderInstanceRef.current = instance;

      // Listener para atualização em tempo real
      instance.on("update", (values) => {
        const newSize = parseFloat(values[0]);
        setTamanhoIcone(newSize);
      });

      console.log("Slider de tamanho criado com sucesso");

    } catch (error) {
      console.error("Erro ao criar slider de tamanho:", error);
    }

    // Cleanup function
    return () => {
      if (iconSizeSliderInstanceRef.current) {
        console.log("Limpando slider de tamanho");
        iconSizeSliderInstanceRef.current.destroy();
        iconSizeSliderInstanceRef.current = null;
      }
    };
  }, []); // Cria apenas uma vez

  // --- FUNÇÕES AUXILIARES ---
  const nomesSubItens = {
    'Passageiros': '1.1 - Proporção Embarque/Desembarque', 
    'Onibus': '1.2 - Frequência de Ônibus', // NOVO INDICADOR ADICIONADO
    'Total Travessias': '1.3 - Total de Travessias', 
    'Placa': '1.4 - Presença de Placa', 
    'Abrigo': '1.5 - Presença de Abrigo',
    'Baia': '1.6 - Presença de Baia', 
    'Mancha Urbana': '1.7 - Dentro da Mancha Urbana', 
    'SH': '1.8 - Segmento Homogêneo (SH)',
    'GS': '2.1 - Grau de Saturação', 
    'P(f)': '2.2 - Probabilidade de Fila', 
    'P(0)': '2.3 - Probabilidade de Ponto Vazio'
  };
  
  // ATUALIZAR OS GRUPOS PARA INCLUIR ÔNIBUS
  const grupoTematicos = ['Passageiros', 'Onibus', 'Total Travessias', 'Placa', 'Abrigo', 'Baia', 'Mancha Urbana', 'SH'];
  const grupoDesempenho = ['GS', 'P(f)', 'P(0)'];

  const getSliderEndTime = () => {
    if (!horariosValidos || !intervaloDeTempo) return '';
    if (modoIntervalo === 'livre' && intervaloDeTempo.length > 1) {
      const endIndex = Math.min(intervaloDeTempo[1], horarios.length - 1);
      return horarios[endIndex];
    }
    return '';
  };

  const getSliderStartTime = () => {
    if (!horariosValidos || !intervaloDeTempo) return '-';
    const startIndex = Math.min(intervaloDeTempo[0] || 0, horarios.length - 1);
    return horarios[startIndex];
  };

  return (
    <aside 
      id="sidebar" 
      className="bg-white w-80 p-6 shadow-lg z-20 transform transition-transform duration-300 ease-in-out overflow-y-auto"
    >
      <h3 className="text-2xl font-bold text-gray-800 mb-6">Menu</h3>

      {/* Seletor de Indicador */}
      <div className="mb-4">
        <label htmlFor="dropdown-indicador" className="block text-sm font-medium text-gray-700 mb-1">
          1. Selecione o Indicador:
        </label>
        <select 
          id="dropdown-indicador" 
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
          value={indicadorSelecionado} 
          onChange={(e) => setIndicadorSelecionado(e.target.value)}
        >
          <optgroup label="1. Mapas Temáticos">
            {indicadores && grupoTematicos.map(key => (
              <option key={key} value={key}>
                {nomesSubItens[key] || indicadores[key]}
              </option>
            ))}
          </optgroup>
          <optgroup label="2. Indicadores de Desempenho">
            {indicadores && grupoDesempenho.map(key => (
              <option key={key} value={key}>
                {nomesSubItens[key] || indicadores[key]}
              </option>
            ))}
          </optgroup>
        </select>
      </div>

      {/* Seletor de Cenário */}
      {mostrarCenario && (
        <div id="cenario-container" className="mb-4">
          <label htmlFor="dropdown-cenario" className="block text-sm font-medium text-gray-700 mb-1">
            2. Cenário (nº de posições):
          </label>
          <select 
            id="dropdown-cenario" 
            className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            value={cenario} 
            onChange={(e) => setCenario(e.target.value)}
          >
            {[1, 2, 3, 4, 5].map(n => (
              <option key={n} value={n}>
                {n} Posição{n > 1 ? 's' : ''}
              </option>
            ))}
          </select>
        </div>
      )}

      <hr className="my-6" />

      {/* Modo de Intervalo */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          3. Modo de Intervalo:
        </label>
        <div className="flex items-center space-x-2 rounded-md bg-gray-200 p-1">
          {['fixo', 'livre'].map(modo => (
            <div className="flex-1" key={modo}>
              <input 
                type="radio" 
                id={`modo-${modo}`} 
                name="modo_intervalo" 
                value={modo} 
                className="sr-only peer" 
                checked={modoIntervalo === modo} 
                onChange={(e) => setModoIntervalo(e.target.value)} 
              />
              <label 
                htmlFor={`modo-${modo}`} 
                className="block w-full text-center py-1 px-2 rounded-md cursor-pointer text-sm peer-checked:bg-gray-700 peer-checked:text-white peer-checked:shadow-inner transition-all"
              >
                {modo === 'fixo' ? '1 Hora' : 'Intervalo Livre'}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Slider de Horário */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          4. Intervalo de Horário:
        </label>
        <div id="slider-container" className="pt-2">
          <div 
            ref={timeSliderElmRef} 
            id="time-slider" 
            className="h-4 mb-2"
          ></div>
          <div id="slider-values" className="flex justify-between mt-2 text-xs text-gray-500">
            <span className="font-medium">{getSliderStartTime()}</span>
            {modoIntervalo === 'livre' && (
              <span className="font-medium">{getSliderEndTime()}</span>
            )}
          </div>
          {/* Indicador visual do status */}
          <div className="mt-2 text-xs text-gray-400">
            {!horariosValidos && "Aguardando dados..."}
            {horariosValidos && (
              <span>
                Modo: {modoIntervalo === 'fixo' ? 'Fixo (1h)' : 'Intervalo Livre'} | 
                Total: {horarios.length} horários
              </span>
            )}
          </div>
        </div>
      </div>
      
      {/* Slider de Tamanho */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          5. Tamanho dos Ícones:
        </label>
        <div className="pt-2 px-2">
          <div 
            ref={iconSizeSliderElmRef} 
            id="icon-size-slider" 
            className="h-4 mb-2"
          ></div>
          <div className="text-center mt-2 text-xs text-gray-500 font-semibold">
            <span>{Math.round(tamanhoIcone * 100)}%</span>
          </div>
        </div>
      </div>

      <div id="legenda-container" className="mt-6">
        <CompactLegend indicator={indicadorSelecionado} data={data} />
      </div>
    </aside>
  );
}