// frontend/src/components/IndicatorsLegend.jsx
import PropTypes from "prop-types";

/**
 * Mostra uma legenda simples para o indicador selecionado.
 * Ajuste `legendConfig` conforme seus dados reais.
 */
export default function IndicatorsLegend({ indicator }) {
  if (!indicator) return null; // nada selecionado → nenhuma legenda

  /** Mapeie cada indicador para cores / rótulos */
  const legendConfig = {
    GS: {
      title: "Grau de Saturação (GS)",
      items: [
        { label: "≤ 0,60 – Livre", color: "#2E7D32" },
        { label: "0,60 – 0,85 – Estável", color: "#FBC02D" },
        { label: "≥ 0,85 – Saturado", color: "#E64A19" },
      ],
    },
    "P(f)": {
      title: "Prob. de Fila (P(f))",
      items: [
        { label: "0 – 20 %", color: "#64b5f6" },
        { label: "20 – 50 %", color: "#1976d2" },
        { label: "> 50 %", color: "#0d47a1" },
      ],
    },
    // adicione outros indicadores aqui…
  };

  const cfg = legendConfig[indicator];
  if (!cfg) return null; // indicador sem legenda configurada

  return (
    <div className="bg-white rounded-xl shadow-lg p-4 space-y-2 text-sm">
      <h4 className="font-semibold text-gray-800 mb-2">{cfg.title}</h4>

      {cfg.items.map(({ label, color }) => (
        <div key={label} className="flex items-center gap-2">
          <span
            className="inline-block w-4 h-4 rounded"
            style={{ background: color }}
          />
          <span>{label}</span>
        </div>
      ))}
    </div>
  );
}

IndicatorsLegend.propTypes = {
  indicator: PropTypes.string,
};
