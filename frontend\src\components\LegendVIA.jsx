// frontend/src/components/LegendVIA.jsx

import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

export default function LegendVIA({ indicador, dados, isVisible = true }) {
  const [legendItems, setLegendItems] = useState([]);
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    if (!indicador || !dados?.projeto_via?.indicadores_config) {
      setLegendItems([]);
      return;
    }

    const configIndicador = dados.projeto_via.indicadores_config[indicador];
    if (!configIndicador) {
      setLegendItems([]);
      return;
    }

    const trechos = Object.values(dados.trechos || {});
    const valores = trechos
      .map(t => t.atributos_originais?.[indicador])
      .filter(v => v !== null && v !== undefined);

    if (valores.length === 0) {
      setLegendItems([]);
      return;
    }

    let items = [];

    if (configIndicador.tipo === 'numerico') {
      // Para indicadores numéricos, cria faixas
      const numericos = valores.map(v => Number(v)).filter(v => !isNaN(v));
      if (numericos.length > 0) {
        const min = Math.min(...numericos);
        const max = Math.max(...numericos);
        
        if (min !== max) {
          const quartis = calcularQuartis(numericos);
          items = [
            { cor: '#3b82f6', texto: `Baixo (${min.toFixed(1)} - ${quartis.q1.toFixed(1)})`, faixa: [min, quartis.q1] },
            { cor: '#10b981', texto: `Médio-Baixo (${quartis.q1.toFixed(1)} - ${quartis.q2.toFixed(1)})`, faixa: [quartis.q1, quartis.q2] },
            { cor: '#eab308', texto: `Médio-Alto (${quartis.q2.toFixed(1)} - ${quartis.q3.toFixed(1)})`, faixa: [quartis.q2, quartis.q3] },
            { cor: '#dc2626', texto: `Alto (${quartis.q3.toFixed(1)} - ${max.toFixed(1)})`, faixa: [quartis.q3, max] }
          ];
        } else {
          items = [{ cor: '#3b82f6', texto: `Valor único: ${min}`, faixa: [min, min] }];
        }
      }
    } else if (configIndicador.tipo === 'categoria') {
      // Para indicadores categóricos
      const cores = [
        '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
        '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
      ];
      
      const categorias = [...new Set(valores)].sort();
      items = categorias.map((categoria, index) => ({
        cor: cores[index % cores.length],
        texto: String(categoria),
        categoria: categoria
      }));
    }

    setLegendItems(items);
  }, [indicador, dados]);

  if (!isVisible || !indicador || legendItems.length === 0) {
    return null;
  }

  const configIndicador = dados?.projeto_via?.indicadores_config?.[indicador];

  return (
    <div 
      className="fixed bottom-4 right-4 via-legend" 
      style={{ 
        zIndex: 1000,
        position: 'fixed',
        bottom: '16px',
        right: '16px'
      }}
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden min-w-64 max-w-80" 
           style={{ boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)' }}>
        
        {/* Header da Legenda */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 text-sm leading-tight flex items-center">
                <i className="fas fa-road mr-2 text-indigo-600"></i>
                {configIndicador?.nome_personalizado || indicador}
              </h4>
              {configIndicador?.unidade && (
                <p className="text-xs text-gray-600 mt-1 leading-tight">
                  Unidade: {configIndicador.unidade}
                </p>
              )}
            </div>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="ml-2 p-1 hover:bg-gray-200 rounded transition-colors duration-200"
              title={isMinimized ? "Expandir legenda" : "Minimizar legenda"}
            >
              <svg 
                className={`w-4 h-4 text-gray-600 transform transition-transform duration-200 ${isMinimized ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Conteúdo da Legenda */}
        {!isMinimized && (
          <div className="px-4 py-3">
            
            {/* Items da legenda */}
            <div className="space-y-2 mb-4">
              {legendItems.map((item, index) => (
                <LegendItemVIA 
                  key={`${indicador}-${index}`} 
                  item={item} 
                />
              ))}
            </div>

            {/* Informações do tipo de indicador */}
            <div className="pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-600 space-y-1">
                <div className="flex justify-between">
                  <span>Tipo:</span>
                  <span className="font-medium capitalize">{configIndicador?.tipo}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total de trechos:</span>
                  <span className="font-medium">{Object.keys(dados?.trechos || {}).length}</span>
                </div>
              </div>
            </div>

            {/* Informações do sistema */}
            <div className="pt-3 border-t border-gray-200 mt-3">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex items-center">
                  <i className="fas fa-file-import mr-1"></i>
                  <span>Importado do Aimsun</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-cogs mr-1"></i>
                  <span>Indicadores configuráveis</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-map mr-1"></i>
                  <span>Sistema VIA v1.0</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Componente para item individual da legenda
function LegendItemVIA({ item }) {
  return (
    <div className="flex items-center gap-3 group">
      {/* Indicador de cor */}
      <div className="flex-shrink-0">
        <div 
          className="w-4 h-4 rounded border border-gray-300 shadow-sm"
          style={{ backgroundColor: item.cor }}
        />
      </div>

      {/* Texto e descrição */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-800 truncate">
            {item.texto}
          </span>
        </div>
      </div>
    </div>
  );
}

// Função auxiliar para calcular quartis
function calcularQuartis(valores) {
  const sorted = [...valores].sort((a, b) => a - b);
  const len = sorted.length;
  
  return {
    q1: sorted[Math.floor(len * 0.25)],
    q2: sorted[Math.floor(len * 0.5)],  // Mediana
    q3: sorted[Math.floor(len * 0.75)]
  };
}

LegendVIA.propTypes = {
  indicador: PropTypes.string.isRequired,
  dados: PropTypes.object,
  isVisible: PropTypes.bool
};

LegendItemVIA.propTypes = {
  item: PropTypes.shape({
    cor: PropTypes.string.isRequired,
    texto: PropTypes.string.isRequired
  }).isRequired
};