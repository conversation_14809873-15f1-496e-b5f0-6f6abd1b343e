# backend/import_ccv_data.py

#!/usr/bin/env python3
"""
Script para importar dados CCV - Versão Robusta e Definitiva
"""

import pandas as pd
import sys
import os
from datetime import datetime
from app import app, db
from models import Empresa, Projeto, CCVPonto, CCVDia, CCVMovimento, CCVTipoVeiculo, CCVContagem

def importar_dados_excel(arquivo_excel):
    """
    Importa dados de contagem de um arquivo Excel de forma robusta, garantindo a
    sincronização com o banco de dados a cada passo.
    """
    print(f"📊 Importando dados do arquivo: {arquivo_excel}")
    
    try:
        df = pd.read_excel(arquivo_excel)
        print(f"📋 Arquivo Excel lido: {len(df)} linhas.")
        
        colunas_obrigatorias = ['nome_projeto', 'ponto', 'data', 'hora', 'movimento', 'tipo_veiculo', 'qte']
        if not all(col in df.columns for col in colunas_obrigatorias):
            print(f"❌ Erro: Colunas obrigatórias faltando. Verifique se o arquivo possui: {colunas_obrigatorias}")
            return 0
        
        mapeamento_tipos = {
            'carro': 'AUTO', 'auto': 'AUTO', 'van': 'VAN', 'cam_leve': 'CAM_LEVE',
            'cam_pesado': 'CAM_PESADO', 'onibus': 'ONIBUS', 'moto': 'MOTO',
            'bicicleta': 'BICICLETA', 'outros': 'OUTROS'
        }
        df['tipo_veiculo_normalizado'] = df['tipo_veiculo'].str.lower().map(mapeamento_tipos).fillna(df['tipo_veiculo'].str.upper())
        
        tipos_veiculo_map = {tv.codigo: tv for tv in CCVTipoVeiculo.query.all()}
        
        projetos_excel = df['nome_projeto'].unique()
        total_registros_adicionados = 0
        
        for nome_projeto in projetos_excel:
            with db.session.begin_nested():
                print(f"\n🏢 Processando projeto: {nome_projeto}")
                df_projeto = df[df['nome_projeto'] == nome_projeto]

                projeto = Projeto.query.filter_by(codigo_projeto=nome_projeto).first()
                if not projeto:
                    empresa = Empresa.query.filter_by(codigo_empresa='FRATAR').first()
                    if not empresa: raise Exception("Empresa 'FRATAR' não encontrada. Execute 'flask setup-ccv'.")
                    
                    projeto = Projeto(empresa_id=empresa.id, codigo_projeto=nome_projeto, nome_projeto=f"Projeto {nome_projeto}")
                    db.session.add(projeto)
                    db.session.flush()

                for index, row in df_projeto.iterrows():
                    try:
                        ponto = CCVPonto.query.filter_by(projeto_id=projeto.id, codigo_ponto=row['ponto']).first()
                        if not ponto:
                            ponto = CCVPonto(projeto_id=projeto.id, codigo_ponto=row['ponto'], nome_ponto=f"Ponto {row['ponto']}")
                            db.session.add(ponto)
                            db.session.flush()

                        data_coleta = pd.to_datetime(row['data']).date()
                        dia = CCVDia.query.filter_by(ponto_id=ponto.id, data_coleta=data_coleta).first()
                        if not dia:
                            dia = CCVDia(ponto_id=ponto.id, data_coleta=data_coleta, dia_semana=data_coleta.weekday() + 1, responsavel='Importação Excel')
                            db.session.add(dia)
                            db.session.flush()

                        movimento = CCVMovimento.query.filter_by(ponto_id=ponto.id, codigo_movimento=row['movimento']).first()
                        if not movimento:
                            movimento = CCVMovimento(ponto_id=ponto.id, codigo_movimento=row['movimento'], nome_movimento=f"Movimento {row['movimento']}")
                            db.session.add(movimento)
                            db.session.flush()

                        tipo_veiculo = tipos_veiculo_map.get(row['tipo_veiculo_normalizado'])
                        if not tipo_veiculo:
                            continue
                        
                        # --- CORREÇÃO FINAL ---
                        # Removemos o formato fixo para deixar o pandas interpretar a hora automaticamente (HH:MM ou HH:MM:SS)
                        hora_obj = pd.to_datetime(str(row['hora'])).time()
                        # --- FIM DA CORREÇÃO ---
                        
                        if not CCVContagem.query.filter_by(dia_id=dia.id, movimento_id=movimento.id, tipo_veiculo_id=tipo_veiculo.id, hora=hora_obj).first():
                            periodo_fim = (datetime.combine(data_coleta, hora_obj) + pd.Timedelta(minutes=15)).time()
                            contagem = CCVContagem(
                                dia_id=dia.id, movimento_id=movimento.id, tipo_veiculo_id=tipo_veiculo.id,
                                hora=hora_obj, periodo_inicio=hora_obj, periodo_fim=periodo_fim,
                                quantidade=int(row['qte'])
                            )
                            db.session.add(contagem)
                            total_registros_adicionados += 1
                    
                    except Exception as e_row:
                        print(f"    ❌ Erro ao processar linha {index + 2}: {e_row}")
                        raise

            db.session.commit()
            print(f"  ✅ Projeto '{nome_projeto}' processado com sucesso.")

        print(f"\n🎉 Importação concluída! {total_registros_adicionados} novos registros adicionados.")
        return total_registros_adicionados

    except Exception as e_main:
        print(f"❌ Um erro crítico ocorreu, a importação foi interrompida: {e_main}")
        db.session.rollback()
        return 0

@app.cli.command("import-ccv")
def import_ccv_data_command():
    with app.app_context():
        arquivo_dados = os.path.join('data', 'ccv_dados.xlsx')
        if not os.path.exists(arquivo_dados):
            print(f"⚠️ Arquivo não encontrado: {arquivo_dados}")
            return
        importar_dados_excel(arquivo_dados)

if __name__ == '__main__':
    with app.app_context():
        arquivo_dados = os.path.join('data', 'ccv_dados.xlsx')
        importar_dados_excel(arquivo_dados)