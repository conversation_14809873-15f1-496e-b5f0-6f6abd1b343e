// frontend/src/components/SidebarCCV.jsx - VERSÃO FINAL COMPLETA

import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

export default function SidebarCCV({
  empresas,
  projetos,
  dados,
  empresaSelecionada,
  setEmpresaSelecionada,
  projetoSelecionado,
  setProjetoSelecionado,
  indicadorSelecionado,
  setIndicadorSelecionado,
  // ATUALIZAÇÃO: Novas props para lidar com grupos
  grupos,
  grupoSelecionado,
  setGrupoSelecionado
}) {
  const navigate = useNavigate();

  // Indicadores disponíveis para CCV
  const indicadores = {
    'Volume Total': 'Volume Total de Veículos',
    'Composição Veicular': 'Composição por Grupo de Veículos',
    'Pico Horário': 'Horário de Pico de Tráfego',
    'Movimentos': 'Análise por Movimento Direcional',
    'Densidade': 'Densidade de Tráfego'
  };

  return (
    <aside className="bg-white w-80 p-6 shadow-lg z-20 transform transition-transform duration-300 ease-in-out overflow-y-auto">
      <div className="space-y-6">
        
        {/* Cabeçalho */}
        <div>
          <h3 className="text-2xl font-bold text-gray-800 mb-2">CCV - Contagem Veicular</h3>
          <p className="text-sm text-gray-600">Análise de volumes e composição do tráfego</p>
        </div>

        {/* Seleção de Empresa */}
        <div>
          <label htmlFor="empresa-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-building mr-2"></i>Empresa:
          </label>
          <select 
            id="empresa-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            value={empresaSelecionada || ''} 
            onChange={(e) => setEmpresaSelecionada(Number(e.target.value))}
          >
            <option value="">Selecione uma empresa</option>
            {empresas.map(empresa => (
              <option key={empresa.id} value={empresa.id}>
                {empresa.nome_empresa}
              </option>
            ))}
          </select>
        </div>

        {/* Seleção de Projeto */}
        <div>
          <label htmlFor="projeto-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-folder mr-2"></i>Projeto:
          </label>
          <select 
            id="projeto-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            value={projetoSelecionado || ''} 
            onChange={(e) => setProjetoSelecionado(Number(e.target.value))}
            disabled={!empresaSelecionada}
          >
            <option value="">Selecione um projeto</option>
            {projetos.map(projeto => (
              <option key={projeto.id} value={projeto.id}>
                {projeto.codigo_projeto} - {projeto.nome_projeto}
              </option>
            ))}
          </select>
        </div>

        <hr className="border-gray-200" />

        {/* Seleção de Indicador */}
        <div>
          <label htmlFor="indicador-select" className="block text-sm font-medium text-gray-700 mb-2">
            <i className="fas fa-chart-line mr-2"></i>Indicador de Análise:
          </label>
          <select 
            id="indicador-select"
            className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            value={indicadorSelecionado} 
            onChange={(e) => setIndicadorSelecionado(e.target.value)}
            disabled={!projetoSelecionado}
          >
            {Object.entries(indicadores).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        {/* ATUALIZAÇÃO: Seletor de GRUPO (substitui o seletor de tipo de veículo) */}
        {indicadorSelecionado === 'Composição Veicular' && (
          <div>
            <label htmlFor="grupo-select" className="block text-sm font-medium text-gray-700 mb-2">
              <i className="fas fa-layer-group mr-2"></i>Grupo de Análise:
            </label>
            <select 
              id="grupo-select"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              value={grupoSelecionado || ''} 
              onChange={(e) => setGrupoSelecionado(e.target.value)}
              disabled={grupos.length === 0}
            >
              <option value="">
                {grupos.length > 0 ? 'Selecione um grupo' : 'Nenhum grupo configurado'}
              </option>
              {grupos.map(grupo => (
                <option key={grupo.codigo} value={grupo.codigo}>
                  {grupo.nome}
                </option>
              ))}
            </select>
            {grupos.length === 0 && projetoSelecionado && (
              <button 
                onClick={() => navigate('/ccv-configuracao')}
                className="text-xs text-blue-600 hover:underline mt-2"
              >
                Clique aqui para configurar os grupos deste projeto.
              </button>
            )}
          </div>
        )}

        {/* ATUALIZAÇÃO: Legenda de Grupos de Veículos */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-800 mb-3">
            <i className="fas fa-palette mr-2"></i>Grupos Configurados
          </h4>
          <div className="space-y-2">
            {grupos.length > 0 ? (
              grupos.map(grupo => (
                <div key={grupo.codigo} className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: grupo.cor_grupo || '#ccc' }}
                  />
                  <span className="text-sm text-gray-700">{grupo.nome}</span>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">Nenhum grupo para exibir.</p>
            )}
          </div>
        </div>
      </div>
    </aside>
  );
}

SidebarCCV.propTypes = {
  empresas: PropTypes.array.isRequired,
  projetos: PropTypes.array.isRequired,
  dados: PropTypes.object,
  empresaSelecionada: PropTypes.number,
  setEmpresaSelecionada: PropTypes.func.isRequired,
  projetoSelecionado: PropTypes.number,
  setProjetoSelecionado: PropTypes.func.isRequired,
  indicadorSelecionado: PropTypes.string.isRequired,
  setIndicadorSelecionado: PropTypes.func.isRequired,
  // ATUALIZAÇÃO: Novas PropTypes para grupos
  grupos: PropTypes.array.isRequired,
  grupoSelecionado: PropTypes.string,
  setGrupoSelecionado: PropTypes.func.isRequired
};