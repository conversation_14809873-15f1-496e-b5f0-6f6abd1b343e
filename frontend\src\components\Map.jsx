// frontend/src/components/Map.jsx - ZOOM AUTOMÁTICO E NAVEGAÇÃO

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, LayersControl, useMap } from 'react-leaflet';
import { useMemo, useEffect, useRef } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import L from 'leaflet';
import { 
  configIndicadores, 
  generatePieIcon, 
  generatePinIcon, 
  getColorForValue,
  formatValue 
} from '../utils/mapHelpers';

// Componente para controlar o zoom automaticamente
function MapController({ peds, shouldFitBounds }) {
  const map = useMap();
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    if (!map || !peds || peds.length === 0) return;

    // Verifica se há parâmetros de zoom na URL (quando volta de detalhes)
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const zoom = searchParams.get('zoom');

    if (lat && lng && zoom) {
      // Restaura a posição salva
      console.log(`🗺️ Restaurando posição salva: lat=${lat}, lng=${lng}, zoom=${zoom}`);
      map.setView([parseFloat(lat), parseFloat(lng)], parseInt(zoom));
      return;
    }

    // Se não há posição salva e shouldFitBounds é true, ajusta para mostrar todos os pontos
    if (shouldFitBounds) {
      const validPeds = peds.filter(ped => 
        ped.lat && ped.long && 
        !isNaN(ped.lat) && !isNaN(ped.long)
      );

      if (validPeds.length > 0) {
        const bounds = L.latLngBounds(
          validPeds.map(ped => [ped.lat, ped.long])
        );
        
        // Ajusta o zoom com padding para melhor visualização
        map.fitBounds(bounds, { 
          padding: [20, 20], // 20px de padding em todas as direções
          maxZoom: 16 // Limita o zoom máximo inicial para não ficar muito próximo
        });
        
        console.log(`🗺️ Zoom ajustado para ${validPeds.length} pontos`);
      }
    }
  }, [map, peds, shouldFitBounds, searchParams]);

  return null;
}

// Componente para salvar o estado do mapa antes de navegar
function MapStateManager({ onMarkerClick }) {
  const map = useMap();
  const location = useLocation();
  
  const handleMarkerClick = (pedName) => {
    if (!map) return;
    
    // Salva o estado atual do mapa
    const center = map.getCenter();
    const zoom = map.getZoom();
    
    console.log(`💾 Salvando estado do mapa: lat=${center.lat}, lng=${center.lng}, zoom=${zoom}`);
    
    // Constrói a URL com os parâmetros do mapa
    const params = new URLSearchParams({
      lat: center.lat.toFixed(6),
      lng: center.lng.toFixed(6),
      zoom: zoom.toString()
    });
    
    // Navega para a página de detalhes com os parâmetros do mapa
    const detailUrl = `/ped/${encodeURIComponent(pedName)}?${params.toString()}`;
    onMarkerClick(detailUrl);
  };

  return { handleMarkerClick };
}

function Map({ data, indicator, cenario, horarios, intervaloDeTempo, modoIntervalo, tamanhoIcone, onMarkerClick }) {
  const location = useLocation();
  const mapRef = useRef(null);
  const peds = data?.pedsData ? Object.values(data.pedsData) : [];
  
  // Determina se deve ajustar o zoom (apenas na primeira carga ou quando não há parâmetros de posição)
  const [searchParams] = useSearchParams();
  const shouldFitBounds = !searchParams.has('lat') || !searchParams.has('lng');

  // Calcula os horários filtrados baseado no modo e intervalo selecionado
  const horariosFiltrados = useMemo(() => {
    if (!horarios || horarios.length === 0 || !intervaloDeTempo) return [];
    
    const [startIndex, endIndex] = intervaloDeTempo;
    
    if (modoIntervalo === 'fixo') {
      // Modo fixo: pega 4 intervalos de 15min (1 hora) a partir do índice selecionado
      const finalIndex = Math.min(startIndex + 3, horarios.length - 1);
      return horarios.slice(startIndex, finalIndex + 1);
    } else {
      // Modo livre: pega do startIndex até endIndex
      return horarios.slice(startIndex, (endIndex || startIndex) + 1);
    }
  }, [horarios, intervaloDeTempo, modoIntervalo]);

  // Calcula o volume máximo para dimensionar os ícones de pizza
  const maxVolumeTotal = useMemo(() => {
    if (indicator !== 'Passageiros') return 1;
    
    let maxVolume = 0;
    peds.forEach(ped => {
      const volumeNoPonto = horariosFiltrados.reduce((sum, h) => {
        return sum + (ped.dados_horarios[h]?.Total || 0);
      }, 0);
      if (volumeNoPonto > maxVolume) maxVolume = volumeNoPonto;
    });
    
    return maxVolume > 0 ? maxVolume : 1;
  }, [peds, horariosFiltrados, indicator]);

  // Calcula o valor máximo de travessias para dimensionar os ícones
  const maxTravessias = useMemo(() => {
    if (indicator !== 'Total Travessias') return 1;
    
    let maxValue = 0;
    peds.forEach(ped => {
      const totalTravessias = horariosFiltrados.reduce((sum, h) => {
        const dados = ped.dados_horarios[h];
        if (dados) {
          const travessia12 = dados['1 - 2'] || 0;
          const travessia21 = dados['2 - 1'] || 0;
          return sum + travessia12 + travessia21;
        }
        return sum;
      }, 0);
      if (totalTravessias > maxValue) maxValue = totalTravessias;
    });
    
    return maxValue > 0 ? maxValue : 1;
  }, [peds, horariosFiltrados, indicator]);

  // Calcula o valor máximo de ônibus para dimensionar os ícones
  const maxOnibus = useMemo(() => {
    if (indicator !== 'Onibus') return 1;
    
    let maxValue = 0;
    peds.forEach(ped => {
      const totalOnibus = horariosFiltrados.reduce((sum, h) => {
        const dados = ped.dados_horarios[h];
        if (dados) {
          return sum + (dados.Onibus || 0);
        }
        return sum;
      }, 0);
      if (totalOnibus > maxValue) maxValue = totalOnibus;
    });
    
    return maxValue > 0 ? maxValue : 1;
  }, [peds, horariosFiltrados, indicator]);

  // Coleta todos os valores únicos para indicadores categóricos
  const allCategoricalValues = useMemo(() => {
    const config = configIndicadores[indicator];
    if (!config?.isCategorical || !config.coresCategoricas) return null;
    
    const values = [];
    peds.forEach(ped => {
      let valor;
      
      // Para indicadores estáticos, busca direto no ped primeiro
      if (config.isStatic) {
        if (indicator === 'Abrigo') valor = ped.abrigo;
        else if (indicator === 'Placa') valor = ped.placa;
        else if (indicator === 'Baia') valor = ped.baia;
        else if (indicator === 'Mancha Urbana') valor = ped.mancha_urbana;
        else if (indicator === 'SH') valor = ped.sh;
        
        // Fallback para dados_horarios se não encontrar
        if (valor === null || valor === undefined) {
          const primeiroHorario = Object.keys(ped.dados_horarios || {})[0];
          if (primeiroHorario) {
            valor = ped.dados_horarios[primeiroHorario][indicator];
          }
        }
      }
      
      if (valor !== null && valor !== undefined) {
        values.push(valor);
      }
    });
    
    return [...new Set(values)].sort();
  }, [peds, indicator]);

  // Função para calcular o valor do indicador para um PED
  const calcularValorIndicador = (ped) => {
    const config = configIndicadores[indicator];
    if (!config) return undefined;

    // Para indicadores estáticos (não variam com o tempo)
    if (config.isStatic) {
      console.log(`DEBUG - Indicador estático: ${indicator}, PED: ${ped['Nome do PED']}`);
      
      // Buscar diretamente no objeto ped primeiro
      let valor;
      
      if (indicator === 'Abrigo') {
        valor = ped.abrigo;
        console.log(`DEBUG Abrigo - Valor direto do ped.abrigo: "${valor}"`);
      } else if (indicator === 'Placa') {
        valor = ped.placa;
        console.log(`DEBUG Placa - Valor direto do ped.placa: "${valor}"`);
      } else if (indicator === 'Baia') {
        valor = ped.baia;
        console.log(`DEBUG Baia - Valor direto do ped.baia: "${valor}"`);
      } else if (indicator === 'Mancha Urbana') {
        valor = ped.mancha_urbana;
        console.log(`DEBUG Mancha Urbana - Valor direto do ped.mancha_urbana: "${valor}"`);
      } else if (indicator === 'SH') {
        valor = ped.sh;
        console.log(`DEBUG SH - Valor direto do ped.sh: "${valor}"`);
      }
      
      // Se não encontrou diretamente no ped, tenta buscar nos dados_horarios como fallback
      if (valor === null || valor === undefined || valor === '') {
        console.log(`DEBUG - Valor não encontrado diretamente, tentando fallback nos dados_horarios`);
        const primeiroHorario = Object.keys(ped.dados_horarios)[0];
        if (primeiroHorario && ped.dados_horarios[primeiroHorario]) {
          const dadosHorarios = ped.dados_horarios[primeiroHorario];
          
          // Tenta diferentes variações do nome do indicador
          valor = dadosHorarios[indicator] || 
                  dadosHorarios[indicator.charAt(0).toUpperCase() + indicator.slice(1)] ||
                  dadosHorarios[indicator.toUpperCase()];
          
          console.log(`DEBUG - Fallback nos dados_horarios: "${valor}"`);
        }
      }
      
      console.log(`DEBUG - Valor final para ${indicator}: "${valor}" (tipo: ${typeof valor})`);
      return valor;
    }

    // Para indicadores dinâmicos
    if (config.worstIs === 'pie') {
      const totals = horariosFiltrados.reduce((acc, h) => {
        const dados = ped.dados_horarios[h];
        if (dados) {
          acc.embarques += dados.Embarque || 0;
          acc.desembarques += dados.Desembarque || 0;
          acc.volumeTotal += dados.Total || 0;
        }
        return acc;
      }, { embarques: 0, desembarques: 0, volumeTotal: 0 });
      
      return totals;
    }

    if (indicator === 'Total Travessias') {
      const totalTravessias = horariosFiltrados.reduce((sum, h) => {
        const dados = ped.dados_horarios[h];
        if (dados) {
          const travessia12 = dados['1 - 2'] || 0;
          const travessia21 = dados['2 - 1'] || 0;
          return sum + travessia12 + travessia21;
        }
        return sum;
      }, 0);
      
      return totalTravessias;
    }

    if (indicator === 'Onibus') {
      const totalOnibus = horariosFiltrados.reduce((sum, h) => {
        const dados = ped.dados_horarios[h];
        if (dados) {
          return sum + (dados.Onibus || 0);
        }
        return sum;
      }, 0);
      
      return totalOnibus;
    }

    // Para outros indicadores dinâmicos (GS, P(f), P(0))
    const coluna = `${indicator}_c${cenario}`;
    const valores = horariosFiltrados.map(h => ped.dados_horarios[h]?.[coluna]).filter(v => v != null);

    if (valores.length === 0) return undefined;

    if (config.worstIs === 'sum') return valores.reduce((a, b) => a + b, 0);
    if (config.worstIs === 'max') return Math.max(...valores);
    if (config.worstIs === 'min') return Math.min(...valores);
    
    return undefined;
  };

  // Função para gerar o ícone do marcador
  const getMarkerIcon = (ped) => {
    const config = configIndicadores[indicator];
    if (!config) return generatePinIcon('#808080', tamanhoIcone);

    const valor = calcularValorIndicador(ped);
    if (valor === undefined || valor === null) {
      return generatePinIcon('#808080', tamanhoIcone, '?');
    }

    // Para indicadores de passageiros (ícone de pizza)
    if (config.worstIs === 'pie') {
      const { embarques, desembarques, volumeTotal } = valor;
      const minSize = 0.8;
      const maxSize = 1.3;
      
      let sizeMultiplier;
      if (volumeTotal === 0) {
        sizeMultiplier = minSize;
      } else {
        const logVolume = Math.log(volumeTotal + 1);
        const logMax = Math.log(maxVolumeTotal + 1);
        const normalizedValue = logVolume / logMax;
        sizeMultiplier = minSize + (normalizedValue * (maxSize - minSize));
      }
      
      return generatePieIcon(embarques, desembarques, volumeTotal, maxVolumeTotal, sizeMultiplier);
    }

    // Para Total Travessias e Ônibus (ícone com tamanho proporcional)
    if (indicator === 'Total Travessias' || indicator === 'Onibus') {
      const cor = getColorForValue(indicator, valor);
      const valorFormatado = formatValue(indicator, valor);
      const minSize = 0.8;
      const maxSize = 2.0;
      
      let sizeMultiplier;
      if (valor === 0) {
        sizeMultiplier = minSize;
      } else {
        const maxRef = indicator === 'Total Travessias' ? maxTravessias : maxOnibus;
        const logValor = Math.log(valor + 1);
        const logMax = Math.log(maxRef + 1);
        const normalizedValue = logValor / logMax;
        sizeMultiplier = minSize + (normalizedValue * (maxSize - minSize));
      }
      
      return generatePinIcon(cor, sizeMultiplier, valorFormatado);
    }

    // Para indicadores categóricos
    if (config.isCategorical) {
      const cor = getColorForValue(indicator, valor, allCategoricalValues);
      
      if (config.titulo === 'Segmento Homogêneo (SH)') {
        return generatePinIcon(cor, tamanhoIcone, valor);
      }
      
      return generatePinIcon(cor, tamanhoIcone, null);
    }

    // Para indicadores numéricos com níveis
    const cor = getColorForValue(indicator, valor);
    return generatePinIcon(cor, tamanhoIcone, null);
  };

  // Função para gerar conteúdo do popup
  const getPopupContent = (ped) => {
    const config = configIndicadores[indicator];
    const valor = calcularValorIndicador(ped);
    
    let valorDisplay = 'N/A';
    if (valor !== undefined && valor !== null) {
      if (config?.worstIs === 'pie') {
        const { embarques, desembarques, volumeTotal } = valor;
        valorDisplay = `${embarques} embarques, ${desembarques} desembarques (Total: ${volumeTotal})`;
      } else {
        valorDisplay = formatValue(indicator, valor);
      }
    }

    return (
      <div className="min-w-64">
        <div className="font-semibold text-lg mb-2 text-gray-800">
          {ped['Nome do PED']}
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Indicador:</span>
            <span className="text-gray-900">{config?.titulo || indicator}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Valor:</span>
            <span className="text-gray-900 font-medium">{valorDisplay}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Localização:</span>
            <span className="text-gray-900">{ped.km || 'N/A'}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Sentido:</span>
            <span className="text-gray-900">{ped.sentido || 'N/A'}</span>
          </div>
        </div>
        
        <div className="mt-3 pt-2 border-t border-gray-200">
          <div className="text-xs text-gray-600 mb-1">
            <span className="font-medium">Período:</span> {
              horariosFiltrados.length > 0 
                ? `${horariosFiltrados[0]} - ${horariosFiltrados[horariosFiltrados.length - 1]}`
                : 'N/A'
            }
          </div>
          {config?.worstIs !== 'pie' && !config?.isStatic && (
            <div className="text-xs text-gray-600">
              <span className="font-medium">Cenário:</span> {cenario} posição{cenario > 1 ? 'es' : ''}
            </div>
          )}
        </div>
        
        <button 
          onClick={() => handleMarkerClickWithState(ped['Nome do PED'])}
          className="mt-3 w-full bg-blue-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
        >
          Ver Detalhes
        </button>
      </div>
    );
  };

  // Função para lidar com clique no marcador salvando o estado
  const handleMarkerClickWithState = (pedName) => {
    if (!mapRef.current) return;
    
    // Salva o estado atual do mapa
    const map = mapRef.current;
    const center = map.getCenter();
    const zoom = map.getZoom();
    
    console.log(`💾 Salvando estado do mapa: lat=${center.lat}, lng=${center.lng}, zoom=${zoom}`);
    
    // Chama o callback original com a URL modificada
    onMarkerClick(pedName, {
      lat: center.lat.toFixed(6),
      lng: center.lng.toFixed(6),
      zoom: zoom.toString()
    });
  };

  return (
    <MapContainer 
      center={[-22.9, -43.2]} 
      zoom={11} 
      style={{ height: '100%', width: '100%' }}
      zoomControl={false}
      ref={mapRef}
      maxZoom={20} // AUMENTA O ZOOM MÁXIMO (era 18 por padrão)
      minZoom={3}  // Define zoom mínimo
    >
      {/* Componente para controle automático de zoom */}
      <MapController peds={peds} shouldFitBounds={shouldFitBounds} />

      {/* Controle de zoom customizado */}
      <div className="leaflet-top leaflet-left" style={{ marginTop: '10px', marginLeft: '10px' }}>
        <div className="leaflet-control-zoom leaflet-bar leaflet-control">
          <a className="leaflet-control-zoom-in" href="#" title="Zoom in" role="button" aria-label="Zoom in">+</a>
          <a className="leaflet-control-zoom-out" href="#" title="Zoom out" role="button" aria-label="Zoom out">−</a>
        </div>
      </div>

      {/* Controle de camadas */}
      <LayersControl position="topright">
        <LayersControl.BaseLayer checked name="Mapa de Ruas">
          <TileLayer 
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" 
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            maxZoom={20} // Permite zoom até nível 20
          />
        </LayersControl.BaseLayer>
        
        <LayersControl.BaseLayer name="Imagem de Satélite">
          <TileLayer 
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}" 
            attribution='&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
            maxZoom={20} // Permite zoom até nível 20
          />
        </LayersControl.BaseLayer>
        
        <LayersControl.BaseLayer name="Terreno">
          <TileLayer 
            url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
            attribution='Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)'
            maxZoom={17} // Terreno tem limite menor
          />
        </LayersControl.BaseLayer>
      </LayersControl>

      {/* Marcadores dos PEDs */}
      {peds.map(ped => {
        // Verifica se o PED tem coordenadas válidas
        if (!ped.lat || !ped.long || isNaN(ped.lat) || isNaN(ped.long)) {
          return null;
        }

        return (
          <Marker
            key={ped['Nome do PED']}
            position={[ped.lat, ped.long]}
            icon={getMarkerIcon(ped)}
            eventHandlers={{ 
              click: () => handleMarkerClickWithState(ped['Nome do PED'])
            }}
          >
            <Popup
              maxWidth={300}
              className="custom-popup"
            >
              {getPopupContent(ped)}
            </Popup>
          </Marker>
        );
      })}

    </MapContainer>
  );
}

export default Map;