import { useState } from 'react';
import PropTypes from 'prop-types';

export default function LegendCCV({ indicador, tipoVeiculo, dados, isVisible = true }) {
  const [isMinimized, setIsMinimized] = useState(false);

  if (!isVisible || !indicador) {
    return null;
  }

  // Configurar legenda baseada no indicador
  const getLegendConfig = () => {
    switch (indicador) {
      case 'Volume Total':
        return {
          title: 'Volume Total de Veículos',
          description: 'Classificação por volume diário total',
          items: [
            { cor: '#2ecc71', texto: '<PERSON><PERSON> (< 100)', descricao: 'Volume muito baixo de tráfego' },
            { cor: '#f1c40f', texto: 'Baixo (100 - 500)', descricao: 'Volume baixo de tráfego' },
            { cor: '#f39c12', texto: 'Médio (500 - 1000)', descricao: 'Volume médio de tráfego' },
            { cor: '#e74c3c', texto: 'Alto (> 1000)', descricao: 'Volume alto de tráfego' }
          ]
        };
      
      case 'Composição Veicular':
        const tiposConfig = {
          'AUTO': { cor: '#3498db', nome: 'Automóveis' },
          'VAN': { cor: '#2ecc71', nome: 'Vans/Utilitários' },
          'CAM_LEVE': { cor: '#f39c12', nome: 'Caminhões Leves' },
          'CAM_PESADO': { cor: '#e74c3c', nome: 'Caminhões Pesados' },
          'ONIBUS': { cor: '#9b59b6', nome: 'Ônibus' },
          'MOTO': { cor: '#1abc9c', nome: 'Motocicletas' }
        };
        
        const tipoSelecionado = tiposConfig[tipoVeiculo] || { cor: '#95a5a6', nome: 'Outros' };
        
        return {
          title: `Composição Veicular - ${tipoSelecionado.nome}`,
          description: 'Volume do tipo de veículo selecionado',
          items: [
            { cor: tipoSelecionado.cor, texto: tipoSelecionado.nome, descricao: `Volume de ${tipoSelecionado.nome.toLowerCase()}` }
          ]
        };
      
      case 'Pico Horário':
        return {
          title: 'Horário de Pico',
          description: 'Identificação dos horários de maior movimento',
          items: [
            { cor: '#27ae60', texto: 'Pico Baixo (< 40)', descricao: 'Movimento baixo no pico' },
            { cor: '#f39c12', texto: 'Pico Médio (40 - 60)', descricao: 'Movimento médio no pico' },
            { cor: '#e74c3c', texto: 'Pico Alto (60 - 80)', descricao: 'Movimento alto no pico' },
            { cor: '#8e44ad', texto: 'Pico Muito Alto (> 80)', descricao: 'Movimento muito alto no pico' }
          ]
        };
      
      case 'Movimentos':
        return {
          title: 'Análise por Movimentos',
          description: 'Número de movimentos direcionais diferentes',
          items: [
            { cor: '#34495e', texto: 'Movimentos Direcionais', descricao: 'Quantidade de movimentos no ponto' }
          ]
        };
      
      case 'Densidade':
        return {
          title: 'Densidade de Tráfego',
          description: 'Volume médio por período de coleta',
          items: [
            { cor: '#27ae60', texto: 'Densidade Baixa (< 25)', descricao: 'Baixa densidade de veículos' },
            { cor: '#f1c40f', texto: 'Densidade Média (25 - 50)', descricao: 'Densidade média de veículos' },
            { cor: '#e67e22', texto: 'Densidade Alta (50 - 75)', descricao: 'Alta densidade de veículos' },
            { cor: '#c0392b', texto: 'Densidade Muito Alta (> 75)', descricao: 'Densidade muito alta de veículos' }
          ]
        };
      
      default:
        return {
          title: 'Legenda CCV',
          description: 'Análise de contagem veicular',
          items: [
            { cor: '#95a5a6', texto: 'Sem dados', descricao: 'Ponto sem dados disponíveis' }
          ]
        };
    }
  };

  const config = getLegendConfig();

  // Estatísticas dos dados se disponível
  const getEstatisticasDados = () => {
    if (!dados?.ccvData) return null;
    
    const pontos = Object.values(dados.ccvData);
    const stats = {
      totalPontos: pontos.length,
      totalVeiculos: 0,
      tiposVeiculos: new Set()
    };
    
    pontos.forEach(ponto => {
      Object.values(ponto.dados_horarios || {}).forEach(hora => {
        Object.entries(hora).forEach(([key, valor]) => {
          if (key.includes('_') && !key.startsWith('GS_') && !key.startsWith('P(')) {
            stats.totalVeiculos += Number(valor) || 0;
            const tipo = key.split('_')[0];
            stats.tiposVeiculos.add(tipo);
          }
        });
      });
    });
    
    return stats;
  };

  const estatisticas = getEstatisticasDados();

  return (
    <div 
      className="fixed bottom-4 right-4 ccv-legend" 
      style={{ 
        zIndex: 1000,
        position: 'fixed',
        bottom: '16px',
        right: '16px'
      }}
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden min-w-64 max-w-80" 
           style={{ boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)' }}>
        
        {/* Header da Legenda */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 text-sm leading-tight flex items-center">
                <i className="fas fa-car mr-2 text-blue-600"></i>
                {config.title}
              </h4>
              {config.description && (
                <p className="text-xs text-gray-600 mt-1 leading-tight">
                  {config.description}
                </p>
              )}
            </div>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="ml-2 p-1 hover:bg-gray-200 rounded transition-colors duration-200"
              title={isMinimized ? "Expandir legenda" : "Minimizar legenda"}
            >
              <svg 
                className={`w-4 h-4 text-gray-600 transform transition-transform duration-200 ${isMinimized ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Conteúdo da Legenda */}
        {!isMinimized && (
          <div className="px-4 py-3">
            
            {/* Items da legenda */}
            <div className="space-y-2 mb-4">
              {config.items.map((item, index) => (
                <LegendItem 
                  key={`${indicador}-${index}`} 
                  item={item} 
                />
              ))}
            </div>

            {/* Informações específicas do indicador */}
            {indicador === 'Composição Veicular' && (
              <div className="mb-4 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  <span className="font-medium">Tipo selecionado:</span> {tipoVeiculo}
                </p>
                <p className="text-xs text-gray-600 mt-1">
                  <span className="font-medium">Tamanho do círculo:</span> Proporcional ao volume
                </p>
              </div>
            )}

            {indicador === 'Pico Horário' && (
              <div className="mb-4 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  <span className="font-medium">Horário exibido:</span> Período de maior movimento
                </p>
                <p className="text-xs text-gray-600 mt-1">
                  <span className="font-medium">Cor:</span> Intensidade do pico
                </p>
              </div>
            )}

            {/* Estatísticas gerais */}
            {estatisticas && (
              <div className="pt-3 border-t border-gray-200">
                <h5 className="text-xs font-semibold text-gray-700 mb-2">
                  <i className="fas fa-chart-bar mr-1"></i>Estatísticas Gerais
                </h5>
                <div className="text-xs text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Pontos de contagem:</span>
                    <span className="font-medium">{estatisticas.totalPontos}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total de veículos:</span>
                    <span className="font-medium">{estatisticas.totalVeiculos.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tipos de veículos:</span>
                    <span className="font-medium">{estatisticas.tiposVeiculos.size}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Informações do sistema */}
            <div className="pt-3 border-t border-gray-200 mt-3">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex items-center">
                  <i className="fas fa-clock mr-1"></i>
                  <span>Períodos de 15 minutos</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-route mr-1"></i>
                  <span>Análise por movimento direcional</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-database mr-1"></i>
                  <span>Sistema CCV v2.0</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Componente para item individual da legenda
function LegendItem({ item }) {
  return (
    <div className="flex items-center gap-3 group">
      {/* Indicador de cor */}
      <div className="flex-shrink-0">
        <div 
          className="w-4 h-4 rounded-full border border-gray-300 shadow-sm"
          style={{ backgroundColor: item.cor }}
        />
      </div>

      {/* Texto e descrição */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-800 truncate">
            {item.texto}
          </span>
        </div>
        
        {item.descricao && item.descricao !== item.texto && (
          <p className="text-xs text-gray-600 mt-0.5 leading-tight">
            {item.descricao}
          </p>
        )}
      </div>
    </div>
  );
}

LegendCCV.propTypes = {
  indicador: PropTypes.string.isRequired,
  tipoVeiculo: PropTypes.string.isRequired,
  dados: PropTypes.object,
  isVisible: PropTypes.bool
};

LegendItem.propTypes = {
  item: PropTypes.shape({
    cor: PropTypes.string.isRequired,
    texto: PropTypes.string.isRequired,
    descricao: PropTypes.string
  }).isRequired
};