// frontend/src/App.jsx - ATUALIZADO COM ROTA ADMIN
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import DashboardCCV from './pages/DashboardCCV';
import DashboardVI<PERSON> from './pages/DashboardVIA';
import PedDetail from './pages/PedDetail';
import CcvDetail from './pages/CcvDetail';
import CcvImport from './pages/CcvImport';
import CcvConfig from './pages/CcvConfig'; // <-- ADICIONE ESTA LINHA
import DatabaseAdmin from './pages/DatabaseAdmin'; // <-- IMPORTAÇÃO ADICIONADA
import Header from './components/Header';
import ProductMenu from './components/ProductMenu';
import React, { useState } from 'react';

function App() {
  return (
    <BrowserRouter>
      <div className="flex flex-col h-screen bg-gray-100 font-sans">
        <Routes>
          {/* Rotas principais com layout */}
          <Route path="/" element={<MainLayout><Dashboard /></MainLayout>} />
          <Route path="/ccv" element={<MainLayout><DashboardCCV /></MainLayout>} />
          <Route path="/via" element={<MainLayout><DashboardVIA /></MainLayout>} />
          
          {/* Rotas de detalhe */}
          <Route path="/ped/:pedName" element={<PedDetail />} />
          <Route path="/ccv/:pontoName" element={<CcvDetail />} />

          {/* Rota única para a página de importação */}
          <Route path="/importar" element={<CcvImport />} />

          {/* ROTA DE ADMINISTRAÇÃO (sem o layout principal) */}
          <Route path="/admin" element={<DatabaseAdmin />} />

          <Route path="/ccv-configuracao" element={<CcvConfig />} /> {/* ADICIONE ESTA LINHA */}

        </Routes>
      </div>
    </BrowserRouter>
  );
}

// Componente auxiliar de layout
const MainLayout = ({ children }) => {
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);
  const toggleSidebar = () => setIsSidebarVisible(!isSidebarVisible);

  return (
    <>
      <Header onMenuToggle={toggleSidebar} />
      <ProductMenu />
      <div className="flex flex-1 overflow-hidden">
        {children && React.cloneElement(children, { isSidebarVisible })}
      </div>
    </>
  );
};

export default App;