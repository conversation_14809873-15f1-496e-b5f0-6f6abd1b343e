// frontend/src/components/MapCCV.jsx

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, LayersControl, useMap } from 'react-leaflet';
import { useMemo, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import L from 'leaflet';

// Componente auxiliar para controlar o mapa (zoom inicial apenas)
function MapController({ pontos, shouldAutoZoom }) {
  const map = useMap();

  useEffect(() => {
    // Só faz auto-zoom na primeira carga, não quando toggle aproximações
    if (shouldAutoZoom && pontos && pontos.length > 0) {
      const pontosComCoordenadas = pontos.filter(p => p.lat && p.long);
      if (pontosComCoordenadas.length > 0) {
        const bounds = L.latLngBounds(pontosComCoordenadas.map(p => [p.lat, p.long]));
        map.fitBounds(bounds, { padding: [50, 50], maxZoom: 16 });
      }
    }
  }, [pontos, map, shouldAutoZoom]);

  return null;
}

// Ícone para pontos principais
const createCCVIcon = (cor, tamanho = 1, valor = null) => {
  const size = 25 * tamanho;
  const html = `<div style="width: ${size}px; height: ${size}px; background-color: ${cor}; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: ${Math.max(8, size * 0.3)}px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8); line-height: 1; font-family: Arial, sans-serif;">${valor !== null ? String(valor) : ''}</div>`;
  return L.divIcon({ 
    html, 
    className: 'ccv-marker-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
};

// Ícone para aproximações (A, B, C, etc.)
const createAproximacaoIcon = (codigo) => {
  const size = 20;
  const html = `<div style="width: ${size}px; height: ${size}px; background-color: #2563eb; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 5px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 11px; font-family: Arial, sans-serif;">${codigo}</div>`;
  return L.divIcon({ 
    html, 
    className: 'ccv-aproximacao-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
};

const getCorPorIndicador = (indicador, valor, tipoVeiculo) => {
    switch (indicador) {
        case 'Volume Total':
          if (valor > 1000) return '#e74c3c'; if (valor > 500) return '#f39c12';
          if (valor > 100) return '#f1c40f'; return '#2ecc71';
        case 'Composição Veicular':
          const coresTipos = {'AUTO': '#3498db', 'VAN': '#2ecc71', 'CAM_LEVE': '#f39c12', 'CAM_PESADO': '#e74c3c', 'ONIBUS': '#9b59b6', 'MOTO': '#1abc9c'};
          return coresTipos[tipoVeiculo] || '#95a5a6';
        case 'Pico Horário':
          if (valor > 80) return '#8e44ad'; if (valor > 60) return '#e74c3c';
          if (valor > 40) return '#f39c12'; return '#27ae60';
        case 'Movimentos': return '#34495e';
        case 'Densidade':
          if (valor > 75) return '#c0392b'; if (valor > 50) return '#e67e22';
          if (valor > 25) return '#f1c40f'; return '#27ae60';
        default: return '#95a5a6';
    }
};

export default function MapCCV({ dados, indicador, tipoVeiculo, onMarkerClick, mostrarMovimentos }) {
  const pontos = dados?.ccvData ? Object.values(dados.ccvData) : [];
  const [shouldAutoZoom, setShouldAutoZoom] = useState(true);

  // Controla se deve fazer auto-zoom (apenas na primeira carga)
  useEffect(() => {
    if (pontos.length > 0 && shouldAutoZoom) {
      // Após o primeiro zoom, desabilita
      const timer = setTimeout(() => setShouldAutoZoom(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [pontos.length, shouldAutoZoom]);

  const pontosComValores = useMemo(() => {
    return pontos.map(ponto => {
        let valor = 0; let valorExibicao = '';
        const dadosHorarios = Object.values(ponto.dados_horarios || {});
        
        switch (indicador) {
            case 'Volume Total':
                valor = dadosHorarios.reduce((total, hora) => total + Object.entries(hora).filter(([key]) => key.includes('_')).reduce((sum, [, qty]) => sum + (Number(qty) || 0), 0), 0);
                valorExibicao = valor.toString(); 
                break;
            case 'Composição Veicular':
                valor = dadosHorarios.reduce((total, hora) => total + Object.entries(hora).filter(([key]) => key.startsWith(tipoVeiculo + '_')).reduce((sum, [, qty]) => sum + (Number(qty) || 0), 0), 0);
                valorExibicao = valor.toString(); 
                break;
            case 'Pico Horário':
                let maxVolume = 0; let horarioPico = '';
                dadosHorarios.forEach(hora => {
                    const volumeHora = Object.entries(hora).filter(([key]) => key.includes('_')).reduce((sum, [, qty]) => sum + (Number(qty) || 0), 0);
                    if (volumeHora > maxVolume) { maxVolume = volumeHora; horarioPico = hora.HORA || ''; }
                });
                valor = maxVolume; valorExibicao = horarioPico; 
                break;
            case 'Movimentos':
                const movimentos = new Set(dadosHorarios.flatMap(hora => Object.keys(hora).filter(key => key.includes('_')).map(key => key.split('_').slice(1).join('_'))));
                valor = movimentos.size; valorExibicao = valor.toString(); 
                break;
            case 'Densidade':
                if (dadosHorarios.length > 0) {
                    const volumeTotal = dadosHorarios.reduce((total, hora) => total + Object.entries(hora).filter(([key]) => key.includes('_')).reduce((sum, [, qty]) => sum + (Number(qty) || 0), 0), 0);
                    valor = Math.round(volumeTotal / dadosHorarios.length);
                    valorExibicao = valor.toString();
                } 
                break;
            default: 
                valor = 0; valorExibicao = '0';
        }
        
        return { 
            ...ponto, 
            valor, 
            valorExibicao, 
            cor: getCorPorIndicador(indicador, valor, tipoVeiculo) 
        };
    });
  }, [pontos, indicador, tipoVeiculo]);

  // Processar aproximações individuais (A, B, C, etc.) com coordenadas do GeoJSON
  const todasAproximacoes = useMemo(() => {
    if (!mostrarMovimentos) return [];
    
    let aproximacoesProcessadas = [];
    
    pontosComValores.forEach(ponto => {
      // Usa o campo 'aproximacoes' que agora vem da API com coordenadas corretas
      if (ponto.aproximacoes && ponto.aproximacoes.length > 0) {
        ponto.aproximacoes.forEach(aproximacao => {
          // Só adiciona se tem coordenadas válidas
          if (aproximacao.latitude && aproximacao.longitude) {
            aproximacoesProcessadas.push({
              ...aproximacao,
              pontoPrincipal: ponto['Nome do Ponto'] || ponto.Codigo,
              pontoId: ponto.Codigo
            });
          }
        });
      }
    });
    
    return aproximacoesProcessadas;
  }, [pontosComValores, mostrarMovimentos]);

  const getPopupContent = (item, tipo = 'ponto') => {
    if (tipo === 'aproximacao') {
      return (
        <div className="min-w-48">
          <div className="font-semibold text-lg mb-2 text-gray-800">
            Aproximação {item.codigo}
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Código:</span>
              <span className="text-gray-900 font-bold text-lg">{item.codigo}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Ponto Principal:</span>
              <span className="text-gray-900">{item.pontoPrincipal}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Coordenadas:</span>
              <span className="text-gray-900 text-xs">
                {item.latitude?.toFixed(6)}, {item.longitude?.toFixed(6)}
              </span>
            </div>
            {item.nome && (
              <div className="flex justify-between">
                <span className="font-medium text-gray-600">Nome:</span>
                <span className="text-gray-900">{item.nome}</span>
              </div>
            )}
          </div>
          <div className="mt-3 pt-2 border-t border-gray-200">
            <p className="text-xs text-gray-600">
              <i className="fas fa-map-marker-alt mr-1"></i>
              Coordenadas importadas do arquivo GeoJSON
            </p>
          </div>
        </div>
      );
    }

    // Popup para pontos principais
    return (
      <div className="min-w-64">
        <div className="font-semibold text-lg mb-2 text-gray-800">
          {item['Nome do Ponto'] || item.Codigo}
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Código:</span>
            <span className="text-gray-900">{item.Codigo}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Indicador:</span>
            <span className="text-gray-900 font-medium">{indicador}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Valor:</span>
            <span className="text-gray-900 font-medium">{item.valorExibicao}</span>
          </div>
          {item.endereco && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Endereço:</span>
              <span className="text-gray-900 text-xs">{item.endereco}</span>
            </div>
          )}
          {item.aproximacoes && item.aproximacoes.length > 0 && (
            <div>
              <span className="font-medium text-gray-600">Aproximações:</span>
              <div className="text-xs text-gray-700 mt-1">
                {item.aproximacoes.length} aproximação(ões): {item.aproximacoes.map(a => a.codigo).join(', ')}
              </div>
            </div>
          )}
        </div>
        <button 
          onClick={() => onMarkerClick(item.Codigo)} // CORREÇÃO 1: Usar item.Codigo em vez de item['Nome do Ponto']
          className="mt-3 w-full bg-blue-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
        >
          Ver Detalhes
        </button>
      </div>
    );
  };

  return (
    <MapContainer 
      center={[-19.9245, -43.9352]} 
      zoom={12} 
      style={{ height: '100%', width: '100%' }} 
      zoomControl={false} 
      maxZoom={20} 
      minZoom={3}
    >
      {/* Componente para o Zoom Automático */}
      <MapController pontos={pontosComValores} shouldAutoZoom={shouldAutoZoom} />

      {/* Controles de zoom personalizados */}
      <div className="leaflet-top leaflet-left" style={{ marginTop: '10px', marginLeft: '10px' }}>
        <div className="leaflet-control-zoom leaflet-bar leaflet-control">
          <a className="leaflet-control-zoom-in" href="#" title="Zoom in" role="button" aria-label="Zoom in">+</a>
          <a className="leaflet-control-zoom-out" href="#" title="Zoom out" role="button" aria-label="Zoom out">−</a>
        </div>
      </div>

      {/* Controle de camadas */}
      <LayersControl position="topright">
        <LayersControl.BaseLayer checked name="Mapa de Ruas">
          <TileLayer 
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" 
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            maxZoom={20} 
          />
        </LayersControl.BaseLayer>
        <LayersControl.BaseLayer name="Imagem de Satélite">
          <TileLayer 
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}" 
            attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
            maxZoom={20} 
          />
        </LayersControl.BaseLayer>
        <LayersControl.BaseLayer name="Terreno">
          <TileLayer 
            url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
            attribution='Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            maxZoom={17}
          />
        </LayersControl.BaseLayer>
      </LayersControl>

      {/* Marcadores dos pontos principais */}
      {pontosComValores.map(ponto => {
        if (!ponto.lat || !ponto.long) return null;
        
        const valorParaIcone = indicador === 'Pico Horário' ? ponto.valorExibicao : ponto.valor;
        
        return (
          <Marker 
            key={ponto.Codigo || ponto['Nome do Ponto']} 
            position={[ponto.lat, ponto.long]} 
            icon={createCCVIcon(ponto.cor, 1, valorParaIcone)}
            eventHandlers={{ 
              click: () => onMarkerClick(ponto.Codigo) // CORREÇÃO 2: Usar ponto.Codigo em vez de ponto['Nome do Ponto']
            }}
          >
            <Popup maxWidth={350} className="ccv-popup">
              {getPopupContent(ponto, 'ponto')}
            </Popup>
          </Marker>
        );
      })}

      {/* Marcadores das aproximações (A, B, C, etc.) quando ativados */}
      {mostrarMovimentos && todasAproximacoes.map((aproximacao, index) => (
        <Marker 
          key={`aproximacao-${aproximacao.pontoId}-${aproximacao.codigo}-${index}`} 
          position={[aproximacao.latitude, aproximacao.longitude]} 
          icon={createAproximacaoIcon(aproximacao.codigo)}
        >
          <Popup maxWidth={300} className="ccv-popup">
            {getPopupContent(aproximacao, 'aproximacao')}
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
}

MapCCV.propTypes = {
  dados: PropTypes.object,
  indicador: PropTypes.string.isRequired,
  tipoVeiculo: PropTypes.string.isRequired,
  onMarkerClick: PropTypes.func.isRequired,
  mostrarMovimentos: PropTypes.bool.isRequired
};