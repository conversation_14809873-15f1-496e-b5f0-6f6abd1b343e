// frontend/src/api/adminService.js

import axios from 'axios';

const API = axios.create({
  baseURL: 'http://localhost:5000',
  timeout: 30000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
});

// Interceptor de resposta para logging
API.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('❌ Admin API Error:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return Promise.reject(error.response?.data || error);
  }
);

/**
 * Busca estatísticas gerais do banco de dados
 */
export async function fetchDatabaseStats() {
  try {
    const response = await API.get('/api/admin/stats');
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar estatísticas:", error);
    throw error;
  }
}

/**
 * Busca todas as empresas com contagem de projetos
 */
export async function fetchAllEmpresas() {
  try {
    const response = await API.get('/api/admin/empresas');
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar empresas:", error);
    throw error;
  }
}

/**
 * Cria uma nova empresa
 */
export async function createEmpresa(empresaData) {
  try {
    const response = await API.post('/api/admin/empresas', empresaData);
    return response.data;
  } catch (error) {
    console.error("Erro ao criar empresa:", error);
    throw error;
  }
}

/**
 * Deleta uma empresa
 */
export async function deleteEmpresa(empresaId) {
  try {
    const response = await API.delete(`/api/admin/empresas/${empresaId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao deletar empresa:", error);
    throw error;
  }
}

/**
 * Busca todos os projetos (opcionalmente filtrados por empresa)
 */
export async function fetchAllProjetos(empresaId = null) {
  try {
    const params = empresaId ? { empresa_id: empresaId } : {};
    const response = await API.get('/api/admin/projetos', { params });
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar projetos:", error);
    throw error;
  }
}

/**
 * Cria um novo projeto
 */
export async function createProjeto(projetoData) {
  try {
    const response = await API.post('/api/admin/projetos', projetoData);
    return response.data;
  } catch (error) {
    console.error("Erro ao criar projeto:", error);
    throw error;
  }
}

/**
 * Deleta um projeto
 */
export async function deleteProjeto(projetoId) {
  try {
    const response = await API.delete(`/api/admin/projetos/${projetoId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao deletar projeto:", error);
    throw error;
  }
}

/**
 * Limpa todos os dados de um produto específico
 */
export async function cleanProductData(produto) {
  try {
    const response = await API.delete(`/api/admin/produtos/${produto}/clean`);
    return response.data;
  } catch (error) {
    console.error(`Erro ao limpar dados do produto ${produto}:`, error);
    throw error;
  }
}

/**
 * Reseta completamente o banco de dados
 * CUIDADO: Esta operação é irreversível!
 */
export async function resetDatabase() {
  try {
    const response = await API.delete('/api/admin/database/reset?confirm=RESET_ALL_DATA');
    return response.data;
  } catch (error) {
    console.error("Erro ao resetar banco de dados:", error);
    throw error;
  }
}