// frontend/src/components/MapVIA.jsx - CORREÇÃO COMPLETA DA PROJEÇÃO

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Polyline, LayersControl, useMap } from 'react-leaflet';
import { useMemo, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import L from 'leaflet';

// Componente auxiliar para controlar o mapa (zoom inicial apenas)
function MapController({ trechos, shouldAutoZoom }) {
  const map = useMap();

  useEffect(() => {
    if (shouldAutoZoom && trechos && trechos.length > 0) {
      // <PERSON>ta todas as coordenadas dos trechos para calcular bounds
      const allCoords = [];
      trechos.forEach(trecho => {
        if (trecho.coordenadas && trecho.coordenadas.length > 0) {
          // Verifica se é MultiLineString ou LineString simples
          if (Array.isArray(trecho.coordenadas[0]) && Array.isArray(trecho.coordenadas[0][0])) {
            // MultiLineString
            trecho.coordenadas.forEach(linha => {
              linha.forEach(coord => {
                if (Array.isArray(coord) && coord.length >= 2) {
                  allCoords.push([coord[0], coord[1]]); // [lat, lng] para Leaflet
                }
              });
            });
          } else {
            // LineString simples
            trecho.coordenadas.forEach(coord => {
              if (Array.isArray(coord) && coord.length >= 2) {
                allCoords.push([coord[0], coord[1]]); // [lat, lng] para Leaflet
              }
            });
          }
        }
      });

      if (allCoords.length > 0) {
        const bounds = L.latLngBounds(allCoords);
        map.fitBounds(bounds, { padding: [50, 50], maxZoom: 16 });
      }
    }
  }, [trechos, map, shouldAutoZoom]);

  return null;
}

// Ícone para nós da rede
const createNoIcon = (tipo = 'node', tamanho = 1) => {
  const size = 12 * tamanho;
  let cor = '#2563eb'; // Azul padrão
  
  // Cores baseadas no tipo de nó
  switch (tipo.toLowerCase()) {
    case 'intersection':
      cor = '#dc2626'; // Vermelho para interseções
      break;
    case 'entry':
      cor = '#16a34a'; // Verde para entradas
      break;
    case 'exit':
      cor = '#ea580c'; // Laranja para saídas
      break;
    case 'connector':
      cor = '#7c3aed'; // Roxo para conectores
      break;
    default:
      cor = '#2563eb'; // Azul para nós genéricos
  }

  const html = `<div style="
    width: ${size}px; 
    height: ${size}px; 
    background-color: ${cor}; 
    border-radius: 50%; 
    border: 2px solid white; 
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  "></div>`;

  return L.divIcon({ 
    html, 
    className: 'via-no-icon', 
    iconSize: [size, size], 
    iconAnchor: [size / 2, size / 2] 
  });
};

// Função para obter cor do trecho baseada no indicador
const getCorTrecho = (trecho, indicador, dadosRede) => {
  if (!indicador || !dadosRede?.projeto_via?.indicadores_config) {
    return '#6b7280'; // Cinza padrão
  }

  const configIndicador = dadosRede.projeto_via.indicadores_config[indicador];
  if (!configIndicador) {
    return '#6b7280';
  }

  // Busca o valor do indicador nos atributos originais do trecho
  const valor = trecho.atributos_originais?.[indicador];
  if (valor === null || valor === undefined) {
    return '#9ca3af'; // Cinza claro para valores ausentes
  }

  // Determina a cor baseada no tipo de indicador
  if (configIndicador.tipo === 'numerico') {
    return getCorNumerica(valor, indicador, dadosRede);
  } else if (configIndicador.tipo === 'categoria') {
    return getCorCategorica(valor, indicador, dadosRede);
  }

  return '#6b7280';
};

// Função para cores numéricas (gradiente)
const getCorNumerica = (valor, indicador, dadosRede) => {
  // Coleta todos os valores do indicador para calcular min/max
  const trechos = Object.values(dadosRede.trechos || {});
  const valores = trechos
    .map(t => t.atributos_originais?.[indicador])
    .filter(v => v !== null && v !== undefined && !isNaN(v))
    .map(v => Number(v));

  if (valores.length === 0) return '#6b7280';

  const minVal = Math.min(...valores);
  const maxVal = Math.max(...valores);
  
  if (minVal === maxVal) return '#3b82f6'; // Azul se todos iguais

  // Normaliza o valor para 0-1
  const normalizado = (Number(valor) - minVal) / (maxVal - minVal);
  
  // Gradiente de cores: azul (baixo) -> amarelo (médio) -> vermelho (alto)
  if (normalizado <= 0.5) {
    // Azul para amarelo
    const t = normalizado * 2;
    return interpolateColor('#3b82f6', '#eab308', t);
  } else {
    // Amarelo para vermelho
    const t = (normalizado - 0.5) * 2;
    return interpolateColor('#eab308', '#dc2626', t);
  }
};

// Função para cores categóricas
const getCorCategorica = (valor, indicador, dadosRede) => {
  const cores = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ];

  // Coleta todas as categorias únicas
  const trechos = Object.values(dadosRede.trechos || {});
  const categorias = [...new Set(
    trechos
      .map(t => t.atributos_originais?.[indicador])
      .filter(v => v !== null && v !== undefined)
  )].sort();

  const index = categorias.indexOf(valor);
  return cores[index % cores.length];
};

// Função auxiliar para interpolação de cores
const interpolateColor = (color1, color2, factor) => {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);
  
  const r = Math.round(r1 + (r2 - r1) * factor);
  const g = Math.round(g1 + (g2 - g1) * factor);
  const b = Math.round(b1 + (b2 - b1) * factor);
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

// FUNÇÃO CORRIGIDA PARA CONVERTER COORDENADAS DO GEOJSON PARA FORMATO LEAFLET
const processarGeometria = (geometria) => {
  if (!geometria || !geometria.coordinates) return [];

  console.log('Processando geometria:', {
    type: geometria.type,
    coordinates: geometria.coordinates.slice(0, 3), // Apenas primeiras 3 coordenadas para debug
    totalCoords: geometria.coordinates.length
  });

  if (geometria.type === 'LineString') {
    // Para LineString: coordinates é um array de [longitude, latitude]
    // Precisamos converter para [latitude, longitude] para o Leaflet
    const converted = geometria.coordinates.map(coord => {
      if (Array.isArray(coord) && coord.length >= 2) {
        return [coord[1], coord[0]]; // [lat, lng] = [coord[1], coord[0]]
      }
      console.warn('Coordenada inválida encontrada:', coord);
      return null;
    }).filter(coord => coord !== null);

    console.log('LineString convertida:', {
      original: geometria.coordinates.slice(0, 3),
      converted: converted.slice(0, 3),
      total: converted.length
    });

    return converted;
  } 
  
  else if (geometria.type === 'MultiLineString') {
    // Para MultiLineString: coordinates é um array de arrays de [longitude, latitude]
    const converted = geometria.coordinates.map(linha => {
      if (Array.isArray(linha)) {
        return linha.map(coord => {
          if (Array.isArray(coord) && coord.length >= 2) {
            return [coord[1], coord[0]]; // [lat, lng] = [coord[1], coord[0]]
          }
          console.warn('Coordenada inválida encontrada:', coord);
          return null;
        }).filter(coord => coord !== null);
      }
      console.warn('Linha inválida encontrada:', linha);
      return [];
    }).filter(linha => linha.length > 0);

    console.log('MultiLineString convertida:', {
      original: geometria.coordinates[0]?.slice(0, 3),
      converted: converted[0]?.slice(0, 3),
      totalLines: converted.length
    });

    return converted;
  }

  console.warn('Tipo de geometria não suportado:', geometria.type);
  return [];
};

export default function MapVIA({ 
  dados, 
  indicador, 
  cenario, 
  mostrarNos, 
  mostrarTrechos, 
  opacidadeTrechos, 
  onTrechoClick 
}) {
  const [shouldAutoZoom, setShouldAutoZoom] = useState(true);

  const trechos = dados?.trechos ? Object.values(dados.trechos) : [];
  const nos = dados?.nos ? Object.values(dados.nos) : [];

  // Controla se deve fazer auto-zoom (apenas na primeira carga)
  useEffect(() => {
    if (trechos.length > 0 && shouldAutoZoom) {
      const timer = setTimeout(() => setShouldAutoZoom(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [trechos.length, shouldAutoZoom]);

  // Processa trechos com suas cores baseadas no indicador
  const trechosProcessados = useMemo(() => {
    console.log('Processando trechos:', trechos.length);
    
    return trechos.map((trecho, index) => {
      const coordenadas = processarGeometria(trecho.geometria);
      const cor = getCorTrecho(trecho, indicador, dados);
      
      console.log(`Trecho ${index}:`, {
        id: trecho.id,
        aimsun_id: trecho.aimsun_id,
        hasGeometry: !!trecho.geometria,
        geometryType: trecho.geometria?.type,
        coordenadasLength: coordenadas.length,
        cor: cor
      });
      
      return {
        ...trecho,
        coordenadas,
        cor,
        valor: trecho.atributos_originais?.[indicador]
      };
    });
  }, [trechos, indicador, dados]);

  // Debug: Log dos trechos processados
  useEffect(() => {
    console.log('Trechos processados summary:', {
      total: trechosProcessados.length,
      comCoordenadas: trechosProcessados.filter(t => t.coordenadas.length > 0).length,
      semCoordenadas: trechosProcessados.filter(t => t.coordenadas.length === 0).length
    });
  }, [trechosProcessados]);

  // Nós filtrados
  const nosFiltrados = useMemo(() => {
    return nos.filter(no => no.latitude && no.longitude);
  }, [nos]);

  const getPopupContentTrecho = (trecho) => {
    const configIndicador = dados?.projeto_via?.indicadores_config?.[indicador];
    
    return (
      <div className="min-w-64">
        <div className="font-semibold text-lg mb-2 text-gray-800">
          {trecho.nome || trecho.aimsun_id || `Trecho ${trecho.id}`}
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">ID Aimsun:</span>
            <span className="text-gray-900">{trecho.aimsun_id || 'N/A'}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Tipo:</span>
            <span className="text-gray-900">{trecho.tipo || 'N/A'}</span>
          </div>
          
          {trecho.comprimento && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Comprimento:</span>
              <span className="text-gray-900">{Math.round(trecho.comprimento)} m</span>
            </div>
          )}
          
          {trecho.numero_faixas && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Faixas:</span>
              <span className="text-gray-900">{trecho.numero_faixas}</span>
            </div>
          )}
          
          {trecho.velocidade_limite && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Velocidade:</span>
              <span className="text-gray-900">{trecho.velocidade_limite} km/h</span>
            </div>
          )}
          
          {indicador && configIndicador && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex justify-between">
                <span className="font-medium text-gray-600">
                  {configIndicador.nome_personalizado || indicador}:
                </span>
                <span className="text-gray-900 font-medium">
                  {trecho.valor !== null && trecho.valor !== undefined 
                    ? `${trecho.valor}${configIndicador.unidade ? ` ${configIndicador.unidade}` : ''}`
                    : 'N/A'}
                </span>
              </div>
            </div>
          )}

          {/* Debug info - remover em produção */}
          <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-500">
            <div>Coordenadas: {trecho.coordenadas.length} pontos</div>
            <div>Tipo geometria: {trecho.geometria?.type || 'N/A'}</div>
          </div>
        </div>
        
        <button 
          onClick={() => onTrechoClick(trecho.id)} 
          className="mt-3 w-full bg-indigo-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-indigo-700 transition-colors"
        >
          Ver Detalhes
        </button>
      </div>
    );
  };

  const getPopupContentNo = (no) => {
    return (
      <div className="min-w-48">
        <div className="font-semibold text-lg mb-2 text-gray-800">
          {no.nome || no.aimsun_id || `Nó ${no.id}`}
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">ID Aimsun:</span>
            <span className="text-gray-900">{no.aimsun_id || 'N/A'}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Tipo:</span>
            <span className="text-gray-900">{no.tipo || 'N/A'}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Coordenadas:</span>
            <span className="text-gray-900 text-xs">
              {no.latitude?.toFixed(6)}, {no.longitude?.toFixed(6)}
            </span>
          </div>
          
          {no.controlado && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Controle:</span>
              <span className="text-gray-900">{no.tipo_controle || 'Sim'}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <MapContainer 
      center={[-19.9245, -43.9352]} 
      zoom={12} 
      style={{ height: '100%', width: '100%' }} 
      zoomControl={false} 
      maxZoom={20} 
      minZoom={3}
    >
      {/* Componente para Zoom Automático */}
      <MapController trechos={trechosProcessados} shouldAutoZoom={shouldAutoZoom} />

      {/* Controles de zoom personalizados */}
      <div className="leaflet-top leaflet-left" style={{ marginTop: '10px', marginLeft: '10px' }}>
        <div className="leaflet-control-zoom leaflet-bar leaflet-control">
          <a className="leaflet-control-zoom-in" href="#" title="Zoom in" role="button" aria-label="Zoom in">+</a>
          <a className="leaflet-control-zoom-out" href="#" title="Zoom out" role="button" aria-label="Zoom out">−</a>
        </div>
      </div>

      {/* Controle de camadas */}
      <LayersControl position="topright">
        <LayersControl.BaseLayer checked name="Mapa de Ruas">
          <TileLayer 
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" 
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            maxZoom={20} 
          />
        </LayersControl.BaseLayer>
        
        <LayersControl.BaseLayer name="Imagem de Satélite">
          <TileLayer 
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}" 
            attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
            maxZoom={20} 
          />
        </LayersControl.BaseLayer>
        
        <LayersControl.BaseLayer name="Terreno">
          <TileLayer 
            url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
            attribution='Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            maxZoom={17}
          />
        </LayersControl.BaseLayer>
      </LayersControl>

      {/* Renderização dos trechos */}
      {mostrarTrechos && trechosProcessados.map((trecho, index) => {
        if (!trecho.coordenadas || trecho.coordenadas.length === 0) {
          console.warn(`Trecho ${trecho.id} sem coordenadas válidas`);
          return null;
        }

        // Debug para o primeiro trecho
        if (index === 0) {
          console.log('Renderizando primeiro trecho:', {
            id: trecho.id,
            coordenadas: trecho.coordenadas.slice(0, 3),
            isMultiLineString: Array.isArray(trecho.coordenadas[0]) && Array.isArray(trecho.coordenadas[0][0])
          });
        }

        // Se é MultiLineString, renderiza múltiplas polylines
        if (Array.isArray(trecho.coordenadas[0]) && Array.isArray(trecho.coordenadas[0][0])) {
          return trecho.coordenadas.map((linha, lineIndex) => (
            <Polyline
              key={`${trecho.id}-${lineIndex}`}
              positions={linha}
              color={trecho.cor}
              weight={4}
              opacity={opacidadeTrechos}
              eventHandlers={{
                click: () => onTrechoClick(trecho.id)
              }}
            >
              <Popup maxWidth={350} className="via-popup">
                {getPopupContentTrecho(trecho)}
              </Popup>
            </Polyline>
          ));
        } else {
          // LineString simples
          return (
            <Polyline
              key={trecho.id}
              positions={trecho.coordenadas}
              color={trecho.cor}
              weight={4}
              opacity={opacidadeTrechos}
              eventHandlers={{
                click: () => onTrechoClick(trecho.id)
              }}
            >
              <Popup maxWidth={350} className="via-popup">
                {getPopupContentTrecho(trecho)}
              </Popup>
            </Polyline>
          );
        }
      })}

      {/* Renderização dos nós */}
      {mostrarNos && nosFiltrados.map(no => (
        <Marker
          key={no.id}
          position={[no.latitude, no.longitude]}
          icon={createNoIcon(no.tipo, 1)}
        >
          <Popup maxWidth={300} className="via-popup">
            {getPopupContentNo(no)}
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
}

MapVIA.propTypes = {
  dados: PropTypes.object,
  indicador: PropTypes.string.isRequired,
  cenario: PropTypes.string.isRequired,
  mostrarNos: PropTypes.bool.isRequired,
  mostrarTrechos: PropTypes.bool.isRequired,
  opacidadeTrechos: PropTypes.number.isRequired,
  onTrechoClick: PropTypes.func.isRequired
};