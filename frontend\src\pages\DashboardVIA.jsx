// frontend/src/pages/DashboardVIA.jsx

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { fetchEmpresas, fetchProjetos } from '../api/ccvImportService';
import { fetchProjetosVIA, fetchDadosVIA } from '../api/viaService';
import SidebarVIA from '../components/SidebarVIA';
import MapVIA from '../components/MapVIA';
import LegendVIA from '../components/LegendVIA';

export default function DashboardVIA({ isSidebarVisible }) {
  const [loading, setLoading] = useState(true);
  const [loadingStep, setLoadingStep] = useState('Iniciando...');
  const [dados, setDados] = useState(null);
  const [empresas, setEmpresas] = useState([]);
  const [projetos, setProjetos] = useState([]);
  const [projetosVIA, setProjetosVIA] = useState([]);
  const [error, setError] = useState(null);
  
  // Estados de filtros
  const [empresaSelecionada, setEmpresaSelecionada] = useState(null);
  const [projetoSelecionado, setProjetoSelecionado] = useState(null);
  const [projetoVIASelecionado, setProjetoVIASelecionado] = useState(null);
  const [indicadorSelecionado, setIndicadorSelecionado] = useState('');
  const [cenarioSelecionado, setCenarioSelecionado] = useState('');
  
  // Estados de visualização
  const [mostrarNos, setMostrarNos] = useState(true);
  const [mostrarTrechos, setMostrarTrechos] = useState(true);
  const [opacidadeTrechos, setOpacidadeTrechos] = useState(0.8);
  
  const navigate = useNavigate();

  useEffect(() => {
    carregarDadosIniciais();
  }, []);

  useEffect(() => {
    if (empresaSelecionada) {
      carregarProjetos();
    } else {
      setProjetos([]);
      setProjetosVIA([]);
      setProjetoSelecionado(null);
      setProjetoVIASelecionado(null);
    }
  }, [empresaSelecionada]);

  useEffect(() => {
    if (empresaSelecionada) {
      carregarProjetosVIA();
    }
  }, [empresaSelecionada]);

  useEffect(() => {
    if (projetoVIASelecionado) {
      carregarDadosVIA();
    } else {
      setDados(null);
    }
  }, [projetoVIASelecionado]);

  const carregarDadosIniciais = async () => {
    try {
      setLoading(true);
      setLoadingStep('Carregando empresas...');
      const empresasData = await fetchEmpresas();
      setEmpresas(empresasData.empresas);
      if (empresasData.empresas.length > 0) {
        setEmpresaSelecionada(empresasData.empresas[0].id);
      } else {
        setLoading(false);
      }
    } catch (err) {
      setError(`Erro ao carregar empresas: ${err.message}`);
      setLoading(false);
    }
  };

  const carregarProjetos = async () => {
    try {
      setLoadingStep('Carregando projetos...');
      const projetosData = await fetchProjetos(empresaSelecionada);
      setProjetos(projetosData.projetos);
    } catch (err) {
      setError(`Erro ao carregar projetos: ${err.message}`);
    }
  };

  const carregarProjetosVIA = async () => {
    try {
      setLoadingStep('Carregando projetos VIA...');
      const projetosVIAData = await fetchProjetosVIA(empresaSelecionada);
      setProjetosVIA(projetosVIAData);
      
      if (projetosVIAData.length > 0) {
        setProjetoVIASelecionado(projetosVIAData[0].id);
      } else {
        setLoading(false);
      }
    } catch (err) {
      setError(`Erro ao carregar projetos VIA: ${err.message}`);
      setLoading(false);
    }
  };

  const carregarDadosVIA = async () => {
    if (!projetoVIASelecionado) return;
    
    try {
      setLoading(true);
      setLoadingStep('Carregando dados da rede viária...');
      const data = await fetchDadosVIA(projetoVIASelecionado);
      setDados(data);
      
      // Define indicador padrão se disponível
      const indicadoresDisponiveis = Object.keys(data.projeto_via.indicadores_config || {});
      if (indicadoresDisponiveis.length > 0 && !indicadorSelecionado) {
        setIndicadorSelecionado(indicadoresDisponiveis[0]);
      }
      
      setError(null);
    } catch (err) {
      setError(`Erro ao carregar dados da rede: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTrechoClick = (trechoId) => {
    console.log('Clique no trecho:', trechoId);
    // Implementar navegação para detalhes do trecho
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Carregando dados VIA...</h2>
          <p className="text-gray-600 mb-4">{loadingStep}</p>
          <div className="text-sm text-gray-500">
            Sistema Viário - Análise de Redes do Aimsun
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-2xl">
          <i className="fas fa-exclamation-triangle text-red-500 text-5xl mb-4"></i>
          <h2 className="text-2xl font-bold text-red-800 mb-4">Erro ao Carregar Dados VIA</h2>
          <p className="text-red-600 mb-4 text-lg">{error}</p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button 
              onClick={carregarDadosIniciais}
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-semibold"
            >
              <i className="fas fa-redo mr-2"></i>
              Tentar Novamente
            </button>
            <button 
              onClick={() => navigate('/importar')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              <i className="fas fa-upload mr-2"></i>
              Importar Rede
            </button>
          </div>
          
          <div className="mt-6 text-sm text-gray-600">
            <p>Verifique se a rede do Aimsun foi importada corretamente ou se o projeto possui dados VIA configurados.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {isSidebarVisible && (
        <SidebarVIA
          empresas={empresas}
          projetos={projetos}
          projetosVIA={projetosVIA}
          dados={dados}
          empresaSelecionada={empresaSelecionada}
          setEmpresaSelecionada={setEmpresaSelecionada}
          projetoSelecionado={projetoSelecionado}
          setProjetoSelecionado={setProjetoSelecionado}
          projetoVIASelecionado={projetoVIASelecionado}
          setProjetoVIASelecionado={setProjetoVIASelecionado}
          indicadorSelecionado={indicadorSelecionado}
          setIndicadorSelecionado={setIndicadorSelecionado}
          cenarioSelecionado={cenarioSelecionado}
          setCenarioSelecionado={setCenarioSelecionado}
          mostrarNos={mostrarNos}
          setMostrarNos={setMostrarNos}
          mostrarTrechos={mostrarTrechos}
          setMostrarTrechos={setMostrarTrechos}
          opacidadeTrechos={opacidadeTrechos}
          setOpacidadeTrechos={setOpacidadeTrechos}
        />
      )}
      
      <main className="flex-1 relative">
        <MapVIA
          dados={dados}
          indicador={indicadorSelecionado}
          cenario={cenarioSelecionado}
          mostrarNos={mostrarNos}
          mostrarTrechos={mostrarTrechos}
          opacidadeTrechos={opacidadeTrechos}
          onTrechoClick={handleTrechoClick}
        />
        
        <LegendVIA 
          indicador={indicadorSelecionado}
          dados={dados}
        />
        
        {/* Controles de visualização */}
        <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]" style={{ marginRight: '60px' }}>
          <h4 className="text-sm font-semibold text-gray-800 mb-3">Controles de Visualização</h4>
          
          <div className="space-y-3">
            {/* Toggle para nós */}
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-700">Mostrar Nós</label>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mostrarNos}
                  onChange={(e) => setMostrarNos(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
              </label>
            </div>

            {/* Toggle para trechos */}
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-700">Mostrar Trechos</label>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mostrarTrechos}
                  onChange={(e) => setMostrarTrechos(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
              </label>
            </div>

            {/* Slider de opacidade */}
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Opacidade dos Trechos: {Math.round(opacidadeTrechos * 100)}%
              </label>
              <input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={opacidadeTrechos}
                onChange={(e) => setOpacidadeTrechos(parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          </div>
        </div>

        {/* Indicador de loading para operações do mapa */}
        {loading && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-4 z-[1001]">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
              <span className="text-sm text-gray-700">{loadingStep}</span>
            </div>
          </div>
        )}
      </main>
    </>
  );
}