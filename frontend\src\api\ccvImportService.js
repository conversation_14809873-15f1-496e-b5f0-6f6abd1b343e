// frontend/src/api/ccvImportService.js - OTIMIZAÇÕES PARA ARQUIVOS GRANDES
import axios from 'axios';

// 1. Definição centralizada da URL da API usando o método do Vite
import { API_BASE_URL } from './config.js';

// 2. Instância do Axios configurada para usar a URL base
const API = axios.create({ 
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
  responseType: 'json'
});

// Configurações de timeout otimizadas
const TIMEOUT_CONFIG = {
  analysis: 120000,      // 2 minutos para análise
  import: 600000,        // 10 minutos para importação
  default: 30000         // 30 segundos padrão
};

// Configuração de tamanhos máximos
const FILE_LIMITS = {
  maxSize: 50 * 1024 * 1024,     // 50MB
  warningSize: 10 * 1024 * 1024,  // 10MB
  maxRows: 100000                 // 100k linhas
};

/**
 * Analisa arquivo Excel com timeout estendido e validações
 */
export const analyzeExcelFile = async (formData, onProgress = null) => {
  try {
    // Validar tamanho do arquivo antes de enviar
    const file = formData.get('contagem_file');
    if (file && file.size > FILE_LIMITS.maxSize) {
      throw new Error(`Arquivo muito grande (${(file.size / 1024 / 1024).toFixed(1)}MB). Máximo permitido: ${FILE_LIMITS.maxSize / 1024 / 1024}MB`);
    }

    if (file && file.size > FILE_LIMITS.warningSize) {
      console.warn(`Arquivo grande detectado (${(file.size / 1024 / 1024).toFixed(1)}MB). Processamento pode demorar.`);
    }

    // Criar AbortController para cancelamento
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_CONFIG.analysis);

    try {
      if (onProgress) onProgress({ step: 'upload', progress: 0, message: 'Enviando arquivo...' });

      const response = await fetch(`${API_BASE_URL}/ccv/analisar-tipos-excel`, {
        method: 'POST',
        body: formData,
        signal: controller.signal,
        // Não definir Content-Type para FormData (deixar o browser definir com boundary)
      });

      clearTimeout(timeoutId);

      if (onProgress) onProgress({ step: 'processing', progress: 50, message: 'Analisando tipos de veículos...' });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        if (response.status === 413) {
          throw new Error('Arquivo muito grande para processamento. Divida em arquivos menores.');
        }
        
        if (response.status === 408) {
          throw new Error('Tempo limite excedido durante análise. Tente com um arquivo menor.');
        }
        
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (onProgress) {
        onProgress({ 
          step: 'completed', 
          progress: 100, 
          message: `Análise concluída: ${data.estatisticas?.total_registros?.toLocaleString()} registros` 
        });
      }

      // Alertar sobre estimativa de tempo se for arquivo grande
      if (data.estatisticas?.estimativa_importacao > 2) {
        console.warn(`Importação estimada: ${data.estatisticas.estimativa_importacao} minutos`);
      }

      return data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Análise cancelada por timeout. Tente com um arquivo menor.');
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Erro na análise do arquivo:', error);
    
    if (error.message.includes('Failed to fetch')) {
      throw new Error('Erro de conexão. Verifique sua internet e tente novamente.');
    }
    
    throw error;
  }
};

/**
 * Confirma classificação e executa importação com monitoramento
 */
export const confirmClassificationAndImport = async (importData, onProgress = null) => {
  try {
    // Validar dados antes de enviar
    if (!importData.arquivo_excel_base64) {
      throw new Error('Dados do arquivo não encontrados');
    }

    // Estimar tamanho baseado no base64
    const estimatedSize = (importData.arquivo_excel_base64.length * 3) / 4;
    const estimatedRows = estimatedSize / 100; // Estimativa grosseira

    if (estimatedRows > FILE_LIMITS.maxRows) {
      throw new Error(`Arquivo possui aproximadamente ${Math.round(estimatedRows / 1000)}k registros. Máximo recomendado: ${FILE_LIMITS.maxRows / 1000}k`);
    }

    // Configurar timeout baseado no tamanho estimado
    const dynamicTimeout = Math.min(
      TIMEOUT_CONFIG.import,
      Math.max(120000, estimatedRows * 60) // 60ms por linha, mínimo 2 minutos
    );

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), dynamicTimeout);

    try {
      if (onProgress) {
        onProgress({ 
          step: 'start', 
          progress: 0, 
          message: 'Iniciando importação...', 
          estimatedTime: Math.round(dynamicTimeout / 60000) 
        });
      }

      // Enviar requisição com dados otimizados
      const response = await fetch(`${API_BASE_URL}/ccv/confirmar-classificacao-importar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...importData,
          // Adicionar configurações de otimização
          batch_size: Math.min(1000, Math.max(100, Math.floor(estimatedRows / 10))),
          timeout_warning: Math.floor(dynamicTimeout * 0.8)
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Simular progresso durante processamento (em uma implementação real, 
      // seria através de WebSockets ou polling)
      if (onProgress) {
        const progressInterval = setInterval(() => {
          // Progresso fictício baseado no tempo
          const elapsed = Date.now() - Date.now();
          const progress = Math.min(90, (elapsed / dynamicTimeout) * 100);
          onProgress({ 
            step: 'processing', 
            progress, 
            message: 'Processando registros...' 
          });
        }, 2000);

        // Limpar intervalo quando response chegar
        setTimeout(() => clearInterval(progressInterval), 100);
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        if (response.status === 408) {
          throw new Error('Timeout na importação. Arquivo muito grande. Divida em partes menores.');
        }
        
        if (response.status === 413) {
          throw new Error('Arquivo excede limite do servidor. Reduza o tamanho do arquivo.');
        }
        
        if (response.status === 206) {
          // Importação parcial - retornar dados mesmo com warnings
          const data = await response.json();
          if (onProgress) {
            onProgress({ 
              step: 'warning', 
              progress: 100, 
              message: 'Importação concluída com avisos',
              warnings: data.detalhes 
            });
          }
          return data;
        }
        
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (onProgress) {
        onProgress({ 
          step: 'completed', 
          progress: 100, 
          message: `Importação concluída: ${data.detalhes?.registros_adicionados?.toLocaleString()} registros`,
          details: data.detalhes 
        });
      }

      return data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Importação cancelada por timeout (${Math.round(dynamicTimeout / 60000)} min). Tente com arquivo menor.`);
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Erro na importação:', error);
    
    if (error.message.includes('Failed to fetch')) {
      throw new Error('Erro de conexão durante importação. Verifique sua internet.');
    }
    
    throw error;
  }
};

/**
 * Valida arquivo antes do upload
 */
export const validateFileBeforeUpload = (file) => {
  const errors = [];
  const warnings = [];

  // Validar tipo de arquivo
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ];

  if (!allowedTypes.includes(file.type)) {
    errors.push('Tipo de arquivo não suportado. Use Excel (.xlsx) ou CSV.');
  }

  // Validar tamanho
  if (file.size > FILE_LIMITS.maxSize) {
    errors.push(`Arquivo muito grande (${(file.size / 1024 / 1024).toFixed(1)}MB). Máximo: ${FILE_LIMITS.maxSize / 1024 / 1024}MB`);
  } else if (file.size > FILE_LIMITS.warningSize) {
    warnings.push(`Arquivo grande (${(file.size / 1024 / 1024).toFixed(1)}MB). Processamento pode demorar.`);
  }

  // Validar nome do arquivo
  if (file.name.length > 255) {
    errors.push('Nome do arquivo muito longo');
  }

  // Estimar número de registros baseado no tamanho
  const estimatedRows = Math.floor(file.size / 100); // Estimativa grosseira
  if (estimatedRows > FILE_LIMITS.maxRows) {
    errors.push(`Arquivo muito grande (≈${(estimatedRows / 1000).toFixed(0)}k registros). Máximo: ${FILE_LIMITS.maxRows / 1000}k`);
  } else if (estimatedRows > 50000) {
    warnings.push(`Arquivo grande (≈${(estimatedRows / 1000).toFixed(0)}k registros). Considere dividir em partes menores.`);
  }

  return { errors, warnings, estimatedRows, estimatedTime: Math.ceil(estimatedRows / 2000) };
};

/**
 * Quebra arquivo grande em chunks menores para upload
 */
export const splitFileIntoChunks = (file, chunkSize = 5 * 1024 * 1024) => {
  const chunks = [];
  let start = 0;

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }

  return chunks;
};

/**
 * Upload de arquivo em chunks com retry automático
 */
export const uploadFileInChunks = async (file, onProgress = null) => {
  try {
    const chunks = splitFileIntoChunks(file);
    const totalChunks = chunks.length;
    
    if (totalChunks === 1) {
      // Arquivo pequeno, upload normal
      return await uploadSingleFile(file, onProgress);
    }

    // Upload em chunks para arquivos grandes
    const uploadedChunks = [];
    
    for (let i = 0; i < totalChunks; i++) {
      const chunk = chunks[i];
      const chunkFormData = new FormData();
      chunkFormData.append('chunk', chunk);
      chunkFormData.append('chunkIndex', i.toString());
      chunkFormData.append('totalChunks', totalChunks.toString());
      chunkFormData.append('fileName', file.name);

      let retries = 3;
      let success = false;

      while (retries > 0 && !success) {
        try {
          const response = await fetch(`${API_BASE_URL}/ccv/upload-chunk`, {
            method: 'POST',
            body: chunkFormData,
            timeout: 60000 // 1 minuto por chunk
          });

          if (response.ok) {
            const chunkData = await response.json();
            uploadedChunks.push(chunkData);
            success = true;

            if (onProgress) {
              onProgress({
                step: 'upload',
                progress: ((i + 1) / totalChunks) * 100,
                message: `Enviando parte ${i + 1} de ${totalChunks}...`
              });
            }
          } else {
            throw new Error(`Erro no upload do chunk ${i}`);
          }
        } catch (error) {
          retries--;
          if (retries === 0) {
            throw new Error(`Falha no upload após 3 tentativas: ${error.message}`);
          }
          
          // Aguardar antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, 1000 * (4 - retries)));
        }
      }
    }

    // Finalizar upload combinando chunks no servidor
    const finalizeResponse = await fetch(`${API_BASE_URL}/ccv/finalize-upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        fileName: file.name,
        totalChunks: totalChunks,
        chunkIds: uploadedChunks.map(c => c.chunkId)
      })
    });

    if (!finalizeResponse.ok) {
      throw new Error('Erro ao finalizar upload dos chunks');
    }

    return await finalizeResponse.json();

  } catch (error) {
    console.error('Erro no upload em chunks:', error);
    throw error;
  }
};

/**
 * Upload de arquivo único com retry
 */
const uploadSingleFile = async (file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);

  let retries = 3;
  
  while (retries > 0) {
    try {
      if (onProgress) {
        onProgress({ step: 'upload', progress: 0, message: 'Enviando arquivo...' });
      }

      const response = await fetch(`${API_BASE_URL}/ccv/upload-single`, {
        method: 'POST',
        body: formData,
        timeout: TIMEOUT_CONFIG.analysis
      });

      if (response.ok) {
        if (onProgress) {
          onProgress({ step: 'upload', progress: 100, message: 'Upload concluído' });
        }
        return await response.json();
      } else {
        throw new Error(`Erro HTTP ${response.status}`);
      }
    } catch (error) {
      retries--;
      if (retries === 0) {
        throw error;
      }
      
      // Aguardar antes de tentar novamente
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

/**
 * Monitora status de importação em tempo real (para implementação futura)
 */
export const monitorImportProgress = async (taskId, onProgress = null) => {
  try {
    const maxAttempts = 60; // 5 minutos (5s * 60)
    let attempts = 0;

    const checkStatus = async () => {
      const response = await fetch(`${API_BASE_URL}/ccv/status-importacao/${taskId}`);
      const status = await response.json();

      if (onProgress) {
        onProgress({
          step: status.status,
          progress: status.progress,
          message: status.message,
          details: status
        });
      }

      if (status.status === 'completed' || status.status === 'failed') {
        return status;
      }

      attempts++;
      if (attempts >= maxAttempts) {
        throw new Error('Timeout no monitoramento da importação');
      }

      // Aguardar 5 segundos antes da próxima verificação
      await new Promise(resolve => setTimeout(resolve, 5000));
      return checkStatus();
    };

    return await checkStatus();

  } catch (error) {
    console.error('Erro no monitoramento:', error);
    throw error;
  }
};

/**
 * Otimiza dados antes do envio
 */
export const optimizeDataForTransmission = (data) => {
  try {
    // Comprimir dados se necessário
    if (data.arquivo_excel_base64 && data.arquivo_excel_base64.length > 10 * 1024 * 1024) {
      console.warn('Arquivo base64 muito grande, considerando compressão');
      // Aqui poderia implementar compressão com bibliotecas como pako
    }

    // Remover dados desnecessários
    const optimizedData = {
      ...data,
      // Manter apenas campos essenciais
      metadata: {
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        compressed: false
      }
    };

    return optimizedData;

  } catch (error) {
    console.error('Erro na otimização dos dados:', error);
    return data;
  }
};

/**
 * Calcula hash do arquivo para verificação de integridade
 */
export const calculateFileHash = async (file) => {
  try {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
  } catch (error) {
    console.warn('Não foi possível calcular hash do arquivo:', error);
    return null;
  }
};

/**
 * Configura interceptador para timeout personalizado
 */
export const createTimeoutFetch = (timeout = 30000) => {
  return (url, options = {}) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    return fetch(url, {
      ...options,
      signal: controller.signal
    }).finally(() => {
      clearTimeout(timeoutId);
    });
  };
};

/**
 * Utilitário para formatação de tamanhos de arquivo
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Utilitário para formatação de tempo estimado
 */
export const formatEstimatedTime = (minutes) => {
  if (minutes < 1) return 'menos de 1 minuto';
  if (minutes < 60) return `${Math.round(minutes)} minuto${minutes > 1 ? 's' : ''}`;
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = Math.round(minutes % 60);
  
  return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}min` : ''}`;
};


// Exportar configurações para uso externo
export { TIMEOUT_CONFIG, FILE_LIMITS };

/**
 * Busca todas as empresas ativas.
 */
export async function fetchEmpresas() {
  try {
    console.log('🏢 Fetching empresas...');
    const response = await API.get('/api/ccv/empresas');

    const data = response.data;
    if (!data || !Array.isArray(data.empresas)) {
      throw new Error('Dados de empresas inválidos');
    }

    console.log('🏢 Empresas processadas:', {
      count: data.empresas.length
    });

    return data;
  } catch (error) {
    console.error('💥 Error fetching empresas:', error);
    throw error;
  }
}

/**
 * Busca projetos, opcionalmente filtrados por empresa.
 */
export async function fetchProjetos(empresaId = null) {
  try {
    console.log('📁 Fetching projetos para empresa:', empresaId);
    const params = empresaId ? { empresa_id: empresaId } : {};
    const response = await API.get('/api/ccv/projetos', { params });

    const data = response.data;
    if (!data || !Array.isArray(data.projetos)) {
      throw new Error('Dados de projetos inválidos');
    }

    console.log('📁 Projetos processados:', {
      count: data.projetos.length
    });

    return data;
  } catch (error) {
    console.error('💥 Error fetching projetos:', error);
    throw error;
  }
}

export async function fetchGroupedCCVData(projetoId) {
  try {
    if (!projetoId) throw new Error("ID do projeto é necessário.");
    console.log('📊 Fetching GROUPED CCV data for projeto:', projetoId);

    // Chama a rota de dados agrupados
    const response = await API.get(`/api/ccv/dados-agrupados/${projetoId}`);

    return response.data;
  } catch (error) {
    console.error('💥 Error fetching grouped CCV data:', error);
    throw error;
  }
}

export async function fetchCcvPointDetails(codigoPonto, projetoId) {
  try {
    console.log(`📈 Fetching details for CCV point: ${codigoPonto} in project ${projetoId}`);
    if (!projetoId) {
      throw new Error("ID do projeto é necessário para buscar detalhes do ponto.");
    }
    const response = await API.get(`/api/ccv/ponto/${codigoPonto}`, { 
      params: { projeto_id: projetoId } 
    });

    const data = response.data;
    if (!data || typeof data !== 'object' || data.error) {
      throw new Error(data.error || 'Dados de detalhe do ponto CCV são inválidos');
    }

    console.log('📈 CCV Point Details processed:', {
      hasData: !!data,
      keys: Object.keys(data),
    });

    return data;
  } catch (error) {
    console.error(`💥 Error fetching CCV point details for ${codigoPonto}:`, error);
    throw error;
  }
}

/**
 * Analisa um arquivo Excel e retorna os tipos de veículos únicos encontrados
 * para que o usuário possa classificá-los antes da importação.
 */
export async function analisarTiposVeiculosExcel(projetoId, contagemFile) {
  const formData = new FormData();
  formData.append('projeto_id', projetoId);
  formData.append('contagem_file', contagemFile);

  try {
    const response = await API.post('/api/ccv/analisar-tipos-excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    const errorData = error.response?.data || { error: error.message, details: [] };
    throw errorData;
  }
}

/**
 * Confirma a classificação dos tipos de veículos e executa a importação.
 */
export async function confirmarClassificacaoEImportar(projetoId, tiposClassificados, arquivoBase64) {
  try {
    const response = await API.post('/api/ccv/confirmar-classificacao-importar', {
      projeto_id: projetoId,
      tipos_classificados: tiposClassificados,
      arquivo_excel_base64: arquivoBase64
    });
    return response.data;
  } catch (error) {
    const errorData = error.response?.data || { error: error.message, details: [] };
    throw errorData;
  }
}

/**
 * Converte um arquivo para Base64 para envio na requisição de confirmação.
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Remove o prefixo "data:..."
      const base64 = reader.result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
}

/**
 * Faz o upload do arquivo Excel de contagem (método direto).
 */
export async function uploadContagem(projetoId, contagemFile) {
  const formData = new FormData();
  formData.append('projeto_id', projetoId);
  formData.append('contagem_file', contagemFile);

  try {
    const response = await API.post('/api/ccv/import-contagem', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    const errorData = error.response?.data || { error: error.message, details: [] };
    throw errorData;
  }
}