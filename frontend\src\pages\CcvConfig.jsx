// frontend/src/pages/CcvConfig.jsx

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { fetchEmpresas, fetchProjetos } from '../api/ccvImportService';
import { 
  fetchTiposVeiculosPorProjeto, 
  fetchGruposVeiculos, 
  saveGrupoVeiculo, 
  deleteGrupoVeiculo,
  fetchCategorias 
} from '../api/ccvConfigService';


export default function CcvConfig() {
    const navigate = useNavigate();
    
    // Estados da UI
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    
    // Estados dos Dados
    const [empresas, setEmpresas] = useState([]);
    const [projetos, setProjetos] = useState([]);
    const [selectedEmpresa, setSelectedEmpresa] = useState('');
    const [selectedProject, setSelectedProject] = useState('');
    
    // Estados Específicos da Configuração
    const [categorias, setCategorias] = useState([]);
    const [tiposVeiculo, setTiposVeiculo] = useState([]);
    const [grupos, setGrupos] = useState([]);
    const [showNewGrupoForm, setShowNewGrupoForm] = useState(false);

// Carregamento inicial de empresas
// 1. Carrega as empresas
    useEffect(() => {
        const loadEmpresas = async () => {
            try {
                const data = await fetchEmpresas();
                setEmpresas(data.empresas || []);
            } catch (err) {
                setError('Falha ao carregar empresas.');
            }
        };
        loadEmpresas();
    }, []);

    // 2. Carrega as categorias
    useEffect(() => {
        const loadCategorias = async () => {
            try {
                // Supondo que a rota /api/ccv/categorias-veiculos existe e está funcionando
                // Se não, esta chamada irá falhar, mas não impedirá as empresas de carregar.
                const data = await fetchCategorias();
                setCategorias(data || []);
            } catch (err) {
                // Apenas loga o erro, não impede o funcionamento da página
                console.error('Falha ao carregar categorias, mas a página continuará funcional.');
            }
        };
        loadCategorias();
    }, []);

    // Carregar projetos quando uma empresa é selecionada
    useEffect(() => {
        if (!selectedEmpresa) {
            setProjetos([]);
            setSelectedProject('');
            return;
        }
        const loadProjetos = async () => {
            try {
                const data = await fetchProjetos(selectedEmpresa);
                setProjetos(data.projetos || []);
            } catch (err) {
                setError('Falha ao carregar projetos.');
            }
        };
        loadProjetos();
    }, [selectedEmpresa]);

    // Carregar tipos e grupos quando um projeto é selecionado
    useEffect(() => {
        if (!selectedProject) {
            setTiposVeiculo([]);
            setGrupos([]);
            return;
        }
        loadConfigData();
    }, [selectedProject]);

    const loadConfigData = async () => {
        setLoading(true);
        setError('');
        try {
            const [tiposData, gruposData] = await Promise.all([
                fetchTiposVeiculosPorProjeto(selectedProject),
                fetchGruposVeiculos(selectedProject)
            ]);
            
            // --- INÍCIO DA CORREÇÃO ---
            // Acessa a chave 'tipos_veiculos' dentro do objeto retornado pela API.
            setTiposVeiculo(tiposData.tipos_veiculos || []); 
            
            // Mantém a correção anterior para os grupos.
            setGrupos(gruposData.grupos || []);
            // --- FIM DA CORREÇÃO ---

            } catch (err) {
                setError('Falha ao carregar configurações do projeto.');
                console.error(err); // Adicionado para mais detalhes do erro no console
            } finally {
                setLoading(false);
            }
    };

    const handleCategoriaChange = async (tipoId, novaCategoriaId) => {
        try {
            await updateTipoVeiculo(tipoId, { categoria_id: novaCategoriaId });
            // Atualiza o estado local para refletir a mudança instantaneamente
            setTiposVeiculo(prevTipos => 
                prevTipos.map(t => t.id === tipoId ? { ...t, categoria_id: novaCategoriaId } : t)
            );
        } catch (err) {
            setError('Falha ao atualizar a categoria.');
        }
    };

    const handleSave = async (grupoData) => {
        try {
            await saveGrupoVeiculo(selectedProject, grupoData);
            setSuccess(`Grupo "${grupoData.nome}" salvo com sucesso!`);
            setShowNewGrupoForm(false);
            loadConfigData(); // Recarrega os grupos
        } catch(err) {
            setError('Falha ao salvar o grupo.');
        }
    };
    
    const handleDelete = async (grupoId) => {
        if (window.confirm("Tem certeza que deseja deletar este grupo?")) {
            try {
                await deleteGrupoVeiculo(grupoId);
                setSuccess('Grupo deletado com sucesso!');
                loadConfigData(); // Recarrega os grupos
            } catch(err) {
                setError('Falha ao deletar o grupo.');
            }
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 p-6">
            {/* ... (o header da página permanece o mesmo) ... */}
             <header className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-800">
                    <i className="fas fa-cogs mr-3 text-green-600"></i>
                    Configuração de Veículos CCV
                </h1>
                <button onClick={() => navigate('/ccv')} className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                    <i className="fas fa-arrow-left mr-2"></i>
                    Voltar ao Dashboard
                </button>
            </header>
            
            <div className="bg-white rounded-xl shadow-lg p-8 space-y-6 max-w-4xl mx-auto">
                {/* ... (seleção de projeto permanece a mesma) ... */}
                <div>
                    <h2 className="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">1. Selecione o Projeto</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <select value={selectedEmpresa} onChange={(e) => setSelectedEmpresa(e.target.value)} className="w-full p-2 border-gray-300 rounded-md">
                            <option value="">-- Selecione uma Empresa --</option>
                            {empresas.map(e => <option key={e.id} value={e.id}>{e.nome_empresa}</option>)}
                        </select>
                        <select value={selectedProject} onChange={(e) => setSelectedProject(e.target.value)} disabled={!selectedEmpresa} className="w-full p-2 border-gray-300 rounded-md disabled:bg-gray-200">
                            <option value="">-- Selecione um Projeto --</option>
                            {projetos.map(p => <option key={p.id} value={p.id}>{p.codigo_projeto} - {p.nome_projeto}</option>)}
                        </select>
                    </div>
                </div>

                {selectedProject && (
                    <div>
                        <div className="flex justify-between items-center border-b pb-2 mb-4">
                            <h2 className="text-xl font-semibold text-gray-800">2. Gerenciar Grupos de Veículos</h2>
                            <button onClick={() => setShowNewGrupoForm(true)} className="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                                <i className="fas fa-plus mr-2"></i>
                                Novo Grupo
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            {showNewGrupoForm && (
                                <GrupoEditor 
                                    grupo={{}} 
                                    tiposDisponiveis={tiposVeiculo} 
                                    onSave={handleSave}
                                    onCancel={() => setShowNewGrupoForm(false)}
                                />
                            )}
                            {grupos.map(grupo => (
                                <GrupoEditor 
                                    key={grupo.id}
                                    grupo={grupo}
                                    tiposDisponiveis={tiposVeiculo}
                                    onSave={handleSave}
                                    onDelete={handleDelete}
                                />
                            ))}
                            {grupos.length === 0 && !showNewGrupoForm && (
                                <p className="text-center text-gray-500 py-4">Nenhum grupo criado para este projeto.</p>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

// frontend/src/pages/CcvConfig.jsx (CONTINUAÇÃO)

// Componente para criar/editar um grupo
function GrupoEditor({ grupo, tiposDisponiveis, onSave, onDelete, onCancel }) {
    const [isEditing, setIsEditing] = useState(!grupo.id); // Inicia em modo de edição se for novo
    const [formData, setFormData] = useState({
        id: grupo.id || null,
        nome: grupo.nome || '',
        codigo: grupo.codigo || '',
        cor_grupo: grupo.cor_grupo || '#3498db',
        tipos_ids: grupo.tipos_associados?.map(t => t.tipo_veiculo_id) || []
    });

    const handleSaveClick = () => {
        onSave(formData);
        if(grupo.id) setIsEditing(false); // Sai do modo de edição se estava atualizando
    };

    const handleCancelClick = () => {
        if (!grupo.id) { // Se for um novo grupo, chama o onCancel do pai
            onCancel();
        } else {
            setIsEditing(false); // Apenas sai do modo de edição
            setFormData({ // Reseta para os valores originais
                id: grupo.id,
                nome: grupo.nome,
                codigo: grupo.codigo,
                cor_grupo: grupo.cor_grupo,
                tipos_ids: grupo.tipos_associados?.map(t => t.tipo_veiculo_id) || []
            });
        }
    };

    const handleTipoToggle = (tipoId) => {
        setFormData(prev => {
            const newTiposIds = prev.tipos_ids.includes(tipoId)
                ? prev.tipos_ids.filter(id => id !== tipoId)
                : [...prev.tipos_ids, tipoId];
            return { ...prev, tipos_ids: newTiposIds };
        });
    };

    if (!isEditing) {
        return (
            <div className="bg-gray-50 p-4 rounded-lg border flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <div className="w-4 h-10 rounded" style={{ backgroundColor: grupo.cor_grupo }}></div>
                    <div>
                        <p className="font-bold text-gray-800">{grupo.nome} ({grupo.codigo})</p>
                        <p className="text-xs text-gray-600">{grupo.tipos_associados?.length || 0} tipos associados</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <button onClick={() => setIsEditing(true)} className="text-blue-600 hover:text-blue-800 p-2"><i className="fas fa-edit"></i></button>
                    <button onClick={() => onDelete(grupo.id)} className="text-red-600 hover:text-red-800 p-2"><i className="fas fa-trash"></i></button>
                </div>
            </div>
        );
    }
    
    return (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 space-y-4">
            <h4 className="font-semibold">{grupo.id ? `Editando Grupo: ${grupo.nome}` : 'Criar Novo Grupo'}</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <input type="text" placeholder="Nome do Grupo (Ex: Leves)" value={formData.nome} onChange={e => setFormData({...formData, nome: e.target.value})} className="p-2 border rounded" />
                <input type="text" placeholder="Código (Ex: LEVES)" value={formData.codigo} onChange={e => setFormData({...formData, codigo: e.target.value.toUpperCase()})} className="p-2 border rounded" />
                <input type="color" value={formData.cor_grupo} onChange={e => setFormData({...formData, cor_grupo: e.target.value})} className="p-1 h-10 w-full border rounded" />
            </div>
            <div>
                <label className="block text-sm font-medium mb-2">Associar Tipos de Veículos:</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-40 overflow-y-auto p-2 border rounded bg-white">
                    {tiposDisponiveis.map(tipo => (
                        <label key={tipo.id} className="flex items-center gap-2 text-sm p-1 rounded hover:bg-gray-100">
                            <input type="checkbox" checked={formData.tipos_ids.includes(tipo.id)} onChange={() => handleTipoToggle(tipo.id)} className="rounded" />
                            {tipo.nome}
                        </label>
                    ))}
                </div>
            </div>
            <div className="flex justify-end gap-2">
                <button onClick={handleCancelClick} className="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">Cancelar</button>
                <button onClick={handleSaveClick} className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">Salvar Grupo</button>
            </div>
        </div>
    );
}