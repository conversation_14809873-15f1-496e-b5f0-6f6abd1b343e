# backend/routes_ccv.py - OTIMIZAÇÕES PARA ARQUIVOS GRANDES E ROTAS RESTAURADAS

from flask import Blueprint, jsonify, request, current_app
from app import db
from models import (Empresa, Projeto, CCVPonto, CCVDia, CCVMovimento, CCVContagem, 
                    CCVTipoVeiculo, CCVCategoriaVeiculo, CCVGrupoVeiculo, CCVTipoGrupo)
from sqlalchemy.orm import joinedload, subqueryload
from sqlalchemy import func, text
import pandas as pd
import re
from datetime import datetime
import time
import json
import os
import tempfile
import threading
from werkzeug.utils import secure_filename
from flask import send_file
import logging
from logging.handlers import RotatingFileHandler
import base64
import io

ccv_bp = Blueprint('ccv', __name__, url_prefix='/api/ccv')

# CONFIGURAÇÕES DE OTIMIZAÇÃO
BATCH_SIZE = 1000  # Processar em lotes de 1000 registros
MAX_PROCESSING_TIME = 300  # 5 minutos máximo
COMMIT_INTERVAL = 500  # Commit a cada 500 registros
# Armazenamento temporário para chunks
CHUNK_STORAGE = {}
CHUNK_LOCK = threading.Lock()

# --- INÍCIO: NOVAS ROTAS E FUNÇÕES DE OTIMIZAÇÃO DE UPLOAD ---

@ccv_bp.route('/upload-chunk', methods=['POST'])
def upload_chunk():
    """Recebe e armazena chunks de arquivo para processamento posterior."""
    try:
        if 'chunk' not in request.files:
            return jsonify({'error': 'Chunk não encontrado'}), 400
        
        chunk = request.files['chunk']
        chunk_index = int(request.form.get('chunkIndex', 0))
        total_chunks = int(request.form.get('totalChunks', 1))
        file_name = request.form.get('fileName', 'unknown')
        
        # Gerar ID único para o arquivo
        file_id = secure_filename(file_name) + '_' + str(int(time.time()))
        
        # Criar diretório temporário se não existir
        temp_dir = os.path.join(tempfile.gettempdir(), 'ccv_chunks')
        os.makedirs(temp_dir, exist_ok=True)
        
        # Salvar chunk
        chunk_path = os.path.join(temp_dir, f"{file_id}_chunk_{chunk_index}")
        chunk.save(chunk_path)
        
        # Armazenar informações do chunk
        with CHUNK_LOCK:
            if file_id not in CHUNK_STORAGE:
                CHUNK_STORAGE[file_id] = {
                    'chunks': {},
                    'total_chunks': total_chunks,
                    'file_name': file_name,
                    'upload_time': time.time()
                }
            
            CHUNK_STORAGE[file_id]['chunks'][chunk_index] = chunk_path
        
        current_app.logger.info(f"Chunk {chunk_index + 1}/{total_chunks} recebido para {file_name}")
        
        return jsonify({
            'success': True,
            'chunkId': chunk_index,
            'fileId': file_id,
            'chunksReceived': len(CHUNK_STORAGE[file_id]['chunks']),
            'totalChunks': total_chunks
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro no upload de chunk: {e}", exc_info=True)
        return jsonify({'error': f'Erro no upload: {str(e)}'}), 500

@ccv_bp.route('/finalize-upload', methods=['POST'])
def finalize_upload():
    """Combina chunks em arquivo único e prepara para análise."""
    try:
        data = request.get_json()
        file_name = data.get('fileName')
        total_chunks = data.get('totalChunks')
        chunk_ids = data.get('chunkIds', [])
        
        # Buscar informações do arquivo
        file_id = None
        with CHUNK_LOCK:
            for fid, info in CHUNK_STORAGE.items():
                if info['file_name'] == file_name and info['total_chunks'] == total_chunks:
                    file_id = fid
                    break
        
        if not file_id:
            return jsonify({'error': 'Arquivo não encontrado'}), 404
        
        file_info = CHUNK_STORAGE[file_id]
        
        # Verificar se todos os chunks foram recebidos
        if len(file_info['chunks']) != total_chunks:
            return jsonify({
                'error': f'Chunks incompletos: {len(file_info["chunks"])}/{total_chunks}'
            }), 400
        
        # Combinar chunks em arquivo único
        temp_dir = os.path.join(tempfile.gettempdir(), 'ccv_uploads')
        os.makedirs(temp_dir, exist_ok=True)
        
        final_path = os.path.join(temp_dir, secure_filename(file_name))
        
        with open(final_path, 'wb') as final_file:
            for i in range(total_chunks):
                chunk_path = file_info['chunks'][i]
                with open(chunk_path, 'rb') as chunk_file:
                    final_file.write(chunk_file.read())
                
                # Remover chunk temporário
                os.remove(chunk_path)
        
        # Limpar informações do chunk storage
        with CHUNK_LOCK:
            del CHUNK_STORAGE[file_id]
        
        current_app.logger.info(f"Arquivo {file_name} montado com sucesso: {final_path}")
        
        return jsonify({
            'success': True,
            'filePath': final_path,
            'fileSize': os.path.getsize(final_path),
            'message': 'Upload concluído com sucesso'
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro ao finalizar upload: {e}", exc_info=True)
        return jsonify({'error': f'Erro ao finalizar: {str(e)}'}), 500

@ccv_bp.route('/upload-single', methods=['POST'])
def upload_single():
    """Upload direto para arquivos pequenos."""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Arquivo não encontrado'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
        
        # Validar tamanho
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > 50 * 1024 * 1024:  # 50MB
            return jsonify({'error': 'Arquivo muito grande para upload único'}), 413
        
        # Salvar arquivo
        temp_dir = os.path.join(tempfile.gettempdir(), 'ccv_uploads')
        os.makedirs(temp_dir, exist_ok=True)
        
        file_path = os.path.join(temp_dir, secure_filename(file.filename))
        file.save(file_path)
        
        current_app.logger.info(f"Upload único concluído: {file.filename} ({file_size} bytes)")
        
        return jsonify({
            'success': True,
            'filePath': file_path,
            'fileSize': file_size,
            'fileName': file.filename
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro no upload único: {e}", exc_info=True)
        return jsonify({'error': f'Erro no upload: {str(e)}'}), 500

def cleanup_temp_files():
    """Remove arquivos temporários antigos."""
    try:
        temp_dirs = [
            os.path.join(tempfile.gettempdir(), 'ccv_chunks'),
            os.path.join(tempfile.gettempdir(), 'ccv_uploads')
        ]
        
        current_time = time.time()
        max_age = 3600  # 1 hora
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
                
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age:
                        os.remove(file_path)
                        current_app.logger.info(f"Arquivo temporário removido: {filename}")
        
        # Limpar chunk storage antigo
        with CHUNK_LOCK:
            to_remove = []
            for file_id, info in CHUNK_STORAGE.items():
                if current_time - info['upload_time'] > max_age:
                    to_remove.append(file_id)
            
            for file_id in to_remove:
                del CHUNK_STORAGE[file_id]
                
    except Exception as e:
        current_app.logger.error(f"Erro na limpeza de arquivos temporários: {e}")

@ccv_bp.route('/upload-progress/<string:file_id>', methods=['GET'])
def get_upload_progress(file_id):
    """Retorna progresso do upload em chunks."""
    try:
        with CHUNK_LOCK:
            if file_id not in CHUNK_STORAGE:
                return jsonify({'error': 'Upload não encontrado'}), 404
            
            info = CHUNK_STORAGE[file_id]
            progress = (len(info['chunks']) / info['total_chunks']) * 100
            
            return jsonify({
                'fileId': file_id,
                'fileName': info['file_name'],
                'progress': progress,
                'chunksReceived': len(info['chunks']),
                'totalChunks': info['total_chunks'],
                'complete': len(info['chunks']) == info['total_chunks']
            })
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def setup_optimizations():
    """Configura otimizações do sistema."""
    try:
        db.engine.pool_timeout = 60
        db.engine.pool_recycle = 3600
        
        import atexit
        atexit.register(cleanup_temp_files)
        
        current_app.logger.info("Otimizações CCV configuradas")
        
    except Exception as e:
        current_app.logger.error(f"Erro ao configurar otimizações: {e}")

def setup_optimized_logging():
    """Configura logging otimizado."""
    if not current_app.debug:
        file_handler = RotatingFileHandler(
            'logs/ccv_import.log', 
            maxBytes=10240000, 
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        current_app.logger.addHandler(file_handler)
        
        current_app.logger.setLevel(logging.INFO)
        current_app.logger.info('Sistema CCV otimizado iniciado')

try:
    setup_optimized_logging()
except Exception as e:
    print(f"Erro ao configurar logging: {e}")

# --- FIM: NOVAS ROTAS E FUNÇÕES DE OTIMIZAÇÃO ---


# --- INÍCIO: ROTAS RESTAURADAS E FUNÇÕES OTIMIZADAS ---

@ccv_bp.route('/analisar-tipos-excel', methods=['POST'])
def analisar_tipos_excel():
    """Analisa o arquivo Excel e retorna os tipos de veículos únicos encontrados."""
    if 'projeto_id' not in request.form or 'contagem_file' not in request.files:
        return jsonify({'error': 'ID do projeto e arquivo de contagem são obrigatórios'}), 400

    projeto_id = int(request.form['projeto_id'])
    contagem_file = request.files['contagem_file']

    try:
        start_time = time.time()
        current_app.logger.info(f"Iniciando análise do arquivo para projeto {projeto_id}")

        # --- INÍCIO DA ALTERAÇÃO ---
        # Leitura do arquivo inteiro de uma vez, sem usar chunksize
        df = pd.read_excel(contagem_file.stream, engine='openpyxl')
        df.columns = [str(col).lower().strip() for col in df.columns]

        if 'tipo_veiculo' not in df.columns:
            return jsonify({'error': 'Coluna "tipo_veiculo" não encontrada no arquivo'}), 400

        tipos_unicos_excel = df['tipo_veiculo'].dropna().unique()
        total_rows = len(df)
        # --- FIM DA ALTERAÇÃO ---

        current_app.logger.info(f"Análise concluída: {total_rows} linhas, {len(tipos_unicos_excel)} tipos únicos")

        # O resto da lógica permanece a mesma...
        tipos_globais = {tv.codigo: tv for tv in CCVTipoVeiculo.query.filter_by(projeto_id=None, ativo=True).all()}
        tipos_projeto_existentes = {tv.codigo: tv for tv in CCVTipoVeiculo.query.filter_by(projeto_id=projeto_id).all()}

        categorias = CCVCategoriaVeiculo.query.all()
        categorias_map = [{'id': cat.id, 'codigo': cat.codigo, 'nome': cat.nome} for cat in categorias]

        grupos = CCVGrupoVeiculo.query.filter_by(projeto_id=projeto_id).all()
        grupos_map = [{'id': g.id, 'codigo': g.codigo, 'nome': g.nome, 'cor_grupo': g.cor_grupo} for g in grupos]

        tipos_para_classificar = []
        tipos_ja_existentes = []

        for nome_excel in tipos_unicos_excel:
            codigo_formatado = str(nome_excel).strip().upper()

            if codigo_formatado in tipos_projeto_existentes:
                tipo_existente = tipos_projeto_existentes[codigo_formatado]
                tipos_ja_existentes.append({
                    'nome_excel': str(nome_excel), 'codigo': codigo_formatado,
                    'tipo_id': tipo_existente.id, 'nome_sistema': tipo_existente.nome,
                    'categoria_id': tipo_existente.categoria_id,
                    'categoria_nome': tipo_existente.categoria.nome if tipo_existente.categoria else 'N/A',
                    'status': 'existente_projeto'
                })
            elif codigo_formatado in tipos_globais:
                tipo_global = tipos_globais[codigo_formatado]
                tipos_ja_existentes.append({
                    'nome_excel': str(nome_excel), 'codigo': codigo_formatado,
                    'tipo_id': tipo_global.id, 'nome_sistema': tipo_global.nome,
                    'categoria_id': tipo_global.categoria_id,
                    'categoria_nome': tipo_global.categoria.nome if tipo_global.categoria else 'N/A',
                    'status': 'existente_global'
                })
            else:
                categoria_sugerida = sugerir_categoria(codigo_formatado)
                tipos_para_classificar.append({
                    'nome_excel': str(nome_excel), 'codigo_sugerido': codigo_formatado,
                    'nome_sugerido': str(nome_excel).strip(),
                    'categoria_sugerida_id': categoria_sugerida,
                    'categoria_sugerida_nome': next((c['nome'] for c in categorias_map if c['id'] == categoria_sugerida), 'ESPECIAL'),
                    'equivalente_auto_sugerido': sugerir_equivalente_auto(codigo_formatado)
                })

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"Análise completa em {elapsed_time:.2f}s")

        return jsonify({
            'tipos_para_classificar': tipos_para_classificar,
            'tipos_ja_existentes': tipos_ja_existentes,
            'categorias_disponiveis': categorias_map,
            'grupos_disponiveis': grupos_map,
            'estatisticas': {
                'total_registros': total_rows, 'total_tipos_unicos': len(tipos_unicos_excel),
                'tipos_novos': len(tipos_para_classificar), 'tipos_existentes': len(tipos_ja_existentes),
                'tempo_analise': round(elapsed_time, 2)
            }
        })

    except Exception as e:
        current_app.logger.error(f"Erro na análise do Excel (sem chunk): {e}", exc_info=True)
        return jsonify({'error': f'Erro ao processar arquivo Excel: {e}'}), 500


@ccv_bp.route('/confirmar-classificacao-importar', methods=['POST'])
def confirmar_classificacao_importar():
    """Importação otimizada que agora processa TODOS os tipos de veículos do arquivo."""
    try:
        start_time = time.time()
        data = request.get_json()
        projeto_id = data.get('projeto_id')
        tipos_classificados = data.get('tipos_classificados', [])
        arquivo_excel_base64 = data.get('arquivo_excel_base64')
        
        if not all([projeto_id, arquivo_excel_base64]):
            return jsonify({'error': 'Dados incompletos para a importação'}), 400
        
        current_app.logger.info(f"Iniciando importação para projeto {projeto_id}")

        # 1. Criar os novos tipos de veículos que foram classificados manualmente
        tipos_novos_criados = 0
        if tipos_classificados:
            current_app.logger.info(f"Criando {len(tipos_classificados)} novos tipos")
            for tipo_class in tipos_classificados:
                if not tipo_class.get('criar_novo', True): continue
                
                novo_tipo = CCVTipoVeiculo(
                    projeto_id=projeto_id, 
                    categoria_id=tipo_class['categoria_id'],
                    codigo=tipo_class['codigo'].strip().upper(), 
                    nome=tipo_class['nome'].strip(),
                    equivalente_auto=float(tipo_class.get('equivalente_auto', 1.0)),
                    cor_padrao=tipo_class.get('cor_padrao', '#95a5a6'),
                    codigo_original=tipo_class['nome_excel']
                )
                db.session.add(novo_tipo)
                tipos_novos_criados += 1
            db.session.commit() # Salva os novos tipos para que possam ser encontrados na próxima etapa
            current_app.logger.info(f"{tipos_novos_criados} tipos criados com sucesso")

        # 2. Ler o arquivo Excel para obter a lista COMPLETA de tipos
        arquivo_bytes = base64.b64decode(arquivo_excel_base64)
        df_import = pd.read_excel(io.BytesIO(arquivo_bytes), engine='openpyxl')
        df_import.columns = [str(col).lower().strip() for col in df_import.columns]
        todos_tipos_excel = df_import['tipo_veiculo'].dropna().unique()
        
        # 3. Construir um mapa de referência completo com TODOS os tipos (existentes + novos)
        tipos_globais = {tv.codigo: tv for tv in CCVTipoVeiculo.query.filter_by(projeto_id=None, ativo=True).all()}
        tipos_projeto = {tv.codigo: tv for tv in CCVTipoVeiculo.query.filter_by(projeto_id=projeto_id).all()}
        # O dicionário combinado dá prioridade aos tipos do projeto em caso de conflito de código
        todos_tipos_db = {**tipos_globais, **tipos_projeto}

        mapa_final_tipos = {}
        for nome_excel in todos_tipos_excel:
            # Tenta encontrar pelo código normalizado
            codigo_formatado = str(nome_excel).strip().upper()
            if codigo_formatado in todos_tipos_db:
                mapa_final_tipos[nome_excel] = todos_tipos_db[codigo_formatado]
            else:
                # Tenta encontrar pelo nome original salvo durante a classificação
                tipo_por_original = CCVTipoVeiculo.query.filter_by(projeto_id=projeto_id, codigo_original=nome_excel).first()
                if tipo_por_original:
                    mapa_final_tipos[nome_excel] = tipo_por_original

        # 4. Processar as contagens usando o mapa completo
        registros_adicionados = 0
        erros_de_linha = []
        pontos_projeto_map = {p.codigo_ponto: p for p in CCVPonto.query.filter_by(projeto_id=projeto_id).all()}
        movimentos_cache = {}
        dias_cache = {}
        contagens_para_adicionar = []

        for index, row in df_import.iterrows():
            try:
                # Pula linha se dados essenciais estiverem faltando
                if pd.isna(row.get('ponto')) or pd.isna(row.get('tipo_veiculo')): continue
                
                # Encontra o tipo de veículo no nosso mapa completo
                tipo_veiculo_obj = mapa_final_tipos.get(row['tipo_veiculo'])
                if not tipo_veiculo_obj:
                    erros_de_linha.append(f"Linha {index + 2}: Tipo '{row['tipo_veiculo']}' não classificado/encontrado.")
                    continue

                # O resto da lógica para encontrar ponto, dia, movimento...
                ponto_codigo = str(row['ponto']).strip()
                ponto = pontos_projeto_map.get(ponto_codigo)
                if not ponto: continue
                
                movimento_codigo = str(row.get('movimento')).strip()
                movimento_key = (ponto.id, movimento_codigo)
                movimento_obj = movimentos_cache.get(movimento_key)
                if not movimento_obj:
                    movimento_obj = CCVMovimento.query.filter_by(ponto_id=ponto.id, codigo_movimento=movimento_codigo).first()
                    if not movimento_obj: continue
                    movimentos_cache[movimento_key] = movimento_obj

                data_coleta = pd.to_datetime(row['data']).date()
                dia_key = (ponto.id, data_coleta)
                dia = dias_cache.get(dia_key)
                if not dia:
                    dia, _ = get_or_create(db.session, CCVDia, ponto_id=ponto.id, data_coleta=data_coleta, defaults={'dia_semana': data_coleta.weekday() + 1})
                    dias_cache[dia_key] = dia
                
                hora_obj = pd.to_datetime(str(row['hora']), errors='coerce').time()
                if not hora_obj: continue

                # Adiciona à lista para inserção em lote
                contagens_para_adicionar.append(CCVContagem(
                    dia_id=dia.id, movimento_id=movimento_obj.id, tipo_veiculo_id=tipo_veiculo_obj.id,
                    hora=hora_obj, periodo_inicio=hora_obj, periodo_fim=hora_obj,
                    quantidade=int(row['qte'])
                ))
            
            except Exception as e:
                erros_de_linha.append(f"Linha {index + 2}: Erro - {str(e)}")
        
        if contagens_para_adicionar:
            db.session.bulk_save_objects(contagens_para_adicionar)
            registros_adicionados = len(contagens_para_adicionar)

        db.session.commit()
        total_time = time.time() - start_time
        current_app.logger.info(f"Importação concluída em {total_time:.2f}s")
        
        return jsonify({
            'message': 'Importação concluída com sucesso!',
            'detalhes': {
                'registros_adicionados': registros_adicionados,
                'tipos_novos_criados': tipos_novos_criados,
                'erros_menores': len(erros_de_linha),
                'tempo_processamento': round(total_time, 2),
                'amostra_erros': erros_de_linha[:10]
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erro crítico na importação: {e}", exc_info=True)
        return jsonify({'error': f'Erro crítico na importação: {str(e)}'}), 500

def get_or_create(session, model, defaults=None, **kwargs):
    """Função auxiliar para buscar ou criar um objeto no banco."""
    instance = session.query(model).filter_by(**kwargs).one_or_none()
    if instance:
        return instance, False
    else:
        defaults = defaults or {}
        kwargs.update(defaults)
        instance = model(**kwargs)
        session.add(instance)
        session.flush()
        return instance, True
    
def criar_movimento_se_necessario(ponto, movimento_codigo):
    """Cria movimento automaticamente se for um padrão válido."""
    try:
        if '-' in movimento_codigo:
            partes = movimento_codigo.split('-')
            if len(partes) == 2:
                origem_str, destino_str = partes[0].strip(), partes[1].strip()
                origem_obj = CCVMovimento.query.filter_by(ponto_id=ponto.id, codigo_movimento=origem_str).first()
                destino_obj = CCVMovimento.query.filter_by(ponto_id=ponto.id, codigo_movimento=destino_str).first()
                if origem_obj and destino_obj:
                    movimento_obj = CCVMovimento(
                        ponto_id=ponto.id, codigo_movimento=movimento_codigo,
                        nome_movimento=f"Movimento de {origem_str} para {destino_str}",
                        origem=origem_str, destino=destino_str, tipo_movimento='combinado'
                    )
                    db.session.add(movimento_obj)
                    db.session.flush()
                    return movimento_obj
        return None
    except Exception:
        return None
    
def sugerir_categoria(codigo_tipo):
    """
    Sugere uma categoria baseada no código do tipo de veículo.
    Categorias da empresa: Moto, Bicicleta, Carro, Cam Leve, Cam Pesado, Onibus, Pedestre, Especial
    """
    codigo_upper = codigo_tipo.upper().strip()
    
    # Busca IDs das categorias da empresa
    categorias = {cat.codigo: cat.id for cat in CCVCategoriaVeiculo.query.all()}
    
    # CATEGORIA MOTO
    if re.search(r'^MC$', codigo_upper):
        return categorias.get('MOTO', categorias.get('ESPECIAL'))
    
    # CATEGORIA BICICLETA
    elif re.search(r'^(PC|CYCLIST.*|BICICLETA).*', codigo_upper):
        return categorias.get('BICICLETA', categorias.get('ESPECIAL'))
    
    # CATEGORIA CARRO
    elif re.search(r'^(CAR|CARRO|AUTO).*', codigo_upper):
        return categorias.get('CARRO', categorias.get('ESPECIAL'))
    
    # CATEGORIA ONIBUS
    elif re.search(r'^(.*CB|.*DB|.*SB|.*IB|ONIBUS|BUS|BUS2e|BUS3e|BUS4e).*', codigo_upper):
        return categorias.get('ONIBUS', categorias.get('ESPECIAL'))
    
    # CATEGORIA CAM LEVE - Caminhões comerciais leves (2-3 eixos)
    elif re.search(r'^(LGV|OGV1|2C|3C|2.*AXL|3AXL|CAM2E|CAM3E).*', codigo_upper):
        return categorias.get('CAM_LEVE', categorias.get('ESPECIAL'))
    
    # Caminhões de 2-3 eixos baseado nos padrões específicos
    elif re.search(r'^(2.*AXLES|3AXLES.*(1SUSPENDED|0SUSPENDED))$', codigo_upper):
        return categorias.get('CAM_LEVE', categorias.get('ESPECIAL'))
    
    # CATEGORIA CAM PESADO - Caminhões pesados (4+ eixos) e OGV2
    elif re.search(r'^(OGV2|4C|4CD|2S1|2S2|2S3|3S1|3S2|3S3|2I2|2I3|3I2|3I3|2J3|3J3|2C2|2C3|3C2|3C3|3D3|3D4|3Q4|3T6|CAM4E|CAM5E|CAM6E|CAM7E|CAM8E|CAM9E|CAM9+E).*', codigo_upper):
        return categorias.get('CAM_PESADO', categorias.get('ESPECIAL'))
    
    # Caminhões de 4+ eixos pelos padrões de eixos
    elif re.search(r'^([4-9]AXLES|[4-9].*SUSPENDED|9\+.*AXLES).*', codigo_upper):
        return categorias.get('CAM_PESADO', categorias.get('ESPECIAL'))
    
    # CATEGORIA PEDESTRE
    elif re.search(r'^(PM|PMR|PEDESTRE).*', codigo_upper):
        return categorias.get('PEDESTRE', categorias.get('ESPECIAL'))
    
    # CATEGORIA ESPECIAL - Para casos não identificados (Animal, etc.)
    else:
        return categorias.get('ESPECIAL')
def sugerir_equivalente_auto(codigo_tipo):
    """
    Sugere um equivalente automóvel baseado no tipo de veículo.
    Baseado nos padrões da empresa.
    """
    codigo_upper = codigo_tipo.upper().strip()
    
    # MOTO
    if re.search(r'^MC$', codigo_upper):
        return 0.5
    
    # BICICLETA/CICLISTAS
    elif re.search(r'^(PC|CYCLIST.*|BICICLETA).*', codigo_upper):
        if 'PUSHING' in codigo_upper:
            return 0.3  # Ciclista empurrando ocupa mais espaço
        else:
            return 0.2  # Bicicleta normal
    
    # CARRO
    elif re.search(r'^CAR.*', codigo_upper):
        if 'TRAILER' in codigo_upper:
            if '1_AXLE' in codigo_upper:
                return 1.3  # Carro com reboque 1 eixo
            elif '2_AXLE' in codigo_upper:
                return 1.5  # Carro com reboque 2 eixos
            else:
                return 1.3  # Reboque genérico
        else:
            return 1.0  # Carro normal
    
    # ONIBUS
    elif re.search(r'^(.*CB|.*DB|.*SB|.*IB|ONIBUS|BUS).*', codigo_upper):
        if re.search(r'^(4DB).*', codigo_upper):
            return 2.8  # Ônibus articulado
        else:
            return 2.5  # Ônibus normal
    
    # CAM LEVE - Caminhões comerciais leves
    elif re.search(r'^(LGV|OGV1|2C|3C|2.*AXL|3AXL).*', codigo_upper):
        return 1.8
    
    elif re.search(r'^(2.*AXLES).*', codigo_upper):
        return 1.8  # 2 eixos
    
    elif re.search(r'^(3AXLES).*', codigo_upper):
        return 2.0  # 3 eixos
    
    # CAM PESADO - Por número de eixos
    elif re.search(r'^(OGV2|4.*|.*4E).*', codigo_upper):
        return 2.5  # 4 eixos
    
    elif re.search(r'^(.*5E|2S3|3S2|2I3|2J3|3I2|2C3|3C2).*', codigo_upper):
        return 3.0  # 5 eixos
    
    elif re.search(r'^(.*6E|3S3|3I3|3J3|3C3|3D3).*', codigo_upper):
        return 3.5  # 6 eixos
    
    elif re.search(r'^(.*7E|3D4|3Q4).*', codigo_upper):
        return 4.0  # 7 eixos
    
    elif re.search(r'^(.*8E).*', codigo_upper):
        return 4.5  # 8 eixos
    
    elif re.search(r'^(.*9E|3T6).*', codigo_upper):
        return 5.0  # 9 eixos
    
    elif re.search(r'^(.*9\+E).*', codigo_upper):
        return 5.5  # 9+ eixos
    
    # Padrões específicos de caminhões pesados
    elif re.search(r'^(2S1|2S2|2I2|3S1|2C2).*', codigo_upper):
        return 2.5  # 4 eixos
    
    # PEDESTRE
    elif re.search(r'^(PM|PMR|PEDESTRE).*', codigo_upper):
        return 0.1
    
    # ESPECIAL
    elif re.search(r'^ANIMAL.*', codigo_upper):
        return 0.2
    
    # PADRÃO
    else:
        return 1.0


def sugerir_cor_padrao(codigo_tipo):
    """
    Sugere uma cor padrão baseada na categoria do veículo.
    """
    codigo_upper = codigo_tipo.upper().strip()
    
    # MOTO - Verde claro
    if re.search(r'^MC$', codigo_upper):
        return '#1abc9c'
    
    # BICICLETA - Verde
    elif re.search(r'^(PC|CYCLIST.*|BICICLETA).*', codigo_upper):
        return '#27ae60'
    
    # CARRO - Azul
    elif re.search(r'^(CAR|CARRO|AUTO).*', codigo_upper):
        return '#3498db'
    
    # ONIBUS - Roxo
    elif re.search(r'^(.*CB|.*DB|.*SB|.*IB|ONIBUS|BUS).*', codigo_upper):
        return '#9b59b6'
    
    # CAM LEVE - Laranja
    elif re.search(r'^(LGV|OGV1|2C|3C|2.*AXL|3AXL).*', codigo_upper):
        return '#f39c12'
    
    # CAM PESADO - Vermelho (gradiente baseado no número de eixos)
    elif re.search(r'^(OGV2|4.*|.*4E).*', codigo_upper):
        return '#e74c3c'  # 4 eixos
    
    elif re.search(r'^(.*5E|2S3|3S2|2I3|2J3|3I2|2C3|3C2).*', codigo_upper):
        return '#c0392b'  # 5 eixos
    
    elif re.search(r'^(.*6E|3S3|3I3|3J3|3C3|3D3).*', codigo_upper):
        return '#a93226'  # 6 eixos
    
    elif re.search(r'^(.*7E|3D4|3Q4).*', codigo_upper):
        return '#922b21'  # 7 eixos
    
    elif re.search(r'^(.*8E).*', codigo_upper):
        return '#7b241c'  # 8 eixos
    
    elif re.search(r'^(.*9E|3T6).*', codigo_upper):
        return '#641e16'  # 9 eixos
    
    elif re.search(r'^(.*9\+E).*', codigo_upper):
        return '#4a1a11'  # 9+ eixos
    
    # PEDESTRE - Cinza
    elif re.search(r'^(PM|PMR|PEDESTRE).*', codigo_upper):
        return '#95a5a6'
    
    # ESPECIAL - Cinza escuro
    else:
        return '#34495e'
    
# --- FUNÇÃO HELPER PARA BUSCAR DADOS BASE (EVITA REPETIÇÃO) ---
def get_dados_ccv_base(projeto_id):
    """
    Busca e formata os dados brutos de contagem para um projeto,
    servindo como base para outras rotas.
    """
    query = CCVPonto.query.options(
        subqueryload(CCVPonto.dias).subqueryload(CCVDia.contagens).joinedload(CCVContagem.tipo_veiculo),
        subqueryload(CCVPonto.dias).subqueryload(CCVDia.contagens).joinedload(CCVContagem.movimento),
        joinedload(CCVPonto.movimentos),
        joinedload(CCVPonto.projeto)
    ).filter(CCVPonto.projeto_id == projeto_id, CCVPonto.ativo == True)
    
    pontos = query.all()
    pontos_data = {}

    tipos_veiculos_ativos = CCVTipoVeiculo.query.filter(
        (CCVTipoVeiculo.projeto_id == None) | (CCVTipoVeiculo.projeto_id == projeto_id),
        CCVTipoVeiculo.ativo == True
    ).all()
    
    for ponto in pontos:
        dados_horarios = {}
        for dia in ponto.dias:
            for contagem in dia.contagens:
                hora_str = contagem.hora.strftime('%H:%M')
                if hora_str not in dados_horarios:
                    dados_horarios[hora_str] = {
                        'HORA': hora_str,
                        'Data': dia.data_coleta.isoformat()
                    }
                chave = f"{contagem.tipo_veiculo.codigo}_{contagem.movimento.codigo_movimento}"
                dados_horarios[hora_str][chave] = dados_horarios[hora_str].get(chave, 0) + contagem.quantidade

        pontos_data[ponto.nome_ponto] = {
            'Nome do Ponto': ponto.nome_ponto,
            'Codigo': ponto.codigo_ponto,
            'lat': float(ponto.latitude) if ponto.latitude else None,
            'long': float(ponto.longitude) if ponto.longitude else None,
            'dados_horarios': dados_horarios,
            'aproximacoes': [
                {
                    'codigo': m.codigo_movimento, 
                    'nome': m.nome_movimento,
                    'latitude': float(m.latitude) if m.latitude else None,
                    'longitude': float(m.longitude) if m.longitude else None
                } for m in ponto.movimentos if '-' not in m.codigo_movimento
            ],
            'movimentos': [{'codigo': m.codigo_movimento, 'nome': m.nome_movimento} for m in ponto.movimentos if '-' in m.codigo_movimento]
        }
        
    tipos_veiculos_formatados = [{'id': t.id, 'codigo': t.codigo, 'nome': t.nome, 'cor': t.cor_padrao, 'categoria_id': t.categoria_id} for t in tipos_veiculos_ativos]
    
    return {'ccvData': pontos_data, 'tiposVeiculos': tipos_veiculos_formatados}

# --- ROTAS DA API (mantidas iguais) ---

@ccv_bp.route('/empresas', methods=['GET'])
def get_empresas():
    empresas = Empresa.query.filter_by(ativo=True).all()
    resultado = [{'id': e.id, 'nome_empresa': e.nome_empresa, 'codigo_empresa': e.codigo_empresa} for e in empresas]
    return jsonify({'empresas': resultado})

@ccv_bp.route('/projetos', methods=['GET'])
def get_projetos():
    empresa_id = request.args.get('empresa_id', type=int)
    query = Projeto.query
    if empresa_id:
        query = query.filter_by(empresa_id=empresa_id)
    projetos = query.all()
    resultado = [{'id': p.id, 'codigo_projeto': p.codigo_projeto, 'nome_projeto': p.nome_projeto} for p in projetos]
    return jsonify({'projetos': resultado})

@ccv_bp.route('/dados/<int:projeto_id>', methods=['GET'])
def get_dados_ccv(projeto_id):
    """Endpoint principal que serve os dados CCV brutos."""
    try:
        dados = get_dados_ccv_base(projeto_id)
        return jsonify(dados)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ccv_bp.route('/dados-agrupados/<int:projeto_id>', methods=['GET'])
def get_dados_agrupados(projeto_id):
    """Retorna dados CCV com volumes agregados pelos grupos configurados."""
    try:
        dados_base = get_dados_ccv_base(projeto_id)
        grupos = CCVGrupoVeiculo.query.options(
            subqueryload(CCVGrupoVeiculo.tipos_associados).joinedload(CCVTipoGrupo.tipo_veiculo)
        ).filter_by(projeto_id=projeto_id).all()
        
        if not grupos:
            return jsonify(dados_base)

        mapa_tipo_id_para_grupos = {}
        for grupo in grupos:
            for tipo_assoc in grupo.tipos_associados:
                tipo_id = tipo_assoc.tipo_veiculo.id
                if tipo_id not in mapa_tipo_id_para_grupos:
                    mapa_tipo_id_para_grupos[tipo_id] = []
                mapa_tipo_id_para_grupos[tipo_id].append(grupo.codigo)

        mapa_codigo_para_tipo_id = {t['codigo']: t['id'] for t in dados_base['tiposVeiculos']}

        for ponto_data in dados_base.get('ccvData', {}).values():
            for hora_dados in ponto_data.get('dados_horarios', {}).values():
                somas_grupos_movimento = {}
                for chave, valor in hora_dados.items():
                    if '_' not in chave: continue
                    
                    tipo_codigo, mov_codigo = chave.split('_', 1)
                    tipo_id = mapa_codigo_para_tipo_id.get(tipo_codigo)
                    
                    if tipo_id in mapa_tipo_id_para_grupos:
                        for grupo_codigo in mapa_tipo_id_para_grupos[tipo_id]:
                            chave_grupo = f"GRUPO_{grupo_codigo}_{mov_codigo}"
                            somas_grupos_movimento[chave_grupo] = somas_grupos_movimento.get(chave_grupo, 0) + valor
                
                hora_dados.update(somas_grupos_movimento)
        
        dados_base['grupos_configurados'] = [g.to_dict(include_tipos=True) for g in grupos]
        return jsonify(dados_base)
        
    except Exception as e:
        current_app.logger.error(f"Erro ao agrupar dados: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@ccv_bp.route('/grupos-veiculos/<int:projeto_id>', methods=['GET', 'POST'])
def manage_grupos_veiculos(projeto_id):
    """Gerencia grupos de um projeto (Cria, Atualiza, Lista)."""
    if request.method == 'POST':
        try:
            data = request.get_json()
            grupo_id = data.get('id')
            codigo = data.get('codigo', '').strip().upper()
            nome = data.get('nome', '').strip()

            if not codigo or not nome:
                return jsonify({'error': 'Código e Nome do grupo são obrigatórios'}), 400

            existing_group = CCVGrupoVeiculo.query.filter_by(
                projeto_id=projeto_id, 
                codigo=codigo
            ).first()

            if existing_group and existing_group.id != grupo_id:
                return jsonify({'error': f"Um grupo com o código '{codigo}' já existe neste projeto."}), 409

            if grupo_id:
                grupo = CCVGrupoVeiculo.query.get(grupo_id)
                if not grupo: 
                    return jsonify({'error': 'Grupo não encontrado'}), 404
            else:
                grupo = CCVGrupoVeiculo(projeto_id=projeto_id)
                db.session.add(grupo)
            
            grupo.nome = nome
            grupo.codigo = codigo
            grupo.cor_grupo = data.get('cor_grupo')
            grupo.ativo = data.get('ativo', True)
            
            db.session.flush()

            CCVTipoGrupo.query.filter_by(grupo_id=grupo.id).delete()
            tipos_ids = data.get('tipos_ids', [])
            for tipo_id in tipos_ids:
                associacao = CCVTipoGrupo(grupo_id=grupo.id, tipo_veiculo_id=tipo_id)
                db.session.add(associacao)
                
            db.session.commit()
            return jsonify(grupo.to_dict(include_tipos=True))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Erro ao salvar grupo: {e}", exc_info=True)
            return jsonify({'error': str(e)}), 500

    try:
        grupos = CCVGrupoVeiculo.query.filter_by(projeto_id=projeto_id).all()
        return jsonify({'grupos': [g.to_dict(include_tipos=True) for g in grupos]})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ccv_bp.route('/categorias-veiculos', methods=['GET'])
def get_categorias_veiculos():
    """Lista todas as categorias de veículos globais."""
    try:
        categorias = CCVCategoriaVeiculo.query.all()
        return jsonify([{'id': cat.id, 'codigo': cat.codigo, 'nome': cat.nome} for cat in categorias])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ccv_bp.route('/tipos-veiculos/<int:projeto_id>', methods=['GET'])
def get_tipos_veiculos_projeto(projeto_id):
    """Lista tipos de veículos para um projeto (globais + específicos)."""
    try:
        tipos = CCVTipoVeiculo.query.filter(
            (CCVTipoVeiculo.projeto_id == projeto_id) | (CCVTipoVeiculo.projeto_id == None)
        ).options(joinedload(CCVTipoVeiculo.categoria)).order_by(CCVTipoVeiculo.nome).all()
        
        resultado = []
        for t in tipos:
            resultado.append({
                'id': t.id, 'codigo': t.codigo, 'nome': t.nome, 
                'cor_padrao': t.cor_padrao, 'categoria_id': t.categoria_id,
                'categoria_nome': t.categoria.nome if t.categoria else 'N/A',
                'codigo_original': t.codigo_original,
                'is_global': t.projeto_id is None
            })
        return jsonify({'tipos_veiculos': resultado})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ccv_bp.route('/tipos-veiculos/<int:tipo_id>', methods=['PUT'])
def update_tipo_veiculo(tipo_id):
    """Atualiza um tipo de veículo (ex: muda a categoria)."""
    try:
        data = request.get_json()
        tipo_veiculo = CCVTipoVeiculo.query.get(tipo_id)
        if not tipo_veiculo:
            return jsonify({'error': 'Tipo de veículo não encontrado'}), 404
        
        if 'categoria_id' in data:
            tipo_veiculo.categoria_id = data['categoria_id']
        
        db.session.commit()
        return jsonify({'message': 'Tipo de veículo atualizado com sucesso!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
    
@ccv_bp.route('/ponto/<string:codigo_ponto>', methods=['GET'])
def get_detalhes_ponto_ccv(codigo_ponto):
    """
    Retorna análises técnicas detalhadas de um ponto específico.
    Inclui dados para relatórios técnicos de engenharia de tráfego.
    """
    try:
        projeto_id = request.args.get('projeto_id', type=int)
        if not projeto_id:
            return jsonify({'error': 'projeto_id é obrigatório'}), 400

        # Buscar o ponto
        ponto = CCVPonto.query.filter_by(
            codigo_ponto=codigo_ponto,
            projeto_id=projeto_id,
            ativo=True
        ).first()
        
        if not ponto:
            return jsonify({'error': f'Ponto {codigo_ponto} não encontrado no projeto {projeto_id}'}), 404

        # Buscar dados de contagem com relacionamentos
        query = CCVDia.query.options(
            subqueryload(CCVDia.contagens).joinedload(CCVContagem.tipo_veiculo),
            subqueryload(CCVDia.contagens).joinedload(CCVContagem.movimento)
        ).filter(CCVDia.ponto_id == ponto.id)
        
        dias = query.all()
        
        if not dias:
            return jsonify({'error': 'Nenhum dado de contagem encontrado para este ponto'}), 404

        # Organizar dados por período horário
        dados_horarios = {}
        
        for dia in dias:
            for contagem in dia.contagens:
                hora_str = contagem.hora.strftime('%H:%M')
                
                if hora_str not in dados_horarios:
                    dados_horarios[hora_str] = {
                        'HORA': hora_str,
                        'Data': dia.data_coleta.isoformat(),
                        'DiaSemana': dia.dia_semana,
                        'Responsavel': dia.responsavel
                    }
                
                # Chave do tipo_movimento
                chave = f"{contagem.tipo_veiculo.codigo}_{contagem.movimento.codigo_movimento}"
                dados_horarios[hora_str][chave] = dados_horarios[hora_str].get(chave, 0) + contagem.quantidade

        # Buscar informações complementares do ponto
        movimentos = CCVMovimento.query.filter_by(ponto_id=ponto.id).all()
        tipos_utilizados = set()
        
        for dia in dias:
            for contagem in dia.contagens:
                tipos_utilizados.add(contagem.tipo_veiculo.id)
        
        tipos_veiculos = CCVTipoVeiculo.query.filter(
            CCVTipoVeiculo.id.in_(tipos_utilizados)
        ).options(joinedload(CCVTipoVeiculo.categoria)).all()

        # Calcular estatísticas básicas
        total_veiculos = sum(
            sum(
                valor for chave, valor in periodo.items() 
                if isinstance(valor, (int, float)) and '_' in str(chave)
            ) 
            for periodo in dados_horarios.values()
        )
        
        total_dias = len(set(periodo['Data'] for periodo in dados_horarios.values()))
        total_horas = len(dados_horarios)

        # Informações de localização se disponível
        coordenadas = {
            'latitude': float(ponto.latitude) if ponto.latitude else None,
            'longitude': float(ponto.longitude) if ponto.longitude else None
        }

        # Metadados do projeto
        projeto = Projeto.query.get(projeto_id)
        empresa = Empresa.query.get(projeto.empresa_id) if projeto else None

        # Preparar resposta
        resultado = {
            'ponto': {
                'codigo': ponto.codigo_ponto,
                'nome': ponto.nome_ponto,
                'coordenadas': coordenadas,
                'endereco': getattr(ponto, 'endereco', None),
                'observacoes': getattr(ponto, 'observacoes', None)
            },
            'projeto': {
                'id': projeto.id,
                'codigo': projeto.codigo_projeto,
                'nome': projeto.nome_projeto
            } if projeto else None,
            'empresa': {
                'nome': empresa.nome_empresa,
                'codigo': empresa.codigo_empresa
            } if empresa else None,
            'dados_horarios': dados_horarios,
            'movimentos': [
                {
                    'codigo': m.codigo_movimento,
                    'nome': m.nome_movimento,
                    'tipo': getattr(m, 'tipo_movimento', 'simples'),
                    'origem': getattr(m, 'origem', None),
                    'destino': getattr(m, 'destino', None)
                } for m in movimentos
            ],
            'tipos_veiculos': [
                {
                    'id': tv.id,
                    'codigo': tv.codigo,
                    'nome': tv.nome,
                    'categoria': tv.categoria.nome if tv.categoria else None,
                    'equivalente_auto': tv.equivalente_auto,
                    'cor': tv.cor_padrao
                } for tv in tipos_veiculos
            ],
            'estatisticas_basicas': {
                'total_veiculos': total_veiculos,
                'total_dias_coleta': total_dias,
                'total_periodos_horarios': total_horas,
                'data_inicio': min(periodo['Data'] for periodo in dados_horarios.values()) if dados_horarios else None,
                'data_fim': max(periodo['Data'] for periodo in dados_horarios.values()) if dados_horarios else None,
                'media_veiculos_hora': round(total_veiculos / total_horas) if total_horas > 0 else 0
            },
            'metadados': {
                'data_consulta': datetime.now().isoformat(),
                'versao_sistema': '2.0',
                'tipo_relatorio': 'detalhes_tecnicos_ccv'
            }
        }
        
        return jsonify(resultado)
        
    except Exception as e:
        current_app.logger.error(f"Erro ao buscar detalhes do ponto {codigo_ponto}: {e}", exc_info=True)
        return jsonify({'error': f'Erro interno do servidor: {str(e)}'}), 500

# === FUNÇÕES AUXILIARES PARA ANÁLISES TÉCNICAS ===

def calcular_fator_hora_pico(volumes_horarios):
    """
    Calcula o Fator de Hora de Pico (FHP) baseado nos volumes horários.
    FHP = Volume da hora pico / (4 × Volume do período de 15 min de maior movimento na hora pico)
    """
    if not volumes_horarios:
        return 0
    
    # Encontrar a hora de maior volume
    hora_pico = max(volumes_horarios.items(), key=lambda x: x[1])
    volume_hora_pico = hora_pico[1]
    
    # Para calcular FHP corretamente, precisaríamos dos dados de 15 em 15 minutos
    # Como temos dados horários, usamos uma aproximação
    volumes_ordenados = sorted(volumes_horarios.values(), reverse=True)
    
    # Aproximação: FHP = volume_pico / média dos 4 maiores volumes
    if len(volumes_ordenados) >= 4:
        media_4_maiores = sum(volumes_ordenados[:4]) / 4
        fhp = volume_hora_pico / media_4_maiores if media_4_maiores > 0 else 0
    else:
        # Se temos menos de 4 períodos, usar todos disponíveis
        media_disponivel = sum(volumes_ordenados) / len(volumes_ordenados)
        fhp = volume_hora_pico / media_disponivel if media_disponivel > 0 else 0
    
    return min(fhp, 1.0)  # FHP não pode ser maior que 1.0

def analisar_composicao_veicular(dados_horarios):
    """
    Analisa a composição veicular e retorna estatísticas detalhadas.
    """
    tipos_volumes = {}
    
    for periodo in dados_horarios.values():
        for chave, volume in periodo.items():
            if isinstance(volume, (int, float)) and '_' in str(chave):
                tipo = chave.split('_')[0]
                tipos_volumes[tipo] = tipos_volumes.get(tipo, 0) + volume
    
    total = sum(tipos_volumes.values())
    
    if total == 0:
        return {'tipos': {}, 'total': 0, 'percentuais': {}}
    
    percentuais = {tipo: (volume / total * 100) for tipo, volume in tipos_volumes.items()}
    
    return {
        'tipos': tipos_volumes,
        'total': total,
        'percentuais': percentuais,
        'tipo_dominante': max(tipos_volumes.items(), key=lambda x: x[1]) if tipos_volumes else None
    }

def analisar_distribuicao_temporal(dados_horarios):
    """
    Analisa a distribuição temporal do tráfego.
    """
    volumes_por_hora = {}
    volumes_por_dia = {}
    
    for periodo in dados_horarios.values():
        hora = periodo['HORA']
        data = periodo['Data']
        
        # Volume por hora
        volume_periodo = sum(
            valor for chave, valor in periodo.items() 
            if isinstance(valor, (int, float)) and '_' in str(chave)
        )
        
        volumes_por_hora[hora] = volumes_por_hora.get(hora, 0) + volume_periodo
        volumes_por_dia[data] = volumes_por_dia.get(data, 0) + volume_periodo
    
    # Estatísticas
    if volumes_por_hora:
        hora_pico = max(volumes_por_hora.items(), key=lambda x: x[1])
        volume_total = sum(volumes_por_hora.values())
        volume_medio = volume_total / len(volumes_por_hora)
        
        # Coeficiente de variação
        import math
        variancia = sum((v - volume_medio) ** 2 for v in volumes_por_hora.values()) / len(volumes_por_hora)
        desvio_padrao = math.sqrt(variancia)
        coef_variacao = desvio_padrao / volume_medio if volume_medio > 0 else 0
        
        return {
            'volumes_por_hora': volumes_por_hora,
            'volumes_por_dia': volumes_por_dia,
            'hora_pico': hora_pico,
            'volume_total': volume_total,
            'volume_medio': volume_medio,
            'coeficiente_variacao': coef_variacao,
            'desvio_padrao': desvio_padrao
        }
    
    return None

def analisar_movimentos_direcionais(dados_horarios):
    """
    Analisa a distribuição dos movimentos direcionais.
    """
    volumes_por_movimento = {}
    
    for periodo in dados_horarios.values():
        for chave, volume in periodo.items():
            if isinstance(volume, (int, float)) and '_' in str(chave):
                movimento = chave.split('_', 1)[1]  # Pega tudo após o primeiro _
                volumes_por_movimento[movimento] = volumes_por_movimento.get(movimento, 0) + volume
    
    total = sum(volumes_por_movimento.values())
    
    if total == 0:
        return {'movimentos': {}, 'total': 0, 'percentuais': {}}
    
    percentuais = {mov: (volume / total * 100) for mov, volume in volumes_por_movimento.items()}
    
    # Análise de balanceamento
    max_percentual = max(percentuais.values()) if percentuais else 0
    balanceamento = 'balanceado' if max_percentual < 40 else 'desbalanceado'
    
    return {
        'movimentos': volumes_por_movimento,
        'total': total,
        'percentuais': percentuais,
        'movimento_dominante': max(volumes_por_movimento.items(), key=lambda x: x[1]) if volumes_por_movimento else None,
        'balanceamento': balanceamento,
        'max_percentual': max_percentual
    }

# === ROTA PARA ANÁLISES TÉCNICAS AVANÇADAS ===

@ccv_bp.route('/ponto/<string:codigo_ponto>/analises', methods=['GET'])
def get_analises_tecnicas_ponto(codigo_ponto):
    """
    Retorna análises técnicas avançadas para um ponto específico.
    Inclui FHP, análise temporal, composição veicular e distribuição direcional.
    """
    try:
        projeto_id = request.args.get('projeto_id', type=int)
        if not projeto_id:
            return jsonify({'error': 'projeto_id é obrigatório'}), 400

        # Buscar dados básicos do ponto
        detalhes_response = get_detalhes_ponto_ccv(codigo_ponto)
        
        if detalhes_response[1] != 200:  # Se não foi sucesso
            return detalhes_response
        
        dados = detalhes_response[0].get_json()
        dados_horarios = dados['dados_horarios']
        
        # Executar análises técnicas
        analise_temporal = analisar_distribuicao_temporal(dados_horarios)
        analise_composicao = analisar_composicao_veicular(dados_horarios)
        analise_movimentos = analisar_movimentos_direcionais(dados_horarios)
        
        # Calcular FHP se tivermos dados temporais
        fhp = 0
        if analise_temporal:
            fhp = calcular_fator_hora_pico(analise_temporal['volumes_por_hora'])
        
        # Interpretações técnicas automáticas
        interpretacoes = {
            'fhp': {
                'valor': round(fhp, 3),
                'interpretacao': (
                    'Tráfego bem distribuído' if fhp > 0.9 else
                    'Tráfego moderadamente concentrado' if fhp > 0.7 else
                    'Tráfego altamente concentrado'
                ),
                'qualidade': (
                    'excelente' if fhp > 0.9 else
                    'boa' if fhp > 0.8 else
                    'regular' if fhp > 0.7 else
                    'ruim'
                )
            },
            'variabilidade': {
                'coeficiente': round(analise_temporal['coeficiente_variacao'], 3) if analise_temporal else 0,
                'interpretacao': (
                    'Baixa variabilidade' if analise_temporal and analise_temporal['coeficiente_variacao'] < 0.5 else
                    'Variabilidade moderada' if analise_temporal and analise_temporal['coeficiente_variacao'] < 1.0 else
                    'Alta variabilidade'
                )
            },
            'balanceamento_direcional': {
                'status': analise_movimentos['balanceamento'],
                'movimento_dominante': analise_movimentos['movimento_dominante'][0] if analise_movimentos['movimento_dominante'] else None,
                'percentual_dominante': round(analise_movimentos['max_percentual'], 1)
            }
        }
        
        # Recomendações técnicas automáticas
        recomendacoes = []
        
        if fhp < 0.7:
            recomendacoes.append({
                'tipo': 'capacidade',
                'prioridade': 'alta',
                'descricao': 'FHP baixo indica concentração crítica de tráfego. Considere medidas de gestão de demanda.',
                'medidas_sugeridas': [
                    'Análise de semáforos para otimização de tempos',
                    'Implementação de faixas exclusivas em horários de pico',
                    'Estudo de rotas alternativas'
                ]
            })
        
        if analise_temporal and analise_temporal['coeficiente_variacao'] > 1.0:
            recomendacoes.append({
                'tipo': 'variabilidade',
                'prioridade': 'media',
                'descricao': 'Alta variabilidade temporal indica necessidade de análise mais detalhada.',
                'medidas_sugeridas': [
                    'Coleta de dados em mais dias para confirmar padrões',
                    'Análise de eventos especiais que possam influenciar o tráfego'
                ]
            })
        
        if analise_movimentos['balanceamento'] == 'desbalanceado':
            recomendacoes.append({
                'tipo': 'geometria',
                'prioridade': 'media',
                'descricao': f'Movimento {analise_movimentos["movimento_dominante"][0]} domina com {analise_movimentos["max_percentual"]:.1f}% do tráfego.',
                'medidas_sugeridas': [
                    'Revisão da geometria viária',
                    'Análise de sinalização direcional',
                    'Estudo de capacidade específica por movimento'
                ]
            })
        
        resultado = {
            'codigo_ponto': codigo_ponto,
            'projeto_id': projeto_id,
            'analise_temporal': analise_temporal,
            'analise_composicao': analise_composicao,
            'analise_movimentos': analise_movimentos,
            'interpretacoes_tecnicas': interpretacoes,
            'recomendacoes': recomendacoes,
            'resumo_executivo': {
                'total_veiculos': dados['estatisticas_basicas']['total_veiculos'],
                'dias_coleta': dados['estatisticas_basicas']['total_dias_coleta'],
                'fhp': round(fhp, 3),
                'tipo_predominante': analise_composicao['tipo_dominante'][0] if analise_composicao['tipo_dominante'] else None,
                'percentual_predominante': round(analise_composicao['percentuais'][analise_composicao['tipo_dominante'][0]], 1) if analise_composicao['tipo_dominante'] else 0,
                'hora_pico': analise_temporal['hora_pico'][0] if analise_temporal else None,
                'volume_hora_pico': analise_temporal['hora_pico'][1] if analise_temporal else 0
            },
            'metadados': {
                'data_analise': datetime.now().isoformat(),
                'versao_algoritmo': '1.0',
                'tipo_analise': 'tecnica_avancada'
            }
        }
        
        return jsonify(resultado)
        
    except Exception as e:
        current_app.logger.error(f"Erro ao executar análises técnicas do ponto {codigo_ponto}: {e}", exc_info=True)
        return jsonify({'error': f'Erro interno do servidor: {str(e)}'}), 500

# === ROTA PARA EXPORTAR RELATÓRIO EM PDF ===

@ccv_bp.route('/ponto/<string:codigo_ponto>/relatorio-pdf', methods=['GET'])
def exportar_relatorio_pdf(codigo_ponto):
    """
    Gera e retorna um relatório técnico em PDF para um ponto específico.
    """
    try:
        projeto_id = request.args.get('projeto_id', type=int)
        if not projeto_id:
            return jsonify({'error': 'projeto_id é obrigatório'}), 400

        # Buscar dados e análises
        analises_response = get_analises_tecnicas_ponto(codigo_ponto)
        
        if analises_response[1] != 200:
            return analises_response
        
        analises = analises_response[0].get_json()
        
        # Aqui você implementaria a geração do PDF
        # Por exemplo, usando reportlab ou weasyprint
        
        # Por enquanto, retornamos os dados para o frontend gerar o PDF
        return jsonify({
            'message': 'Dados preparados para geração de PDF',
            'dados_relatorio': analises,
            'formato': 'pdf_data'
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro ao gerar relatório PDF do ponto {codigo_ponto}: {e}", exc_info=True)
        return jsonify({'error': f'Erro interno do servidor: {str(e)}'}), 500