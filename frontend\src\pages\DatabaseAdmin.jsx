// frontend/src/pages/DatabaseAdmin.jsx

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  fetchDatabaseStats,
  fetchAllEmpresas,
  createEmpresa,
  deleteEmpresa,
  fetchAllProjetos,
  createProjeto,
  deleteProjeto,
  cleanProductData,
  resetDatabase
} from '../api/adminService';

// Configuração dos produtos
const PRODUTOS = {
  ped: { nome: 'PED - Pontos de Embarque e Desembarque', cor: 'blue', icone: 'fas fa-bus' },
  ccv: { nome: 'CCV - Contagem Classificada de Veículos', cor: 'green', icone: 'fas fa-car' },
  via: { nome: 'VIA - Sistema Viário', cor: 'indigo', icone: 'fas fa-road' },
  ftp: { nome: 'FTP - Fluxo de Travessias de Pedestres', cor: 'orange', icone: 'fas fa-walking' },
  tlp: { nome: 'TLP - Tempo de Viagem em Links', cor: 'purple', icone: 'fas fa-route' },
  pod: { nome: 'POD - Pesquisa Origem-Destino', cor: 'red', icone: 'fas fa-map-marked-alt' }
};

export default function DatabaseAdmin() {
  const navigate = useNavigate();
  
  // Estados principais
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [empresas, setEmpresas] = useState([]);
  const [projetos, setProjetos] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Estados de formulários
  const [showEmpresaForm, setShowEmpresaForm] = useState(false);
  const [showProjetoForm, setShowProjetoForm] = useState(false);
  const [empresaForm, setEmpresaForm] = useState({
    nome_empresa: '',
    codigo_empresa: '',
    gerencia: '',
    endereco: '',
    telefone: '',
    email: ''
  });
  const [projetoForm, setProjetoForm] = useState({
    empresa_id: '',
    codigo_projeto: '',
    nome_projeto: '',
    descricao: '',
    cliente: '',
    coordenador: ''
  });
  
  // Estados de filtros
  const [empresaFilter, setEmpresaFilter] = useState('');
  
  // Estados de UI
  const [message, setMessage] = useState({ type: '', text: '' });
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (activeTab === 'projetos') {
      loadProjetos();
    }
  }, [activeTab, empresaFilter]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [statsData, empresasData] = await Promise.all([
        fetchDatabaseStats(),
        fetchAllEmpresas()
      ]);
      
      setStats(statsData.stats);
      setEmpresas(empresasData.empresas);
    } catch (error) {
      showMessage('error', `Erro ao carregar dados: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadProjetos = async () => {
    try {
      const data = await fetchAllProjetos(empresaFilter || null);
      setProjetos(data.projetos);
    } catch (error) {
      showMessage('error', `Erro ao carregar projetos: ${error.message}`);
    }
  };

  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  const handleCreateEmpresa = async (e) => {
    e.preventDefault();
    try {
      setActionLoading(true);
      await createEmpresa(empresaForm);
      showMessage('success', 'Empresa criada com sucesso!');
      setShowEmpresaForm(false);
      setEmpresaForm({
        nome_empresa: '',
        codigo_empresa: '',
        gerencia: '',
        endereco: '',
        telefone: '',
        email: ''
      });
      loadInitialData();
    } catch (error) {
      showMessage('error', `Erro ao criar empresa: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteEmpresa = async (empresaId, nomeEmpresa) => {
    if (!window.confirm(`Tem certeza que deseja deletar a empresa "${nomeEmpresa}" e todos os seus dados? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      setActionLoading(true);
      const result = await deleteEmpresa(empresaId);
      showMessage('success', result.message);
      loadInitialData();
    } catch (error) {
      showMessage('error', `Erro ao deletar empresa: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreateProjeto = async (e) => {
    e.preventDefault();
    try {
      setActionLoading(true);
      await createProjeto(projetoForm);
      showMessage('success', 'Projeto criado com sucesso!');
      setShowProjetoForm(false);
      setProjetoForm({
        empresa_id: '',
        codigo_projeto: '',
        nome_projeto: '',
        descricao: '',
        cliente: '',
        coordenador: ''
      });
      loadProjetos();
    } catch (error) {
      showMessage('error', `Erro ao criar projeto: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteProjeto = async (projetoId, nomeProjeto) => {
    if (!window.confirm(`Tem certeza que deseja deletar o projeto "${nomeProjeto}" e todos os seus dados? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      setActionLoading(true);
      const result = await deleteProjeto(projetoId);
      showMessage('success', result.message);
      loadProjetos();
    } catch (error) {
      showMessage('error', `Erro ao deletar projeto: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleCleanProduct = async (produto) => {
    const produtoInfo = PRODUTOS[produto];
    if (!window.confirm(`Tem certeza que deseja limpar TODOS os dados do produto ${produtoInfo.nome}? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      setActionLoading(true);
      const result = await cleanProductData(produto);
      showMessage('success', result.message);
      loadInitialData();
    } catch (error) {
      showMessage('error', `Erro ao limpar produto: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleResetDatabase = async () => {
    const confirmText = 'RESETAR TUDO';
    const userInput = prompt(`ATENÇÃO: Esta ação irá DELETAR TODOS OS DADOS do banco de dados!\n\nPara confirmar, digite exatamente: ${confirmText}`);
    
    if (userInput !== confirmText) {
      showMessage('error', 'Operação cancelada. Texto de confirmação incorreto.');
      return;
    }

    try {
      setActionLoading(true);
      const result = await resetDatabase();
      showMessage('success', result.message);
      loadInitialData();
    } catch (error) {
      showMessage('error', `Erro ao resetar banco: ${error.error || error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800">Carregando dados do banco...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <i className="fas fa-database text-2xl text-blue-600 mr-3"></i>
            <h1 className="text-2xl font-bold text-gray-800">Administração do Banco de Dados</h1>
          </div>
          <button 
            onClick={() => navigate('/')} 
            className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i className="fas fa-arrow-left mr-2"></i>Voltar ao Dashboard
          </button>
        </div>
      </header>

      <main className="container mx-auto p-6">
        
        {/* Mensagens */}
        {message.text && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            <div className="flex items-center">
              <i className={`fas ${message.type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'} mr-2`}></i>
              {message.text}
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="flex border-b">
            {[
              { id: 'overview', label: 'Visão Geral', icon: 'fas fa-chart-pie' },
              { id: 'empresas', label: 'Empresas', icon: 'fas fa-building' },
              { id: 'projetos', label: 'Projetos', icon: 'fas fa-folder' },
              { id: 'produtos', label: 'Produtos', icon: 'fas fa-cubes' },
              { id: 'danger', label: 'Zona de Perigo', icon: 'fas fa-exclamation-triangle' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-4 font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                } ${tab.id === 'danger' ? 'text-red-500 hover:text-red-700' : ''}`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab: Visão Geral */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            
            {/* Estatísticas Gerais */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">
                <i className="fas fa-chart-bar mr-2"></i>Estatísticas do Banco de Dados
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Estrutura Organizacional */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-3">Estrutura Organizacional</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Empresas:</span>
                      <span className="font-medium">{stats?.empresas || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Projetos:</span>
                      <span className="font-medium">{stats?.projetos || 0}</span>
                    </div>
                  </div>
                </div>

                {/* Produtos por categoria */}
                {Object.entries(PRODUTOS).map(([codigo, info]) => {
                  const produtoStats = stats?.[codigo];
                  if (!produtoStats) return null;

                  return (
                    <div key={codigo} className={`bg-${info.cor}-50 p-4 rounded-lg`}>
                      <h3 className={`font-semibold text-${info.cor}-800 mb-3 flex items-center`}>
                        <i className={`${info.icone} mr-2`}></i>
                        {codigo.toUpperCase()}
                      </h3>
                      <div className="space-y-2 text-sm">
                        {Object.entries(produtoStats).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                            <span className="font-medium">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Tab: Empresas */}
        {activeTab === 'empresas' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">
                <i className="fas fa-building mr-2"></i>Gerenciar Empresas
              </h2>
              <button onClick={() => setShowEmpresaForm(!showEmpresaForm)} className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                {showEmpresaForm ? <><i className="fas fa-times mr-2"></i>Cancelar</> : <><i className="fas fa-plus mr-2"></i>Nova Empresa</>}
              </button>
            </div>
            
            {showEmpresaForm && (
              <form onSubmit={handleCreateEmpresa} className="p-4 border rounded-lg bg-gray-50 mb-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input type="text" placeholder="Nome da Empresa" value={empresaForm.nome_empresa} onChange={e => setEmpresaForm({...empresaForm, nome_empresa: e.target.value})} required className="p-2 border rounded-md" />
                  <input type="text" placeholder="Código da Empresa" value={empresaForm.codigo_empresa} onChange={e => setEmpresaForm({...empresaForm, codigo_empresa: e.target.value})} required className="p-2 border rounded-md" />
                  <input type="text" placeholder="Gerência" value={empresaForm.gerencia} onChange={e => setEmpresaForm({...empresaForm, gerencia: e.target.value})} className="p-2 border rounded-md" />
                  <input type="text" placeholder="Endereço" value={empresaForm.endereco} onChange={e => setEmpresaForm({...empresaForm, endereco: e.target.value})} className="p-2 border rounded-md" />
                  <input type="text" placeholder="Telefone" value={empresaForm.telefone} onChange={e => setEmpresaForm({...empresaForm, telefone: e.target.value})} className="p-2 border rounded-md" />
                  <input type="email" placeholder="Email" value={empresaForm.email} onChange={e => setEmpresaForm({...empresaForm, email: e.target.value})} className="p-2 border rounded-md" />
                </div>
                <button type="submit" disabled={actionLoading} className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 disabled:bg-green-300">
                  {actionLoading ? 'Salvando...' : 'Salvar Empresa'}
                </button>
              </form>
            )}

            <div className="space-y-4">
              {empresas.map(empresa => (
                <div key={empresa.id} className="border p-4 rounded-lg flex justify-between items-center">
                  <div>
                    <h3 className="font-bold text-lg">{empresa.nome_empresa} <span className="text-sm font-normal text-gray-500">({empresa.codigo_empresa})</span></h3>
                    <p className="text-sm text-gray-600">{empresa.gerencia}</p>
                    <p className="text-sm text-gray-600">Projetos: {empresa.total_projetos}</p>
                  </div>
                  <button onClick={() => handleDeleteEmpresa(empresa.id, empresa.nome_empresa)} disabled={actionLoading} className="bg-red-600 text-white px-3 py-1 rounded-lg hover:bg-red-700 disabled:bg-red-300">
                    <i className="fas fa-trash-alt"></i>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tab: Projetos */}
        {activeTab === 'projetos' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">
                <i className="fas fa-folder mr-2"></i>Gerenciar Projetos
              </h2>
              <button onClick={() => setShowProjetoForm(!showProjetoForm)} className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                {showProjetoForm ? <><i className="fas fa-times mr-2"></i>Cancelar</> : <><i className="fas fa-plus mr-2"></i>Novo Projeto</>}
              </button>
            </div>
            
            <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Filtrar por Empresa:</label>
                <select value={empresaFilter} onChange={e => setEmpresaFilter(e.target.value)} className="w-full p-2 border-gray-300 rounded-md">
                    <option value="">-- Todas as Empresas --</option>
                    {empresas.map(e => <option key={e.id} value={e.id}>{e.nome_empresa}</option>)}
                </select>
            </div>

            {showProjetoForm && (
              <form onSubmit={handleCreateProjeto} className="p-4 border rounded-lg bg-gray-50 mb-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <select value={projetoForm.empresa_id} onChange={e => setProjetoForm({...projetoForm, empresa_id: e.target.value})} required className="p-2 border rounded-md">
                    <option value="">-- Selecione a Empresa --</option>
                    {empresas.map(e => <option key={e.id} value={e.id}>{e.nome_empresa}</option>)}
                  </select>
                  <input type="text" placeholder="Nome do Projeto" value={projetoForm.nome_projeto} onChange={e => setProjetoForm({...projetoForm, nome_projeto: e.target.value})} required className="p-2 border rounded-md" />
                  <input type="text" placeholder="Código do Projeto" value={projetoForm.codigo_projeto} onChange={e => setProjetoForm({...projetoForm, codigo_projeto: e.target.value})} required className="p-2 border rounded-md" />
                  <input type="text" placeholder="Cliente" value={projetoForm.cliente} onChange={e => setProjetoForm({...projetoForm, cliente: e.target.value})} className="p-2 border rounded-md" />
                  <input type="text" placeholder="Coordenador" value={projetoForm.coordenador} onChange={e => setProjetoForm({...projetoForm, coordenador: e.target.value})} className="p-2 border rounded-md" />
                  <textarea placeholder="Descrição" value={projetoForm.descricao} onChange={e => setProjetoForm({...projetoForm, descricao: e.target.value})} className="p-2 border rounded-md md:col-span-2"></textarea>
                </div>
                <button type="submit" disabled={actionLoading} className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 disabled:bg-green-300">
                  {actionLoading ? 'Salvando...' : 'Salvar Projeto'}
                </button>
              </form>
            )}

            <div className="space-y-4">
              {projetos.map(projeto => (
                <div key={projeto.id} className="border p-4 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-bold text-lg">{projeto.nome_projeto} <span className="text-sm font-normal text-gray-500">({projeto.codigo_projeto})</span></h3>
                      <p className="text-sm text-gray-600">{projeto.empresa.nome_empresa}</p>
                      <p className="text-sm text-gray-600">Status: {projeto.status}</p>
                      <p className="text-sm text-gray-600 mt-2">Dados:
                        {Object.entries(projeto.dados_count).map(([key, value]) => value > 0 && <span key={key} className="ml-2 text-xs bg-gray-200 px-2 py-1 rounded-full">{key.toUpperCase()}: {value}</span>)}
                      </p>
                    </div>
                    <button onClick={() => handleDeleteProjeto(projeto.id, projeto.nome_projeto)} disabled={actionLoading} className="bg-red-600 text-white px-3 py-1 rounded-lg hover:bg-red-700 disabled:bg-red-300">
                      <i className="fas fa-trash-alt"></i>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Tab: Produtos */}
        {activeTab === 'produtos' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">
              <i className="fas fa-cubes mr-2"></i>Limpeza de Dados por Produto
            </h2>
            <p className="text-gray-600 mb-6">
              Esta ação removerá todos os dados associados a um produto específico (pontos, contagens, etc.), mas não deletará os projetos ou empresas.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(PRODUTOS).map(([codigo, info]) => (
                <div key={codigo} className={`border p-4 rounded-lg flex justify-between items-center bg-${info.cor}-50 border-${info.cor}-200`}>
                  <div>
                    <h3 className={`font-semibold text-lg text-${info.cor}-800 flex items-center`}>
                      <i className={`${info.icone} mr-2`}></i>
                      {info.nome}
                    </h3>
                  </div>
                  <button
                    onClick={() => handleCleanProduct(codigo)}
                    disabled={actionLoading}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:bg-red-300 transition-colors"
                  >
                    <i className="fas fa-trash-alt mr-2"></i>Limpar
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tab: Zona de Perigo */}
        {activeTab === 'danger' && (
          <div className="bg-red-50 border-2 border-red-500 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-red-800 mb-4">
              <i className="fas fa-exclamation-triangle mr-2"></i>Zona de Perigo
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg text-red-700">Resetar Banco de Dados</h3>
                <p className="text-red-600 mt-1">
                  Esta ação irá apagar <strong>TODOS</strong> os dados do banco de dados, incluindo empresas, projetos, usuários e todos os dados de produtos. A estrutura de tabelas será recriada, e o sistema voltará ao seu estado inicial.
                </p>
                <p className="text-red-600 font-bold mt-2">
                  Esta ação é irreversível.
                </p>
              </div>
              <button
                onClick={handleResetDatabase}
                disabled={actionLoading}
                className="bg-red-700 text-white px-6 py-3 rounded-lg hover:bg-red-800 disabled:bg-red-400 transition-colors font-bold"
              >
                <i className="fas fa-bomb mr-2"></i>
                RESETAR COMPLETAMENTE O BANCO DE DADOS
              </button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}