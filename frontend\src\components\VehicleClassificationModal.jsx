// frontend/src/components/VehicleClassificationModal.jsx

import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// **NOVO: Paleta de cores para sugestão automática**
const colorPalette = [
  '#3498db', '#e74c3c', '#2ecc71', '#f1c40f', '#9b59b6', '#34495e',
  '#1abc9c', '#e67e22', '#16a085', '#d35400', '#2980b9', '#c0392b'
];

const getSuggestedColor = (index) => colorPalette[index % colorPalette.length];


export default function VehicleClassificationModal({ 
  isOpen, 
  onClose, 
  analysisData, 
  onConfirmClassification,
  loading 
}) {
  const [classificacoes, setClassificacoes] = useState([]);
  const [step, setStep] = useState(1); // 1: Revisão, 2: Classificação, 3: Confirmação

  useEffect(() => {
    if (analysisData && isOpen) {
      // Inicializa as classificações com base na análise
      const inicialClassificacoes = analysisData.tipos_para_classificar.map((tipo, index) => ({ // Adicionado index
        nome_excel: tipo.nome_excel,
        codigo: tipo.codigo_sugerido,
        nome: tipo.nome_sugerido,
        categoria_id: tipo.categoria_sugerida_id,
        equivalente_auto: tipo.equivalente_auto_sugerido,
        // **ALTERADO: Usa a função para obter uma cor diferente para cada tipo**
        cor_padrao: getSuggestedColor(index), 
        descricao: '',
        grupos_ids: [],
        criar_novo: true
      }));
      setClassificacoes(inicialClassificacoes);
      setStep(1);
    }
  }, [analysisData, isOpen]);

  const handleClassificacaoChange = (index, field, value) => {
    setClassificacoes(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    );
  };

  const handleGruposChange = (index, grupoId, checked) => {
    setClassificacoes(prev => 
      prev.map((item, i) => {
        if (i === index) {
          const grupos_ids = checked 
            ? [...item.grupos_ids, grupoId]
            : item.grupos_ids.filter(id => id !== grupoId);
          return { ...item, grupos_ids };
        }
        return item;
      })
    );
  };

  const handleConfirm = () => {
    onConfirmClassification(classificacoes);
  };

  const getCategoriaNome = (categoriaId) => {
    return analysisData?.categorias_disponiveis?.find(c => c.id === categoriaId)?.nome || 'N/A';
  };

  if (!isOpen || !analysisData) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col shadow-2xl">
        
        {/* Header */}
        <div className="bg-green-600 text-white p-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">Classificação de Tipos de Veículos</h2>
              <p className="text-green-100 mt-1">
                {step === 1 && "Revisão dos tipos encontrados"}
                {step === 2 && "Classificação dos novos tipos"}
                {step === 3 && "Confirmação da importação"}
              </p>
            </div>
            <button 
              onClick={onClose}
              className="text-white hover:bg-green-700 p-2 rounded-lg transition-colors"
              disabled={loading}
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
          
          {/* Progress Steps */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm">
              <div className={`flex items-center ${step >= 1 ? 'text-white' : 'text-green-300'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                  step >= 1 ? 'bg-white text-green-600' : 'bg-green-500'
                }`}>
                  {step > 1 ? <i className="fas fa-check"></i> : '1'}
                </div>
                Revisão
              </div>
              <div className={`flex items-center ${step >= 2 ? 'text-white' : 'text-green-300'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                  step >= 2 ? 'bg-white text-green-600' : 'bg-green-500'
                }`}>
                  {step > 2 ? <i className="fas fa-check"></i> : '2'}
                </div>
                Classificação
              </div>
              <div className={`flex items-center ${step >= 3 ? 'text-white' : 'text-green-300'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                  step >= 3 ? 'bg-white text-green-600' : 'bg-green-500'
                }`}>
                  {step > 3 ? <i className="fas fa-check"></i> : '3'}
                </div>
                Confirmação
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          
          {/* Step 1: Revisão */}
          {step === 1 && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-800 mb-2">
                  <i className="fas fa-info-circle mr-2"></i>
                  Estatísticas do Arquivo
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {analysisData.estatisticas.total_registros}
                    </div>
                    <div className="text-gray-600">Total de Registros</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {analysisData.estatisticas.tipos_existentes}
                    </div>
                    <div className="text-gray-600">Tipos Existentes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {analysisData.estatisticas.tipos_novos}
                    </div>
                    <div className="text-gray-600">Tipos Novos</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {analysisData.estatisticas.total_tipos_unicos}
                    </div>
                    <div className="text-gray-600">Tipos Únicos</div>
                  </div>
                </div>
              </div>

              {/* Tipos Existentes */}
              {analysisData.tipos_ja_existentes.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    <i className="fas fa-check-circle text-green-600 mr-2"></i>
                    Tipos Já Existentes ({analysisData.tipos_ja_existentes.length})
                  </h3>
                  <div className="bg-green-50 border border-green-200 rounded-lg overflow-hidden">
                    <div className="max-h-40 overflow-y-auto">
                      {analysisData.tipos_ja_existentes.map((tipo, index) => (
                        <div key={index} className="flex justify-between items-center p-3 border-b border-green-100 last:border-b-0">
                          <div>
                            <span className="font-medium text-gray-800">{tipo.nome_excel}</span>
                            <span className="text-sm text-gray-600 ml-2">→ {tipo.nome_sistema}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">
                              {tipo.categoria_nome}
                            </span>
                            <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">
                              {tipo.status === 'existente_global' ? 'Global' : 'Projeto'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Tipos Para Classificar */}
              {analysisData.tipos_para_classificar.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    <i className="fas fa-exclamation-triangle text-orange-600 mr-2"></i>
                    Tipos Que Precisam Ser Classificados ({analysisData.tipos_para_classificar.length})
                  </h3>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg overflow-hidden">
                    <div className="max-h-60 overflow-y-auto">
                      {analysisData.tipos_para_classificar.map((tipo, index) => (
                        <div key={index} className="flex justify-between items-center p-3 border-b border-orange-100 last:border-b-0">
                          <div>
                            <span className="font-medium text-gray-800">{tipo.nome_excel}</span>
                            <span className="text-sm text-gray-600 ml-2">→ {tipo.codigo_sugerido}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">
                              {tipo.categoria_sugerida_nome}
                            </span>
                            <span className="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded">
                              Equiv: {tipo.equivalente_auto_sugerido}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Classificação */}
          {step === 2 && (
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <p className="text-yellow-800">
                  <i className="fas fa-lightbulb mr-2"></i>
                  <strong>Instruções:</strong> Revise e ajuste as classificações sugeridas para cada tipo de veículo. 
                  As sugestões foram feitas automaticamente baseadas no nome do tipo.
                </p>
              </div>

              {classificacoes.map((classificacao, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    
                    {/* Informações Básicas */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-800 border-b pb-2">
                        <i className="fas fa-car mr-2 text-blue-600"></i>
                        {classificacao.nome_excel}
                      </h4>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Código do Sistema</label>
                        <input type="text" value={classificacao.codigo} onChange={(e) => handleClassificacaoChange(index, 'codigo', e.target.value.toUpperCase())} className="w-full p-2 border border-gray-300 rounded-md text-sm" placeholder="Ex: AUTO, CAM_LEVE"/>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Nome Descritivo</label>
                        <input type="text" value={classificacao.nome} onChange={(e) => handleClassificacaoChange(index, 'nome', e.target.value)} className="w-full p-2 border border-gray-300 rounded-md text-sm" placeholder="Ex: Automóvel de Passeio"/>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Categoria</label>
                        <select value={classificacao.categoria_id} onChange={(e) => handleClassificacaoChange(index, 'categoria_id', parseInt(e.target.value))} className="w-full p-2 border border-gray-300 rounded-md text-sm">
                          {analysisData.categorias_disponiveis.map(cat => (<option key={cat.id} value={cat.id}>{cat.nome}</option>))}
                        </select>
                      </div>
                    </div>

                    {/* Configurações Avançadas */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-700 border-b pb-2"><i className="fas fa-cogs mr-2 text-gray-600"></i>Configurações Avançadas</h5>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Equivalente Auto</label>
                          <input type="number" step="0.1" min="0" max="10" value={classificacao.equivalente_auto} onChange={(e) => handleClassificacaoChange(index, 'equivalente_auto', parseFloat(e.target.value))} className="w-full p-2 border border-gray-300 rounded-md text-sm"/>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Cor Padrão</label>
                          <input type="color" value={classificacao.cor_padrao} onChange={(e) => handleClassificacaoChange(index, 'cor_padrao', e.target.value)} className="w-full h-10 border border-gray-300 rounded-md"/>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Descrição (Opcional)</label>
                        <textarea value={classificacao.descricao} onChange={(e) => handleClassificacaoChange(index, 'descricao', e.target.value)} className="w-full p-2 border border-gray-300 rounded-md text-sm h-16 resize-none" placeholder="Descrição detalhada do tipo de veículo..."/>
                      </div>

                      {analysisData.grupos_disponiveis.length > 0 && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Associar aos Grupos</label>
                          <div className="space-y-1 max-h-20 overflow-y-auto border border-gray-200 rounded p-2">
                            {analysisData.grupos_disponiveis.map(grupo => (
                              <label key={grupo.id} className="flex items-center text-sm">
                                <input type="checkbox" checked={classificacao.grupos_ids.includes(grupo.id)} onChange={(e) => handleGruposChange(index, grupo.id, e.target.checked)} className="mr-2"/>
                                <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: grupo.cor_grupo }}/>
                                {grupo.nome}
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Step 3: Confirmação */}
          {step === 3 && (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-semibold text-green-800 mb-2"><i className="fas fa-check-circle mr-2"></i>Resumo da Classificação</h3>
                <p className="text-green-700 text-sm">Revise as classificações abaixo. Ao confirmar, os novos tipos serão criados e a importação será executada.</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {classificacoes.map((classificacao, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-800">{classificacao.nome_excel}</h4>
                      <div className="w-4 h-4 rounded-full" style={{ backgroundColor: classificacao.cor_padrao }}/>
                    </div>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div><strong>Código:</strong> {classificacao.codigo}</div>
                      <div><strong>Nome:</strong> {classificacao.nome}</div>
                      <div><strong>Categoria:</strong> {getCategoriaNome(classificacao.categoria_id)}</div>
                      <div><strong>Equivalente:</strong> {classificacao.equivalente_auto}</div>
                      {classificacao.grupos_ids.length > 0 && (<div><strong>Grupos:</strong> {classificacao.grupos_ids.length} grupo(s)</div>)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {step === 1 && `${analysisData.tipos_para_classificar.length} tipos precisam ser classificados`}
              {step === 2 && `Classificando ${classificacoes.length} tipos de veículos`}
              {step === 3 && 'Pronto para importar os dados'}
            </div>
            <div className="flex space-x-3">
              {step > 1 && (<button onClick={() => setStep(step - 1)} disabled={loading} className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:bg-gray-300 transition-colors"><i className="fas fa-arrow-left mr-2"></i>Voltar</button>)}
              {step < 3 ? (<button onClick={() => setStep(step + 1)} disabled={loading || (step === 2 && classificacoes.some(c => !c.codigo || !c.nome))} className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 transition-colors">Próximo<i className="fas fa-arrow-right ml-2"></i></button>) : (<button onClick={handleConfirm} disabled={loading} className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 transition-colors">{loading ? (<><i className="fas fa-spinner fa-spin mr-2"></i>Importando...</>) : (<><i className="fas fa-download mr-2"></i>Confirmar e Importar</>)}</button>)}
            </div>
          </div>
        </div>
      </div>
    </div>
    )
}

VehicleClassificationModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    analysisData: PropTypes.object,
    onConfirmClassification: PropTypes.func.isRequired,
    loading: PropTypes.bool.isRequired,
};