// frontend/src/components/ProductMenu.jsx - ATUALIZADO COM VIA E ADMIN

import { Link, useLocation } from 'react-router-dom';

export default function ProductMenu() {
  const location = useLocation();

  const produtos = [
    {
      path: '/',
      nome: 'PED',
      icone: 'fas fa-bus',
      cor: 'blue'
    },
    {
      path: '/ccv',
      nome: 'CCV',
      icone: 'fas fa-car',
      cor: 'green'
    },
    {
      path: '/via',
      nome: 'VIA',
      icone: 'fas fa-road',
      cor: 'indigo'
    },
    {
      path: '/importar',
      nome: 'Importar',
      icone: 'fas fa-upload',
      cor: 'purple'
    },
    // BOTÃO DE ADMIN ADICIONADO AQUI
    {
      path: '/admin',
      nome: 'Admin',
      icone: 'fas fa-database',
      cor: 'gray'
    }
  ];

  const colorClasses = {
    blue: {
      border: 'border-blue-500',
      text: 'text-blue-600'
    },
    green: {
      border: 'border-green-500',
      text: 'text-green-600'
    },
    indigo: {
      border: 'border-indigo-500',
      text: 'text-indigo-600'
    },
    purple: {
      border: 'border-purple-500',
      text: 'text-purple-600'
    },
    // COR PARA O BOTÃO DE ADMIN
    gray: {
      border: 'border-gray-500',
      text: 'text-gray-600'
    }
  };

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4">
        <nav className="flex space-x-6">
          {produtos.map(produto => {
            const isActive = location.pathname.startsWith(produto.path) && produto.path !== '/' || location.pathname === produto.path;
            const activeColor = colorClasses[produto.cor] || colorClasses.blue;

            return (
              <Link
                key={produto.path}
                to={produto.path}
                className={`flex items-center px-3 py-2 text-sm font-medium border-b-4 transition-colors duration-200 ${
                  isActive 
                    ? `${activeColor.border} ${activeColor.text}` 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={`${produto.icone} text-lg mr-2 w-5 text-center`}></i>
                <span className="font-semibold">{produto.nome}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
}