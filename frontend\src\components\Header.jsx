// frontend/src/components/Header.jsx
export default function Header({ onMenuToggle }) {
  return (
    <header className="bg-[#111827] text-white h-16 flex items-center px-6 shadow-lg z-30 flex-shrink-0 justify-between relative">
      <div className="flex items-center">
        <button type="button" className="menu-toggle text-gray-300 hover:text-white mr-4" onClick={onMenuToggle}>
          <i className="fas fa-bars text-xl"></i>
        </button>
        {/* As imagens devem estar na pasta 'frontend/public/images/' */}
        <img src="/images/logo1.png" alt="Logótipo 1" className="h-9" onError={(e) => (e.currentTarget.style.display = 'none')} />
      </div>
      <h1 className="text-xl font-semibold text-white absolute left-1/2 -translate-x-1/2 whitespace-nowrap">
        Dashboard de Performance dos Pontos de Embarque e Desembarque
      </h1>
      <div className="flex items-center">
        <img src="/images/logo2.svg" alt="Logótipo 2" className="h-10" onError={(e) => (e.currentTarget.style.display = 'none')} />
      </div>
    </header>
  );
}