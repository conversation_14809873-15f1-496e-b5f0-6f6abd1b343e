// frontend/src/api/ccvConfigService.js

import axios from 'axios';

const API = axios.create({ 
  baseURL: API_BASE_URL,
  timeout: 15000
});

API.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(error.response?.data || error)
);

/**
 * Busca os tipos de veículos (globais + específicos) para um projeto.
 */
export async function fetchTiposVeiculosPorProjeto(projetoId) {
  try {
    const response = await API.get(`/api/ccv/tipos-veiculos/${projetoId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar tipos de veículos:", error);
    throw error;
  }
}

/**
 * Busca os grupos de veículos configurados para um projeto.
 */
export async function fetchGruposVeiculos(projetoId) {
  try {
    const response = await API.get(`/api/ccv/grupos-veiculos/${projetoId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar grupos de veículos:", error);
    throw error;
  }
}

/**
 * <PERSON> (cria ou atualiza) um grupo de veículos.
 */
export async function saveGrupoVeiculo(projetoId, grupoData) {
  try {
    const response = await API.post(`/api/ccv/grupos-veiculos/${projetoId}`, grupoData);
    return response.data;
  } catch (error) {
    console.error("Erro ao salvar grupo de veículo:", error);
    throw error;
  }
}

/**
 * Deleta um grupo de veículos.
 */
export async function deleteGrupoVeiculo(grupoId) {
  try {
    const response = await API.delete(`/api/ccv/grupos-veiculos/grupo/${grupoId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao deletar grupo de veículo:", error);
    throw error;
  }
}

/**
 * Busca todas as categorias de veículos disponíveis.
 */
export async function fetchCategorias() {
  try {
    const response = await API.get('/api/ccv/categorias-veiculos');
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar categorias:", error);
    throw error;
  }
}

/**
 * Atualiza um tipo de veículo.
 */
export async function updateTipoVeiculo(tipoId, updateData) {
  try {
    const response = await API.put(`/api/ccv/tipos-veiculos/${tipoId}`, updateData);
    return response.data;
  } catch (error) {
    console.error("Erro ao atualizar tipo de veículo:", error);
    throw error;
  }
}

