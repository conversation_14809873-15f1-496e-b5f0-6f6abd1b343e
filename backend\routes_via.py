# backend/routes_via.py - ARQUIVO COMPLETO E CORRIGIDO

from flask import Blueprint, jsonify, request, current_app
from app import db
from models import Projeto, VIAProjeto, VIANo, VIATrecho, VIACenario, VIADadosDesempenho
import json
import traceback
from datetime import datetime

### ADICIONADO: Importação da biblioteca para conversão de coordenadas ###
from pyproj import Transformer, CRS

via_bp = Blueprint('via', __name__, url_prefix='/api/via')


### ADICIONADO: Função auxiliar para converter geometrias UTM para WGS 84 ###
def convert_geometry_coords(geometry, transformer):
    """
    Converte as coordenadas de uma geometria GeoJSON usando o transformer do pyproj.
    Suporta Point, LineString e MultiLineString.
    """
    if not geometry or 'type' not in geometry or 'coordinates' not in geometry:
        return geometry

    geom_type = geometry['type']
    coords = geometry['coordinates']

    try:
        if geom_type == 'Point':
            x, y, *z = coords
            lon, lat = transformer.transform(x, y)
            geometry['coordinates'] = [lon, lat] + z
        
        elif geom_type == 'LineString':
            converted_coords = []
            for x, y, *z in coords:
                lon, lat = transformer.transform(x, y)
                converted_coords.append([lon, lat] + z)
            geometry['coordinates'] = converted_coords

        elif geom_type == 'MultiLineString':
            multi_converted_coords = []
            for line in coords:
                converted_line = []
                for x, y, *z in line:
                    lon, lat = transformer.transform(x, y)
                    converted_line.append([lon, lat] + z)
                multi_converted_coords.append(converted_line)
            geometry['coordinates'] = multi_converted_coords
            
    except Exception as e:
        current_app.logger.error(f"Erro ao converter coordenadas do tipo {geom_type}: {e}")
        # Retorna a geometria original em caso de erro para não quebrar a importação
        return geometry

    return geometry


@via_bp.route('/projetos', methods=['GET'])
def get_projetos_via():
    """Lista projetos VIA com estatísticas"""
    try:
        empresa_id = request.args.get('empresa_id', type=int)
        
        query = VIAProjeto.query.join(Projeto)
        if empresa_id:
            query = query.filter(Projeto.empresa_id == empresa_id)
        
        projetos_via = query.all()
        
        resultado = []
        for pv in projetos_via:
            total_nos = VIANo.query.filter_by(projeto_via_id=pv.id).count()
            total_trechos = VIATrecho.query.filter_by(projeto_via_id=pv.id).count()
            total_cenarios = VIACenario.query.filter_by(projeto_via_id=pv.id).count()
            
            resultado.append({
                'id': pv.id,
                'projeto_id': pv.projeto_id,
                'nome_rede': pv.nome_rede,
                'descricao': pv.descricao,
                'fonte_dados': pv.fonte_dados,
                'versao_aimsun': pv.versao_aimsun,
                'data_criacao': pv.data_criacao.isoformat(),
                'ativo': pv.ativo,
                'indicadores_config': pv.indicadores_config or {},
                'estatisticas': {
                    'total_nos': total_nos,
                    'total_trechos': total_trechos,
                    'total_cenarios': total_cenarios
                },
                'projeto': {
                    'codigo': pv.projeto.codigo_projeto,
                    'nome': pv.projeto.nome_projeto
                }
            })
        
        return jsonify({
            'projetos_via': resultado,
            'total': len(resultado)
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro ao buscar projetos VIA: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@via_bp.route('/upload-geojson', methods=['POST'])
def upload_geojson_via():
    """Upload e análise de rede GeoJSON do Aimsun"""
    try:
        if 'projeto_id' not in request.form:
            return jsonify({'error': 'ID do projeto é obrigatório'}), 400
        if 'geojson_file' not in request.files:
            return jsonify({'error': 'Arquivo GeoJSON é obrigatório'}), 400
        
        projeto_id = int(request.form['projeto_id'])
        nome_rede = request.form.get('nome_rede', 'Rede Aimsun')
        geojson_file = request.files['geojson_file']
        
        projeto = Projeto.query.get(projeto_id)
        if not projeto:
            return jsonify({'error': 'Projeto não encontrado'}), 404
        
        try:
            geojson_data = json.load(geojson_file.stream)
        except json.JSONDecodeError:
            return jsonify({'error': 'Arquivo GeoJSON inválido'}), 400
        
        if geojson_data.get('type') != 'FeatureCollection':
            return jsonify({'error': 'GeoJSON deve ser uma FeatureCollection'}), 400
        
        features = geojson_data.get('features', [])
        if not features:
            return jsonify({'error': 'GeoJSON não contém features'}), 400
        
        atributos_encontrados = set()
        tipos_geometria = set()
        nos_features = []
        trechos_features = []
        
        for feature in features:
            properties = feature.get('properties', {})
            geometry = feature.get('geometry', {})
            
            atributos_encontrados.update(properties.keys())
            
            geom_type = geometry.get('type')
            tipos_geometria.add(geom_type)
            
            if geom_type == 'Point':
                nos_features.append(feature)
            elif geom_type in ['LineString', 'MultiLineString']:
                trechos_features.append(feature)
        
        atributos_ignorados = {'id', 'fid', 'objectid', 'shape_length', 'shape_area'}
        atributos_uteis = sorted([attr for attr in atributos_encontrados 
                                if attr.lower() not in atributos_ignorados])
        
        atributos_categorizados = categorizar_atributos(features, atributos_uteis)
        
        return jsonify({
            'message': 'Análise do GeoJSON concluída',
            'estatisticas': {
                'total_features': len(features),
                'total_nos': len(nos_features),
                'total_trechos': len(trechos_features),
                'tipos_geometria': list(tipos_geometria)
            },
            'atributos_encontrados': atributos_categorizados,
            'preview': {
                'primeiro_no': nos_features[0].get('properties', {}) if nos_features else None,
                'primeiro_trecho': trechos_features[0].get('properties', {}) if trechos_features else None
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro no upload GeoJSON VIA: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@via_bp.route('/confirmar-importacao', methods=['POST'])
def confirmar_importacao_via():
    """Confirma e executa a importação com indicadores selecionados e CONVERSÃO DE COORDENADAS"""
    try:
        data = request.get_json()
        
        projeto_id = data.get('projeto_id')
        nome_rede = data.get('nome_rede')
        indicadores_config = data.get('indicadores_config', {})
        geojson_data = data.get('geojson_data')
        versao_aimsun = data.get('versao_aimsun', 'Não especificada')
        overwrite = data.get('overwrite', False)
        
        if not all([projeto_id, nome_rede, geojson_data]):
            return jsonify({'error': 'Dados obrigatórios ausentes'}), 400
        
        projeto_via_existente = VIAProjeto.query.filter_by(projeto_id=projeto_id).first()
        
        if projeto_via_existente and not overwrite:
            return jsonify({
                'error': 'Projeto VIA já existe',
                'require_confirmation': True,
                'existing_project': {
                    'nome_rede': projeto_via_existente.nome_rede,
                    'data_criacao': projeto_via_existente.data_criacao.isoformat()
                }
            }), 409
        
        if projeto_via_existente and overwrite:
            VIADadosDesempenho.query.join(VIATrecho).filter(
                VIATrecho.projeto_via_id == projeto_via_existente.id
            ).delete(synchronize_session=False)
            
            VIANo.query.filter_by(projeto_via_id=projeto_via_existente.id).delete()
            VIATrecho.query.filter_by(projeto_via_id=projeto_via_existente.id).delete()
            VIACenario.query.filter_by(projeto_via_id=projeto_via_existente.id).delete()
            
            projeto_via = projeto_via_existente
            projeto_via.nome_rede = nome_rede
            projeto_via.indicadores_config = indicadores_config
            projeto_via.versao_aimsun = versao_aimsun
            projeto_via.data_atualizacao = datetime.utcnow()
        else:
            projeto_via = VIAProjeto(
                projeto_id=projeto_id,
                nome_rede=nome_rede,
                indicadores_config=indicadores_config,
                versao_aimsun=versao_aimsun
            )
            db.session.add(projeto_via)
            db.session.flush()
        
        features = geojson_data.get('features', [])
        nos_criados = 0
        trechos_criados = 0
        nos_map = {}
        
        ### ADICIONADO: Criar o transformador para converter de UTM 23S para WGS 84 ###
        # EPSG:32723 -> WGS 84 / UTM zone 23S (comum para Minas Gerais)
        # EPSG:4326  -> WGS 84 (Latitude/Longitude)
        transformer = Transformer.from_crs("EPSG:32723", "EPSG:4326", always_xy=True)

        # Primeiro, cria os nós
        for feature in features:
            properties = feature.get('properties', {})
            geometry = feature.get('geometry', {})
            
            if geometry.get('type') == 'Point':
                ### MODIFICADO: Converte as coordenadas antes de usar ###
                geometry = convert_geometry_coords(geometry, transformer)
                coordinates = geometry.get('coordinates', [])
                
                if len(coordinates) >= 2:
                    no = VIANo(
                        projeto_via_id=projeto_via.id,
                        aimsun_id=properties.get('id') or properties.get('Id') or properties.get('ID'),
                        nome=properties.get('name') or properties.get('Name') or properties.get('nome'),
                        tipo=properties.get('type') or properties.get('Type') or 'node',
                        longitude=coordinates[0], # Agora é Longitude
                        latitude=coordinates[1],  # Agora é Latitude
                        altitude=coordinates[2] if len(coordinates) > 2 else None,
                        atributos_originais=properties
                    )
                    db.session.add(no)
                    db.session.flush()
                    
                    if no.aimsun_id:
                        nos_map[no.aimsun_id] = no.id
                    
                    nos_criados += 1
        
        # Depois, cria os trechos
        for feature in features:
            properties = feature.get('properties', {})
            geometry = feature.get('geometry', {})
            
            if geometry.get('type') in ['LineString', 'MultiLineString']:
                ### MODIFICADO: Converte as coordenadas antes de usar ###
                geometry = convert_geometry_coords(geometry, transformer)
                
                trecho = VIATrecho(
                    projeto_via_id=projeto_via.id,
                    aimsun_id=properties.get('id') or properties.get('Id') or properties.get('ID'),
                    nome=properties.get('name') or properties.get('Name') or properties.get('nome'),
                    tipo=properties.get('type') or properties.get('Type') or 'section',
                    geometria=geometry, # Salva a geometria já CONVERTIDA
                    comprimento=properties.get('length') or properties.get('Length') or properties.get('comprimento'),
                    largura=properties.get('width') or properties.get('Width') or properties.get('largura'),
                    numero_faixas=properties.get('lanes') or properties.get('Lanes') or properties.get('faixas'),
                    velocidade_limite=properties.get('speed_limit') or properties.get('SpeedLimit') or properties.get('velocidade'),
                    capacidade=properties.get('capacity') or properties.get('Capacity') or properties.get('capacidade'),
                    atributos_originais=properties
                )
                
                origem_id = properties.get('from_node') or properties.get('FromNode') or properties.get('origem')
                destino_id = properties.get('to_node') or properties.get('ToNode') or properties.get('destino')
                
                if origem_id and origem_id in nos_map:
                    trecho.no_origem_id = nos_map[origem_id]
                if destino_id and destino_id in nos_map:
                    trecho.no_destino_id = nos_map[destino_id]
                
                db.session.add(trecho)
                trechos_criados += 1
        
        cenario_padrao = VIACenario(
            projeto_via_id=projeto_via.id,
            nome_cenario='Cenário Base',
            descricao='Cenário base importado do Aimsun',
            tipo_cenario='atual',
            ano_referencia=datetime.now().year
        )
        db.session.add(cenario_padrao)
        
        db.session.commit()
        
        return jsonify({
            'message': 'Importação concluída com sucesso! As coordenadas foram convertidas para Lat/Lon.',
            'projeto_via_id': projeto_via.id,
            'estatisticas': {
                'nos_criados': nos_criados,
                'trechos_criados': trechos_criados,
                'indicadores_configurados': len(indicadores_config)
            },
            'indicadores_config': indicadores_config
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erro na confirmação de importação VIA: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@via_bp.route('/projeto/<int:projeto_via_id>/dados', methods=['GET'])
def get_dados_via(projeto_via_id):
    """Retorna dados da rede VIA para visualização"""
    try:
        projeto_via = VIAProjeto.query.get(projeto_via_id)
        if not projeto_via:
            return jsonify({'error': 'Projeto VIA não encontrado'}), 404
        
        trechos = VIATrecho.query.filter_by(projeto_via_id=projeto_via_id).all()
        nos = VIANo.query.filter_by(projeto_via_id=projeto_via_id).all()
        
        trechos_data = {}
        for trecho in trechos:
            trecho_info = {
                'id': trecho.id,
                'aimsun_id': trecho.aimsun_id,
                'nome': trecho.nome,
                'tipo': trecho.tipo,
                'geometria': trecho.geometria,
                'comprimento': float(trecho.comprimento) if trecho.comprimento else None,
                'numero_faixas': trecho.numero_faixas,
                'velocidade_limite': float(trecho.velocidade_limite) if trecho.velocidade_limite else None,
                'capacidade': trecho.capacidade,
                'atributos_originais': trecho.atributos_originais
            }
            
            dados_desempenho = VIADadosDesempenho.query.filter_by(trecho_id=trecho.id).all()
            if dados_desempenho:
                trecho_info['dados_desempenho'] = [
                    {
                        'cenario_id': dp.cenario_id,
                        'data_coleta': dp.data_coleta.isoformat() if dp.data_coleta else None,
                        'indicadores': dp.indicadores
                    } for dp in dados_desempenho
                ]
            
            trechos_data[trecho.aimsun_id or str(trecho.id)] = trecho_info
        
        nos_data = {}
        for no in nos:
            nos_data[no.aimsun_id or str(no.id)] = {
                'id': no.id,
                'aimsun_id': no.aimsun_id,
                'nome': no.nome,
                'tipo': no.tipo,
                'latitude': float(no.latitude) if no.latitude else None,
                'longitude': float(no.longitude) if no.longitude else None,
                'atributos_originais': no.atributos_originais
            }
        
        return jsonify({
            'projeto_via': {
                'id': projeto_via.id,
                'nome_rede': projeto_via.nome_rede,
                'indicadores_config': projeto_via.indicadores_config or {}
            },
            'trechos': trechos_data,
            'nos': nos_data,
            'estatisticas': {
                'total_trechos': len(trechos),
                'total_nos': len(nos)
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Erro ao buscar dados VIA: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

def categorizar_atributos(features, atributos):
    """Categoriza atributos por tipo de dado"""
    if not features:
        return []
    
    categoria_map = {}
    
    for attr in atributos:
        valores = []
        for feature in features[:100]:
            valor = feature.get('properties', {}).get(attr)
            if valor is not None:
                valores.append(valor)
        
        if not valores:
            continue
        
        tipo = 'texto'
        unidade = ''
        descricao = ''
        
        valores_numericos = []
        for v in valores:
            try:
                if isinstance(v, (int, float)):
                    valores_numericos.append(float(v))
                elif isinstance(v, str) and v.replace('.', '').replace('-', '').isdigit():
                    valores_numericos.append(float(v))
            except:
                pass
        
        if len(valores_numericos) > len(valores) * 0.7:
            tipo = 'numerico'
            min_val = min(valores_numericos)
            max_val = max(valores_numericos)
            
            attr_lower = attr.lower()
            if 'speed' in attr_lower or 'velocidade' in attr_lower:
                unidade = 'km/h'
                descricao = 'Velocidade'
            elif 'length' in attr_lower or 'comprimento' in attr_lower or 'distance' in attr_lower:
                unidade = 'm'
                descricao = 'Comprimento/Distância'
            elif 'capacity' in attr_lower or 'capacidade' in attr_lower:
                unidade = 'veic/h'
                descricao = 'Capacidade'
            elif 'lanes' in attr_lower or 'faixas' in attr_lower:
                unidade = 'faixas'
                descricao = 'Número de faixas'
            elif 'flow' in attr_lower or 'fluxo' in attr_lower:
                unidade = 'veic/h'
                descricao = 'Fluxo de veículos'
            elif 'density' in attr_lower or 'densidade' in attr_lower:
                unidade = 'veic/km'
                descricao = 'Densidade'
            elif 'time' in attr_lower or 'tempo' in attr_lower:
                unidade = 's'
                descricao = 'Tempo'
            elif 'delay' in attr_lower or 'atraso' in attr_lower:
                unidade = 's'
                descricao = 'Atraso'
            
            categoria_map[attr] = {
                'nome': attr,
                'tipo': tipo,
                'unidade': unidade,
                'descricao': descricao or f'Atributo numérico: {attr}',
                'estatisticas': {
                    'min': round(min_val, 2),
                    'max': round(max_val, 2),
                    'valores_unicos': len(set(valores_numericos))
                },
                'recomendado': len(unidade) > 0
            }
        else:
            valores_unicos = list(set(str(v) for v in valores if v is not None))
            categoria_map[attr] = {
                'nome': attr,
                'tipo': 'categoria' if len(valores_unicos) <= 20 else 'texto',
                'unidade': '',
                'descricao': f'Atributo categórico: {attr}',
                'estatisticas': {
                    'valores_unicos': len(valores_unicos),
                    'exemplos': valores_unicos[:5]
                },
                'recomendado': len(valores_unicos) <= 10 and len(valores_unicos) > 1
            }
    
    return list(categoria_map.values())