/*
 * Este ficheiro contém apenas estilos customizados para componentes de terceiros
 * que não são controlados pelo Tailwind CSS, como a biblioteca noUiSlider.
 */

/* Estilos para o contentor do slider */
#slider-container {
    padding-top: 8px;
}

#slider-values {
    margin-top: 8px;
}

/* Estilos para a barra do slider (sobrescreve o padrão da biblioteca) */
#time-slider.noUi-target,
#icon-size-slider.noUi-target  {
    height: 4px;
    background: #e5e7eb; /* bg-gray-200 */
    border: none;
    box-shadow: none;
}

/* Estilo para a barra de conexão (o trecho colorido entre os pontos) */
#time-slider .noUi-connect,
#icon-size-slider .noUi-connect {
    background: #374151; /* bg-gray-700 */
}

/* Estilo para os pontos do slider (handles) */
#time-slider .noUi-handle,
#icon-size-slider .noUi-handle {
    height: 16px;
    width: 16px;
    top: -6px;
    right: -8px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #374151; /* bg-gray-700 */
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    cursor: grab;
}

/* Remove as barras verticais de dentro do ícone do slider */
#time-slider .noUi-handle::before,
#time-slider .noUi-handle::after,
#icon-size-slider .noUi-handle::before,
#icon-size-slider .noUi-handle::after  {
    display: none;
}

/* Garante que o mapa ocupe todo o espaço disponível */
#map {
    width: 100%;
    height: 100%;
}

/* --- NOVA REGRA PARA O MENU RECOLHIDO --- */
/* Esta regra é usada pelo JavaScript para alterar a largura da sidebar */
#sidebar.collapsed {
    width: 0;
    padding-left: 0;
    padding-right: 0;
    overflow: hidden;
}

/* === ESTILOS CUSTOMIZADOS PARA MAPAS E LEGENDAS === */

/* Estilos para ícones de mapa customizados */
.leaflet-pie-icon,
.leaflet-pin-icon {
    background: none !important;
    border: none !important;
}

/* CORREÇÃO: Z-index para legenda do mapa */
.map-legend {
    z-index: 1000 !important;
    position: relative;
}

/* Garante que a legenda fique acima dos controles do leaflet */
.leaflet-bottom.leaflet-right {
    z-index: 1000 !important;
}

/* Estilos para popups customizados */
.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001 !important;
}

.leaflet-popup-content {
    margin: 12px 16px;
    font-family: inherit;
}

.custom-popup .leaflet-popup-content-wrapper {
    border-radius: 12px;
    overflow: hidden;
}

.custom-popup .leaflet-popup-tip {
    border-top-color: white;
}

/* Animações suaves para marcadores */
.leaflet-marker-icon {
    transition: transform 0.2s ease;
}

.leaflet-marker-icon:hover {
    transform: scale(1.1);
    z-index: 1000;
}

/* Estilos para controles de zoom customizados */
.leaflet-control-zoom {
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-control-zoom a {
    background-color: white !important;
    color: #374151 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    border: none !important;
    transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
    background-color: #f3f4f6 !important;
    transform: scale(1.05);
}

/* Estilos para camadas de controle */
.leaflet-control-layers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    border-radius: 8px !important;
    border: none !important;
}

.leaflet-control-layers-toggle {
    background-color: white !important;
    border-radius: 8px !important;
}

/* Estilos para informações do mapa */
.map-info-box {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .leaflet-popup-content-wrapper {
        max-width: 280px !important;
    }
    
    .leaflet-control-zoom {
        margin-left: 5px !important;
        margin-top: 5px !important;
    }
    
    .leaflet-control-layers {
        margin-right: 5px !important;
        margin-top: 5px !important;
    }
    
    /* Ajusta o tamanho dos ícones em telas pequenas */
    .leaflet-marker-icon {
        transform: scale(0.8);
    }
    
    .leaflet-marker-icon:hover {
        transform: scale(0.9);
    }
}

/* Animações de entrada para legendas */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.legend-enter {
    animation: fadeInUp 0.3s ease-out;
}

/* Estilos para legenda compacta na sidebar */
.compact-legend {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
}

.compact-legend-item {
    transition: background-color 0.2s ease;
}

.compact-legend-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* Estilos para indicadores de carregamento */
.loading-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Melhora a visibilidade dos tooltips */
.tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
}

/* Ícones de marcadores CCV */
.ccv-marker-icon {
    background: none !important;
    border: none !important;
}

/* Popup customizado para CCV */
.ccv-popup .leaflet-popup-content-wrapper {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.ccv-popup .leaflet-popup-content {
    margin: 16px 20px;
    font-family: inherit;
    line-height: 1.4;
}

.ccv-popup .leaflet-popup-tip {
    border-top-color: white;
}

/* Legenda CCV */
.ccv-legend {
    z-index: 1000 !important;
    position: relative;
}

/* Animações para marcadores CCV */
.ccv-marker-icon {
    transition: transform 0.2s ease;
}

.ccv-marker-icon:hover {
    transform: scale(1.1);
    z-index: 1000;
}

/* Menu de produtos */
.product-menu {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Estados de loading para CCV */
.ccv-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsividade para CCV */
@media (max-width: 768px) {
    .ccv-legend {
        bottom: 80px !important;
        right: 10px !important;
        max-width: calc(100vw - 20px) !important;
    }
    
    .ccv-popup .leaflet-popup-content-wrapper {
        max-width: 280px !important;
    }
}