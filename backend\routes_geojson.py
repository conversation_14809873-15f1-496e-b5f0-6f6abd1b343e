# backend/routes_geojson.py

import os
import json
from flask import Blueprint, jsonify, request, current_app
from werkzeug.utils import secure_filename
from models import db, Projeto, CCVPonto, CCVDia, CCVMovimento, CCVContagem, Empresa

geojson_bp = Blueprint('geojson', __name__, url_prefix='/api/geojson')

@geojson_bp.route('/check_data/<int:project_id>', methods=['GET'])
def check_project_data(project_id):
    """Verifica se um projeto já possui pontos ou movimentos."""
    try:
        has_points = db.session.query(CCVPonto.id).filter_by(projeto_id=project_id).first() is not None
        return jsonify({'has_data': has_points})
    except Exception as e:
        current_app.logger.error(f"Erro ao verificar dados do projeto {project_id}: {e}")
        return jsonify({'error': str(e)}), 500

@geojson_bp.route('/upload', methods=['POST'])
def upload_geojson():
    """Recebe arquivos GeoJSON e os importa para um projeto em uma única transação."""
    try:
        if 'projeto_id' not in request.form:
            return jsonify({'error': 'ID do projeto é obrigatório'}), 400
        if 'pontos_file' not in request.files:
            return jsonify({'error': 'Arquivo GeoJSON de pontos é obrigatório'}), 400

        projeto_id = int(request.form['projeto_id'])
        pontos_file = request.files['pontos_file']
        movimentos_file = request.files.get('movimentos_file')
        overwrite = request.form.get('overwrite', 'false').lower() == 'true'

        if not Projeto.query.get(projeto_id):
            return jsonify({'error': 'Projeto não encontrado'}), 404

        if overwrite:
            current_app.logger.info(f"Sobrescrevendo dados para o projeto ID: {projeto_id}")
            
            # Deleta os dados na ordem correta para respeitar as chaves estrangeiras.
            ponto_ids_subquery = db.session.query(CCVPonto.id).filter_by(projeto_id=projeto_id).scalar_subquery()
            dia_ids_subquery = db.session.query(CCVDia.id).filter(CCVDia.ponto_id.in_(ponto_ids_subquery)).scalar_subquery()
            
            # Deleta primeiro a tabela mais "filha": CCVContagem.
            db.session.execute(
                db.delete(CCVContagem).where(CCVContagem.dia_id.in_(dia_ids_subquery))
            )

            # Deleta as tabelas "mães" da contagem: CCVDia e CCVMovimento.
            db.session.execute(
                db.delete(CCVDia).where(CCVDia.ponto_id.in_(ponto_ids_subquery))
            )
            db.session.execute(
                db.delete(CCVMovimento).where(CCVMovimento.ponto_id.in_(ponto_ids_subquery))
            )

            # Por último, deleta a tabela "avó": CCVPonto.
            db.session.execute(
                db.delete(CCVPonto).where(CCVPonto.projeto_id == projeto_id)
            )

        # --- LÓGICA DE INSERÇÃO ATÔMICA ---
        
        # 1. Importar Pontos para a sessão
        pontos_content = json.load(pontos_file.stream)
        pontos_importados = 0
        ponto_obj_map = {}  # Mapeia o código do ponto para o objeto do SQLAlchemy

        for feature in pontos_content.get('features', []):
            props = feature.get('properties', {})
            geom = feature.get('geometry', {})
            if not props or not geom or geom.get('type') != 'Point':
                continue
            
            nome_ponto = props.get('Name') or props.get('nome')
            coords = geom.get('coordinates')
            if not nome_ponto or not coords or len(coords) < 2:
                continue

            novo_ponto = CCVPonto(
                projeto_id=projeto_id,
                codigo_ponto=nome_ponto,
                nome_ponto=f"Ponto {nome_ponto}",
                longitude=coords[0],
                latitude=coords[1]
            )
            db.session.add(novo_ponto)
            ponto_obj_map[novo_ponto.codigo_ponto] = novo_ponto
            pontos_importados += 1

        # 2. Flush da sessão para gerar os IDs dos pontos no banco de dados, sem fechar a transação
        db.session.flush()

        # 3. Importar Movimentos/Aproximações para a sessão
        movimentos_importados = 0
        if movimentos_file and ponto_obj_map:
            movimentos_content = json.load(movimentos_file.stream)
            
            # Dicionário para agrupar aproximações por ponto
            aproximacoes_por_ponto = {}
            
            for feature in movimentos_content.get('features', []):
                props = feature.get('properties', {})
                geom = feature.get('geometry', {})

                if not props or not geom or geom.get('type') != 'Point':
                    continue
                
                codigo_ponto = props.get('ponto')
                aproximacao = props.get('aproximacao')
                coords = geom.get('coordinates')

                # Valida se os dados essenciais existem
                if not codigo_ponto or not aproximacao or not coords or len(coords) < 2:
                    continue
                
                # Verifica se o ponto existe no projeto
                ponto_obj = ponto_obj_map.get(codigo_ponto)
                if not ponto_obj:
                    continue
                
                # Agrupa por ponto para evitar duplicatas
                if codigo_ponto not in aproximacoes_por_ponto:
                    aproximacoes_por_ponto[codigo_ponto] = {}
                
                # Só adiciona se a aproximação ainda não foi processada para este ponto
                if aproximacao not in aproximacoes_por_ponto[codigo_ponto]:
                    aproximacoes_por_ponto[codigo_ponto][aproximacao] = {
                        'latitude': coords[1],
                        'longitude': coords[0],
                        'ponto_obj': ponto_obj
                    }
            
            # Cria os movimentos únicos por aproximação
            for codigo_ponto, aproximacoes in aproximacoes_por_ponto.items():
                for codigo_aproximacao, dados in aproximacoes.items():
                    novo_movimento = CCVMovimento(
                        ponto_id=dados['ponto_obj'].id,
                        codigo_movimento=codigo_aproximacao,  # A, B, C, etc.
                        nome_movimento=f"Aproximação {codigo_aproximacao}",
                        latitude=dados['latitude'],
                        longitude=dados['longitude'],
                        tipo_movimento='aproximacao'
                    )
                    
                    db.session.add(novo_movimento)
                    movimentos_importados += 1
        
        # 4. Commit final: Salva pontos e movimentos juntos na mesma transação
        db.session.commit()

        return jsonify({
            'message': 'Importação concluída com sucesso!',
            'pontos_importados': pontos_importados,
            'movimentos_importados': movimentos_importados,
            'detalhes': f'Foram importadas {movimentos_importados} aproximações únicas para {len(ponto_obj_map)} pontos.'
        })

    except Exception as e:
        db.session.rollback() # Desfaz a transação em caso de erro
        current_app.logger.error(f"Erro na importação GeoJSON: {e}", exc_info=True)
        error_message = str(getattr(e, 'orig', e))
        return jsonify({'error': f"Erro interno no servidor: {error_message}"}), 500