// frontend/src/components/ImportProgressModal.jsx - COMPONENTE OTIMIZADO

import React, { useState, useCallback, useRef } from 'react';
import { 
  analyzeExcelFile, 
  confirmClassificationAndImport, 
  validateFileBeforeUpload,
  formatFileSize,
  formatEstimatedTime,
  TIMEOUT_CONFIG,
  FILE_LIMITS 
} from '../api/ccvImportService';

const ImportProgressModal = ({ isOpen, onClose, projetoId, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState('file-selection');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [warnings, setWarnings] = useState([]);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [processingStats, setProcessingStats] = useState({});
  
  const fileInputRef = useRef(null);
  const startTimeRef = useRef(null);

  const resetState = useCallback(() => {
    setCurrentStep('file-selection');
    setProgress(0);
    setMessage('');
    setIsProcessing(false);
    setError(null);
    setWarnings([]);
    setAnalysisResult(null);
    setSelectedFile(null);
    setFileValidation(null);
    setEstimatedTime(null);
    setProcessingStats({});
    startTimeRef.current = null;
  }, []);

  const handleFileSelect = useCallback((event) => {
    const file = event.target.files[0];
    if (!file) return;

    setSelectedFile(file);
    setError(null);
    setWarnings([]);

    // Validar arquivo
    const validation = validateFileBeforeUpload(file);
    setFileValidation(validation);

    if (validation.errors.length > 0) {
      setError(validation.errors.join('; '));
      return;
    }

    if (validation.warnings.length > 0) {
      setWarnings(validation.warnings);
    }

    // Mostrar estimativa de tempo
    if (validation.estimatedTime > 0) {
      setEstimatedTime(validation.estimatedTime);
    }
  }, []);

  const handleProgressUpdate = useCallback((progressData) => {
    setProgress(progressData.progress || 0);
    setMessage(progressData.message || '');
    
    if (progressData.step) {
      setCurrentStep(progressData.step);
    }

    if (progressData.estimatedTime) {
      setEstimatedTime(progressData.estimatedTime);
    }

    if (progressData.warnings) {
      setWarnings(prev => [...prev, ...progressData.warnings]);
    }

    if (progressData.details) {
      setProcessingStats(progressData.details);
    }
  }, []);

  const handleAnalyzeFile = async () => {
    if (!selectedFile || !projetoId) return;

    setIsProcessing(true);
    setError(null);
    setCurrentStep('analysis');
    startTimeRef.current = Date.now();

    try {
      const formData = new FormData();
      formData.append('contagem_file', selectedFile);
      formData.append('projeto_id', projetoId.toString());

      const result = await analyzeExcelFile(formData, handleProgressUpdate);
      
      setAnalysisResult(result);
      setCurrentStep('classification');
      
      // Mostrar estatísticas da análise
      if (result.estatisticas) {
        setMessage(`Análise concluída: ${result.estatisticas.total_registros?.toLocaleString()} registros, ${result.estatisticas.tipos_novos} tipos novos`);
      }

    } catch (err) {
      setError(err.message);
      setCurrentStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirmImport = async (classificationData) => {
    if (!analysisResult || !selectedFile) return;

    setIsProcessing(true);
    setError(null);
    setCurrentStep('import');
    setProgress(0);

    try {
      // Preparar dados para importação
      const fileReader = new FileReader();
      
      const fileBase64 = await new Promise((resolve, reject) => {
        fileReader.onload = () => {
          const result = fileReader.result;
          const base64 = result.split(',')[1]; // Remover prefixo data:...base64,
          resolve(base64);
        };
        fileReader.onerror = reject;
        fileReader.readAsDataURL(selectedFile);
      });

      const importData = {
        projeto_id: projetoId,
        tipos_classificados: classificationData,
        arquivo_excel_base64: fileBase64
      };

      const result = await confirmClassificationAndImport(importData, handleProgressUpdate);

      setCurrentStep('completed');
      setProcessingStats(result.detalhes || {});
      setMessage('Importação concluída com sucesso!');

      // Aguardar um pouco antes de fechar
      setTimeout(() => {
        onSuccess(result);
        onClose();
        resetState();
      }, 2000);

    } catch (err) {
      setError(err.message);
      setCurrentStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStepIcon = (step) => {
    const icons = {
      'file-selection': 'fa-file-upload',
      'analysis': 'fa-search',
      'classification': 'fa-tags',
      'import': 'fa-database',
      'completed': 'fa-check-circle',
      'error': 'fa-exclamation-triangle'
    };
    return icons[step] || 'fa-circle';
  };

  const getStepColor = (step) => {
    const colors = {
      'file-selection': 'text-blue-600',
      'analysis': 'text-purple-600',
      'classification': 'text-yellow-600',
      'import': 'text-orange-600',
      'completed': 'text-green-600',
      'error': 'text-red-600'
    };
    return colors[step] || 'text-gray-600';
  };

  const calculateElapsedTime = () => {
    if (!startTimeRef.current) return 0;
    return Math.round((Date.now() - startTimeRef.current) / 1000);
  };

  const calculateSpeed = () => {
    if (!processingStats.registros_adicionados || !startTimeRef.current) return 0;
    const elapsedSeconds = (Date.now() - startTimeRef.current) / 1000;
    return Math.round(processingStats.registros_adicionados / elapsedSeconds);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Importação Otimizada de Dados CCV
          </h2>
          <button
            onClick={() => {
              if (!isProcessing) {
                onClose();
                resetState();
              }
            }}
            disabled={isProcessing}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <i className="fas fa-times text-xl"></i>
          </button>
        </div>

        {/* Progress Steps */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            {['file-selection', 'analysis', 'classification', 'import', 'completed'].map((step, index) => (
              <div key={step} className="flex items-center">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center border-2
                  ${currentStep === step ? 'border-blue-600 bg-blue-600 text-white' :
                    ['completed', 'error'].includes(currentStep) && index < 4 ? 'border-green-600 bg-green-600 text-white' :
                    'border-gray-300 bg-white text-gray-600'}
                `}>
                  <i className={`fas ${getStepIcon(step)} text-sm`}></i>
                </div>
                {index < 4 && (
                  <div className={`
                    h-1 w-16 mx-2
                    ${['completed', 'error'].includes(currentStep) && index < 3 ? 'bg-green-600' :
                      currentStep === ['analysis', 'classification', 'import', 'completed'][index] ? 'bg-blue-600' :
                      'bg-gray-300'}
                  `}></div>
                )}
              </div>
            ))}
          </div>
          
          {/* Step Labels */}
          <div className="flex justify-between mt-2 text-xs text-gray-600">
            <span>Seleção</span>
            <span>Análise</span>
            <span>Classificação</span>
            <span>Importação</span>
            <span>Concluído</span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          
          {/* File Selection Step */}
          {currentStep === 'file-selection' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 hover:border-blue-500 transition-colors">
                  <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Selecione o arquivo CCV
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Arquivo Excel (.xlsx) ou CSV com dados de contagem
                  </p>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <i className="fas fa-folder-open mr-2"></i>
                    Escolher Arquivo
                  </button>
                </div>
              </div>

              {/* File Info */}
              {selectedFile && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-900">{selectedFile.name}</h4>
                      <p className="text-blue-700 text-sm">
                        {formatFileSize(selectedFile.size)}
                        {fileValidation?.estimatedRows && 
                          ` • ≈${(fileValidation.estimatedRows / 1000).toFixed(0)}k registros`
                        }
                        {estimatedTime && 
                          ` • Tempo estimado: ${formatEstimatedTime(estimatedTime)}`
                        }
                      </p>
                    </div>
                    <button
                      onClick={handleAnalyzeFile}
                      disabled={fileValidation?.errors?.length > 0}
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Analisar
                    </button>
                  </div>
                </div>
              )}

              {/* Warnings */}
              {warnings.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-800 mb-2">
                    <i className="fas fa-exclamation-triangle mr-2"></i>
                    Avisos
                  </h4>
                  <ul className="text-yellow-700 text-sm space-y-1">
                    {warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* File Limits Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-800 mb-2">Limites do Sistema</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div>
                    <strong>Tamanho máximo:</strong><br/>
                    {formatFileSize(FILE_LIMITS.maxSize)}
                  </div>
                  <div>
                    <strong>Registros máximos:</strong><br/>
                    {(FILE_LIMITS.maxRows / 1000).toFixed(0)}k linhas
                  </div>
                  <div>
                    <strong>Timeout máximo:</strong><br/>
                    {Math.round(TIMEOUT_CONFIG.import / 60000)} minutos
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Processing Steps */}
          {['analysis', 'classification', 'import'].includes(currentStep) && (
            <div className="space-y-6">
              
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className={`font-medium ${getStepColor(currentStep)}`}>
                    <i className={`fas ${getStepIcon(currentStep)} mr-2`}></i>
                    {message || 'Processando...'}
                  </span>
                  <span className="text-sm text-gray-600">
                    {progress.toFixed(1)}%
                  </span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      currentStep === 'error' ? 'bg-red-500' : 'bg-blue-600'
                    }`}
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              {/* Processing Stats */}
              {isProcessing && startTimeRef.current && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-50 p-3 rounded text-center">
                    <div className="text-lg font-bold text-gray-800">
                      {calculateElapsedTime()}s
                    </div>
                    <div className="text-sm text-gray-600">Tempo decorrido</div>
                  </div>
                  
                  {processingStats.registros_adicionados && (
                    <div className="bg-green-50 p-3 rounded text-center">
                      <div className="text-lg font-bold text-green-800">
                        {processingStats.registros_adicionados.toLocaleString()}
                      </div>
                      <div className="text-sm text-green-600">Registros</div>
                    </div>
                  )}
                  
                  {calculateSpeed() > 0 && (
                    <div className="bg-blue-50 p-3 rounded text-center">
                      <div className="text-lg font-bold text-blue-800">
                        {calculateSpeed()}/s
                      </div>
                      <div className="text-sm text-blue-600">Velocidade</div>
                    </div>
                  )}
                  
                  {estimatedTime && (
                    <div className="bg-purple-50 p-3 rounded text-center">
                      <div className="text-lg font-bold text-purple-800">
                        {formatEstimatedTime(estimatedTime)}
                      </div>
                      <div className="text-sm text-purple-600">Estimativa</div>
                    </div>
                  )}
                </div>
              )}

              {/* Classification Interface */}
              {currentStep === 'classification' && analysisResult && (
                <ClassificationInterface
                  analysisResult={analysisResult}
                  onConfirm={handleConfirmImport}
                  isProcessing={isProcessing}
                />
              )}
            </div>
          )}

          {/* Completed Step */}
          {currentStep === 'completed' && (
            <div className="text-center space-y-4">
              <div className="text-green-600 text-6xl mb-4">
                <i className="fas fa-check-circle"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                Importação Concluída!
              </h3>
              <p className="text-gray-600">{message}</p>
              
              {processingStats && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800 mb-2">Resultados da Importação</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    {Object.entries(processingStats).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-green-700">
                          {typeof value === 'number' ? value.toLocaleString() : value}
                        </div>
                        <div className="text-green-600 capitalize">
                          {key.replace(/_/g, ' ')}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Error Step */}
          {currentStep === 'error' && (
            <div className="text-center space-y-4">
              <div className="text-red-600 text-6xl mb-4">
                <i className="fas fa-exclamation-triangle"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                Erro na Importação
              </h3>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700">{error}</p>
              </div>
              
              {/* Sugestões de solução */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                <h4 className="font-medium text-blue-800 mb-2">
                  <i className="fas fa-lightbulb mr-2"></i>
                  Sugestões para resolver o problema:
                </h4>
                <ul className="text-blue-700 text-sm space-y-1">
                  {error?.includes('timeout') || error?.includes('Timeout') ? (
                    <>
                      <li>• Divida o arquivo em partes menores (máx. {FILE_LIMITS.maxRows / 1000}k registros)</li>
                      <li>• Verifique sua conexão com a internet</li>
                      <li>• Tente importar em horários de menor movimento</li>
                      <li>• Remova colunas desnecessárias do arquivo</li>
                    </>
                  ) : error?.includes('grande') || error?.includes('size') ? (
                    <>
                      <li>• Reduza o tamanho do arquivo (máx. {formatFileSize(FILE_LIMITS.maxSize)})</li>
                      <li>• Comprima o arquivo Excel antes do upload</li>
                      <li>• Divida em múltiplos arquivos menores</li>
                    </>
                  ) : (
                    <>
                      <li>• Verifique o formato do arquivo (Excel .xlsx recomendado)</li>
                      <li>• Confirme se as colunas obrigatórias existem</li>
                      <li>• Remova caracteres especiais dos dados</li>
                      <li>• Tente novamente em alguns minutos</li>
                    </>
                  )}
                </ul>
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => {
                    setCurrentStep('file-selection');
                    setError(null);
                    setProgress(0);
                  }}
                  className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
                >
                  <i className="fas fa-redo mr-2"></i>
                  Tentar Novamente
                </button>
                <button
                  onClick={() => {
                    onClose();
                    resetState();
                  }}
                  className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
                >
                  Fechar
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer com informações do sistema */}
        <div className="border-t bg-gray-50 px-6 py-3">
          <div className="flex justify-between items-center text-xs text-gray-500">
            <div>
              Sistema CCV v2.0 • Otimizado para arquivos grandes
            </div>
            <div className="flex items-center space-x-4">
              <span>Máx: {formatFileSize(FILE_LIMITS.maxSize)}</span>
              <span>Timeout: {Math.round(TIMEOUT_CONFIG.import / 60000)}min</span>
              {selectedFile && (
                <span>Arquivo: {formatFileSize(selectedFile.size)}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente para interface de classificação de tipos
const ClassificationInterface = ({ analysisResult, onConfirm, isProcessing }) => {
  const [typesClassification, setTypesClassification] = useState(
    analysisResult.tipos_para_classificar.map(tipo => ({
      ...tipo,
      criar_novo: true,
      categoria_id: tipo.categoria_sugerida_id,
      codigo: tipo.codigo_sugerido,
      nome: tipo.nome_sugerido,
      equivalente_auto: tipo.equivalente_auto_sugerido,
      cor_padrao: '#95a5a6',
      grupos_ids: []
    }))
  );

  const handleTypeUpdate = (index, field, value) => {
    setTypesClassification(prev => 
      prev.map((type, i) => 
        i === index ? { ...type, [field]: value } : type
      )
    );
  };

  const handleConfirmAll = () => {
    onConfirm(typesClassification);
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 mb-2">
          <i className="fas fa-tags mr-2"></i>
          Classificação de Tipos de Veículos
        </h3>
        <p className="text-blue-700 text-sm">
          {analysisResult.tipos_para_classificar.length} tipos novos encontrados. 
          Revise as classificações sugeridas antes de prosseguir.
        </p>
      </div>

      {/* Estatísticas da análise */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gray-50 p-3 rounded text-center">
          <div className="text-lg font-bold text-gray-800">
            {analysisResult.estatisticas.total_registros?.toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Total Registros</div>
        </div>
        <div className="bg-green-50 p-3 rounded text-center">
          <div className="text-lg font-bold text-green-800">
            {analysisResult.tipos_ja_existentes.length}
          </div>
          <div className="text-sm text-green-600">Tipos Existentes</div>
        </div>
        <div className="bg-yellow-50 p-3 rounded text-center">
          <div className="text-lg font-bold text-yellow-800">
            {analysisResult.tipos_para_classificar.length}
          </div>
          <div className="text-sm text-yellow-600">Tipos Novos</div>
        </div>
        <div className="bg-purple-50 p-3 rounded text-center">
          <div className="text-lg font-bold text-purple-800">
            {analysisResult.estatisticas.tempo_analise}s
          </div>
          <div className="text-sm text-purple-600">Tempo Análise</div>
        </div>
      </div>

      {/* Lista de tipos para classificar */}
      {typesClassification.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-800">Tipos a Classificar:</h4>
          
          <div className="max-h-96 overflow-y-auto border rounded-lg">
            <table className="min-w-full">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                    Tipo do Excel
                  </th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                    Código
                  </th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                    Nome
                  </th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                    Categoria
                  </th>
                  <th className="px-4 py-2 text-center text-sm font-medium text-gray-700">
                    Equiv. Auto
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {typesClassification.map((tipo, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-4 py-2 text-sm font-medium text-gray-900">
                      {tipo.nome_excel}
                    </td>
                    <td className="px-4 py-2">
                      <input
                        type="text"
                        value={tipo.codigo}
                        onChange={(e) => handleTypeUpdate(index, 'codigo', e.target.value)}
                        className="w-full px-2 py-1 text-sm border rounded"
                      />
                    </td>
                    <td className="px-4 py-2">
                      <input
                        type="text"
                        value={tipo.nome}
                        onChange={(e) => handleTypeUpdate(index, 'nome', e.target.value)}
                        className="w-full px-2 py-1 text-sm border rounded"
                      />
                    </td>
                    <td className="px-4 py-2">
                      <select
                        value={tipo.categoria_id}
                        onChange={(e) => handleTypeUpdate(index, 'categoria_id', parseInt(e.target.value))}
                        className="w-full px-2 py-1 text-sm border rounded"
                      >
                        {analysisResult.categorias_disponiveis.map(cat => (
                          <option key={cat.id} value={cat.id}>
                            {cat.nome}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-4 py-2 text-center">
                      <input
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        value={tipo.equivalente_auto}
                        onChange={(e) => handleTypeUpdate(index, 'equivalente_auto', parseFloat(e.target.value))}
                        className="w-16 px-2 py-1 text-sm border rounded text-center"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Tipos já existentes */}
      {analysisResult.tipos_ja_existentes.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-semibold text-gray-800">Tipos Já Existentes:</h4>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              {analysisResult.tipos_ja_existentes.slice(0, 10).map((tipo, index) => (
                <div key={index} className="flex items-center text-green-700">
                  <i className="fas fa-check-circle mr-2 text-green-600"></i>
                  <span className="font-medium">{tipo.codigo}</span>
                  <span className="ml-2 text-green-600">({tipo.categoria_nome})</span>
                </div>
              ))}
              {analysisResult.tipos_ja_existentes.length > 10 && (
                <div className="text-green-600 italic">
                  ... e mais {analysisResult.tipos_ja_existentes.length - 10} tipos
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Botões de ação */}
      <div className="flex justify-end space-x-4 pt-4 border-t">
        <button
          onClick={() => setTypesClassification(prev => 
            prev.map(type => ({
              ...type,
              categoria_id: analysisResult.categorias_disponiveis.find(c => c.codigo === 'ESPECIAL')?.id || type.categoria_id
            }))
          )}
          className="text-gray-600 hover:text-gray-800"
        >
          Marcar Todos como Especial
        </button>
        
        <button
          onClick={handleConfirmAll}
          disabled={isProcessing || typesClassification.some(t => !t.codigo || !t.nome)}
          className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <i className="fas fa-spinner fa-spin mr-2"></i>
              Importando...
            </>
          ) : (
            <>
              <i className="fas fa-check mr-2"></i>
              Confirmar e Importar
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ImportProgressModal;