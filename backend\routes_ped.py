# backend/routes_ped.py

from flask import Blueprint, jsonify, request, current_app
from app import db
from models import Empresa, Projeto, Ped, PedDadosHorarios, PedDia
import pandas as pd
from datetime import datetime
import numpy as np

ped_bp = Blueprint('ped', __name__, url_prefix='/api/ped')

@ped_bp.route('/validate/<int:projeto_id>', methods=['GET'])
def validate_ped_project(projeto_id):
    """Valida se um projeto existe e pode receber dados PED"""
    try:
        projeto = Projeto.query.get(projeto_id)
        if not projeto:
            return jsonify({'error': 'Projeto não encontrado'}), 404
        
        # Conta PEDs existentes
        peds_count = Ped.query.filter_by(projeto_id=projeto_id).count()
        
        return jsonify({
            'projeto': {
                'id': projeto.id,
                'nome': projeto.nome_projeto,
                'codigo': projeto.codigo_projeto
            },
            'peds_existentes': peds_count,
            'pode_importar': True
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ped_bp.route('/stats/<int:projeto_id>', methods=['GET'])
def get_ped_stats(projeto_id):
    """Retorna estatísticas dos dados PED de um projeto"""
    try:
        peds = Ped.query.filter_by(projeto_id=projeto_id).all()
        
        total_peds = len(peds)
        total_dados_horarios = sum(len(ped.dados_horarios) for ped in peds)
        total_dias = PedDia.query.join(Ped).filter(Ped.projeto_id == projeto_id).count()
        
        return jsonify({
            'total_peds': total_peds,
            'total_dados_horarios': total_dados_horarios,
            'total_dias': total_dias,
            'peds': [{'nome': ped.nome_ped, 'total_horarios': len(ped.dados_horarios)} for ped in peds]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ped_bp.route('/import-dados', methods=['POST'])
def import_ped_dados():
    """Importa dados PED de um arquivo Excel"""
    if 'projeto_id' not in request.form:
        return jsonify({'error': 'ID do projeto é obrigatório'}), 400
    if 'dados_file' not in request.files:
        return jsonify({'error': 'Arquivo de dados (Excel) é obrigatório'}), 400

    projeto_id = int(request.form['projeto_id'])
    dados_file = request.files['dados_file']
    
    try:
        # Valida o projeto
        projeto = Projeto.query.get(projeto_id)
        if not projeto:
            return jsonify({'error': 'Projeto não encontrado'}), 404

        # Lê o arquivo Excel
        try:
            df = pd.read_excel(dados_file.stream, engine='openpyxl')
        except Exception as e:
            current_app.logger.error(f"Falha ao ler o arquivo Excel: {e}", exc_info=True)
            return jsonify({'error': 'Não foi possível ler o arquivo. Verifique se é um formato Excel (.xlsx) válido.'}), 400

        # Normaliza nomes das colunas
        df.columns = [str(col).lower().strip().replace(' ', '_') for col in df.columns]
        
        # Define mapeamento de colunas (flexível para diferentes formatos)
        column_mapping = {
            # Campos obrigatórios
            'nome_ped': ['nome_ped', 'nome_do_ped', 'ped', 'ponto', 'nome_ponto'],
            'hora': ['hora', 'horario', 'time'],
            'embarque': ['embarque', 'embarques', 'emb'],
            'desembarque': ['desembarque', 'desembarques', 'desemb', 'des'],
            'onibus': ['onibus', 'ônibus', 'bus', 'veiculos'],
            'travessia_1_2': ['1_-_2', '1-2', 'travessia_1_2', 'trav_1_2'],
            'travessia_2_1': ['2_-_1', '2-1', 'travessia_2_1', 'trav_2_1'],
            
            # Campos opcionais
            'data': ['data', 'date', 'dia'],
            'intervalo': ['intervalo', 'periodo', 'duracao'],
            'total': ['total', 'total_passageiros', 'soma'],
            'latitude': ['latitude', 'lat'],
            'longitude': ['longitude', 'lng', 'long'],
            'km': ['km', 'quilometro', 'marco'],
            'sh': ['sh', 'segmento_homogeneo', 'segmento'],
            'sentido': ['sentido', 'direcao', 'lado'],
            'placa': ['placa', 'sinalizada'],
            'abrigo': ['abrigo', 'cobertura'],
            'baia': ['baia', 'acostamento'],
            'mancha_urbana': ['mancha_urbana', 'urbana', 'cidade']
        }
        
        # Encontra as colunas no DataFrame
        found_columns = {}
        missing_required = []
        
        for field_name, possible_names in column_mapping.items():
            found = False
            for possible_name in possible_names:
                if possible_name in df.columns:
                    found_columns[field_name] = possible_name
                    found = True
                    break
            
            # Verifica campos obrigatórios
            if not found and field_name in ['nome_ped', 'hora', 'embarque', 'desembarque', 'onibus']:
                missing_required.append(field_name)
        
        if missing_required:
            return jsonify({
                'error': f"Colunas obrigatórias não encontradas: {', '.join(missing_required)}",
                'colunas_disponiveis': list(df.columns),
                'colunas_obrigatorias': ['nome_ped', 'hora', 'embarque', 'desembarque', 'onibus']
            }), 400

        # Processa os dados
        registros_adicionados = 0
        registros_atualizados = 0
        erros_de_linha = []
        peds_criados = {}
        
        for index, row in df.iterrows():
            try:
                # Extrai dados da linha
                nome_ped = str(row[found_columns['nome_ped']]).strip()
                if not nome_ped or nome_ped.lower() in ['nan', 'none', '']:
                    erros_de_linha.append(f"Linha {index + 2}: Nome do PED vazio ou inválido")
                    continue
                
                # Busca ou cria o PED
                if nome_ped not in peds_criados:
                    ped = Ped.query.filter_by(projeto_id=projeto_id, nome_ped=nome_ped).first()
                    if not ped:
                        ped = Ped(
                            projeto_id=projeto_id,
                            nome_ped=nome_ped,
                            codigo_ponto=nome_ped,  # Use nome como código se não especificado
                            # Campos opcionais com fallback
                            latitude=row.get(found_columns.get('latitude')) if 'latitude' in found_columns else None,
                            longitude=row.get(found_columns.get('longitude')) if 'longitude' in found_columns else None,
                            km=str(row.get(found_columns.get('km', ''), '')).strip() if 'km' in found_columns else None,
                            sh=str(row.get(found_columns.get('sh', ''), '')).strip() if 'sh' in found_columns else None,
                            sentido=str(row.get(found_columns.get('sentido', ''), '')).strip() if 'sentido' in found_columns else None,
                            placa=str(row.get(found_columns.get('placa', ''), '')).strip() if 'placa' in found_columns else None,
                            abrigo=str(row.get(found_columns.get('abrigo', ''), '')).strip() if 'abrigo' in found_columns else None,
                            baia=str(row.get(found_columns.get('baia', ''), '')).strip() if 'baia' in found_columns else None,
                            mancha_urbana=str(row.get(found_columns.get('mancha_urbana', ''), '')).strip() if 'mancha_urbana' in found_columns else None
                        )
                        db.session.add(ped)
                        db.session.flush()  # Para obter o ID
                    peds_criados[nome_ped] = ped
                else:
                    ped = peds_criados[nome_ped]
                
                # Processa dados horários
                hora_value = row[found_columns['hora']]
                if pd.isna(hora_value):
                    erros_de_linha.append(f"Linha {index + 2}: Hora vazia")
                    continue
                
                # Converte hora para string
                if isinstance(hora_value, str):
                    hora_str = hora_value.strip()
                else:
                    # Se for datetime ou time, converte para string
                    try:
                        if hasattr(hora_value, 'strftime'):
                            hora_str = hora_value.strftime('%H:%M')
                        else:
                            hora_str = str(hora_value)
                    except:
                        hora_str = str(hora_value)
                
                # Extrai valores numéricos
                def safe_int(value):
                    if pd.isna(value):
                        return 0
                    try:
                        return int(float(value))
                    except:
                        return 0
                
                embarque = safe_int(row[found_columns['embarque']])
                desembarque = safe_int(row[found_columns['desembarque']])
                onibus = safe_int(row[found_columns['onibus']])
                
                # Campos opcionais
                total = embarque + desembarque  # Calcula se não fornecido
                if 'total' in found_columns:
                    total_fornecido = safe_int(row[found_columns['total']])
                    if total_fornecido > 0:
                        total = total_fornecido
                
                travessia_1_2 = safe_int(row[found_columns.get('travessia_1_2', None)]) if 'travessia_1_2' in found_columns else 0
                travessia_2_1 = safe_int(row[found_columns.get('travessia_2_1', None)]) if 'travessia_2_1' in found_columns else 0
                intervalo = safe_int(row[found_columns.get('intervalo', None)]) if 'intervalo' in found_columns else 15
                
                # Verifica se já existe um registro para este PED e hora
                existing = PedDadosHorarios.query.filter_by(
                    ped_id=ped.id,
                    hora=hora_str
                ).first()
                
                if existing:
                    # Atualiza registro existente
                    existing.embarque = embarque
                    existing.desembarque = desembarque
                    existing.total = total
                    existing.onibus = onibus
                    existing.travessia_1_2 = travessia_1_2
                    existing.travessia_2_1 = travessia_2_1
                    existing.intervalo = intervalo
                    registros_atualizados += 1
                else:
                    # Cria novo registro
                    dados_horarios = PedDadosHorarios(
                        ped_id=ped.id,
                        hora=hora_str,
                        intervalo=intervalo,
                        embarque=embarque,
                        desembarque=desembarque,
                        total=total,
                        onibus=onibus,
                        travessia_1_2=travessia_1_2,
                        travessia_2_1=travessia_2_1
                    )
                    db.session.add(dados_horarios)
                    registros_adicionados += 1
                
            except Exception as e:
                erros_de_linha.append(f"Linha {index + 2}: Erro ao processar - {str(e)}")
        
        # Verifica se houve erros críticos
        if len(erros_de_linha) > len(df) * 0.5:  # Se mais de 50% das linhas tiveram erro
            db.session.rollback()
            return jsonify({
                'error': 'Muitos erros encontrados durante a importação.',
                'details': erros_de_linha[:10],  # Mostra apenas os primeiros 10 erros
                'total_erros': len(erros_de_linha)
            }), 400
        
        # Commit das alterações
        db.session.commit()
        
        return jsonify({
            'message': f'Importação PED concluída com sucesso!',
            'registros_adicionados': registros_adicionados,
            'registros_atualizados': registros_atualizados,
            'peds_processados': len(peds_criados),
            'erros_ignorados': len(erros_de_linha),
            'detalhes': {
                'peds': list(peds_criados.keys()),
                'colunas_utilizadas': found_columns
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erro crítico na importação PED: {e}", exc_info=True)
        return jsonify({'error': f'Erro crítico no servidor: {str(e)}'}), 500