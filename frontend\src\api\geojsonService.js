// frontend/src/api/geojsonService.js

import axios from 'axios';

const API = axios.create({ 
  baseURL: API_BASE_URL,
  timeout: 30000, // Timeout maior para uploads
});

// Interceptor de erros para logging
API.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('❌ GeoJSON API Error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      responseData: error.response?.data
    });
    return Promise.reject(error.response?.data || error);
  }
);

/**
 * Busca todos os projetos de uma empresa. Reutiliza a rota do CCV.
 * @param {number} empresaId - O ID da empresa.
 * @returns {Promise<Array>} - Uma lista de projetos.
 */
export async function fetchProjetos(empresaId) {
    try {
        const response = await API.get('/api/ccv/projetos', { params: { empresa_id: empresaId } });
        return response.data.projetos || [];
    } catch (error) {
        console.error("Erro ao buscar projetos:", error);
        throw error;
    }
}

/**
 * Cria um novo projeto.
 * @param {object} projetoData - Dados do novo projeto { nome_projeto, codigo_projeto, empresa_id }.
 * @returns {Promise<object>} - O projeto criado.
 */
export async function createProjeto(projetoData) {
    try {
        const response = await API.post('/api/projetos', projetoData);
        return response.data;
    } catch (error) {
        console.error("Erro ao criar projeto:", error);
        throw error;
    }
}

/**
 * Verifica se um projeto já contém dados.
 * @param {number} projetoId - O ID do projeto.
 * @returns {Promise<boolean>} - True se o projeto tiver dados.
 */
export async function checkProjectData(projetoId) {
    try {
        const response = await API.get(`/api/geojson/check_data/${projetoId}`);
        return response.data.has_data;
    } catch (error) {
        console.error("Erro ao verificar dados do projeto:", error);
        throw error;
    }
}


/**
 * Faz o upload dos arquivos GeoJSON.
 * @param {object} data - Contém { projetoId, pontosFile, movimentosFile, overwrite }.
 * @returns {Promise<object>} - A resposta do servidor.
 */
export async function uploadGeojson({ projetoId, pontosFile, movimentosFile, overwrite }) {
    const formData = new FormData();
    formData.append('projeto_id', projetoId);
    formData.append('pontos_file', pontosFile);
    if (movimentosFile) {
        formData.append('movimentos_file', movimentosFile);
    }
    formData.append('overwrite', overwrite);

    try {
        const response = await API.post('/api/geojson/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error("Erro no upload do GeoJSON:", error);
        throw error;
    }
}